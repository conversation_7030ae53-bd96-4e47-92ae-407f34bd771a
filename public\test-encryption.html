<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .warning {
            color: #ffc107;
        }
        .info {
            color: #17a2b8;
        }
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .status-item {
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .encrypted {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .plain {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 加密功能测试页面</h1>
        <p>此页面用于测试和验证安全存储的加密功能。</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>1. 当前存储状态检查</h3>
            <button onclick="checkCurrentStorage()">检查当前存储状态</button>
            <div id="storageStatus" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 强制加密测试</h3>
            <button onclick="testEncryption()">测试加密功能</button>
            <button onclick="forceEncryptExisting()">强制加密现有数据</button>
            <div id="encryptionResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 解密测试</h3>
            <button onclick="testDecryption()">测试解密功能</button>
            <div id="decryptionResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 完整流程测试</h3>
            <button onclick="testFullFlow()">完整加密/解密流程测试</button>
            <div id="fullFlowResult" class="result"></div>
        </div>
    </div>

    <script>
        // 模拟加密类（简化版本，与实际代码保持一致）
        class TestSecureStorageEncryption {
            static SENSITIVE_KEYS = ['access_token', 'refresh_token', 'user_id'];
            static encryptionKey = null;

            static generateDeviceKey() {
                if (this.encryptionKey) {
                    return this.encryptionKey;
                }

                try {
                    const fingerprint = [
                        navigator.userAgent,
                        navigator.language,
                        `${screen.width}x${screen.height}`,
                        new Date().getTimezoneOffset().toString(),
                        'autofill-secure-key-v1'
                    ].join('|');
                    
                    this.encryptionKey = btoa(fingerprint).replace(/[^a-zA-Z0-9]/g, '').slice(0, 32).padEnd(32, 'A');
                    return this.encryptionKey;
                } catch (error) {
                    console.warn('生成设备密钥失败，使用默认密钥:', error);
                    this.encryptionKey = 'AutoFillSecureStorageDefaultKey32';
                    return this.encryptionKey;
                }
            }

            static isSensitive(key) {
                return this.SENSITIVE_KEYS.includes(key);
            }

            static encrypt(data) {
                if (!data) return data;
                
                try {
                    const key = this.generateDeviceKey();
                    
                    const encrypted = data.split('').map((char, i) => {
                        const keyChar = key.charCodeAt(i % key.length);
                        const dataChar = char.charCodeAt(0);
                        return String.fromCharCode(dataChar ^ keyChar);
                    }).join('');
                    
                    return 'ENCRYPTED:' + btoa(encrypted);
                } catch (error) {
                    console.error('加密失败:', error);
                    return data;
                }
            }

            static decrypt(encryptedData) {
                if (!encryptedData || !encryptedData.startsWith('ENCRYPTED:')) {
                    return encryptedData;
                }
                
                try {
                    const key = this.generateDeviceKey();
                    
                    const base64Data = encryptedData.replace('ENCRYPTED:', '');
                    const encrypted = atob(base64Data);
                    
                    const decrypted = encrypted.split('').map((char, i) => {
                        const keyChar = key.charCodeAt(i % key.length);
                        const encryptedChar = char.charCodeAt(0);
                        return String.fromCharCode(encryptedChar ^ keyChar);
                    }).join('');
                    
                    return decrypted;
                } catch (error) {
                    console.error('解密失败:', error);
                    return encryptedData;
                }
            }
        }

        // 检查当前存储状态
        function checkCurrentStorage() {
            const resultDiv = document.getElementById('storageStatus');
            const sensitiveKeys = ['access_token', 'refresh_token', 'user_id'];
            const otherKeys = ['user_email', 'subscription_info', 'token_type'];
            
            let result = '<span class="info">📊 当前存储状态:</span>\n\n';
            
            // 检查敏感数据
            result += '<span class="info">敏感数据:</span>\n';
            sensitiveKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    const isEncrypted = value.startsWith('ENCRYPTED:');
                    const status = isEncrypted ? '🔒 已加密' : '⚠️ 明文';
                    const className = isEncrypted ? 'success' : 'error';
                    result += `<span class="${className}">${key}: ${status}</span>\n`;
                    result += `  值: ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}\n`;
                } else {
                    result += `<span class="warning">${key}: 无数据</span>\n`;
                }
            });
            
            // 检查非敏感数据
            result += '\n<span class="info">非敏感数据:</span>\n';
            otherKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    result += `<span class="info">${key}: 存在</span>\n`;
                    result += `  值: ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}\n`;
                } else {
                    result += `<span class="warning">${key}: 无数据</span>\n`;
                }
            });
            
            resultDiv.innerHTML = result;
        }

        // 测试加密功能
        function testEncryption() {
            const resultDiv = document.getElementById('encryptionResult');
            
            try {
                const testData = {
                    access_token: 'test_token_123456789',
                    refresh_token: 'test_refresh_987654321',
                    user_id: 'test_user_456'
                };
                
                let result = '<span class="info">🔐 加密测试结果:</span>\n\n';
                
                Object.entries(testData).forEach(([key, value]) => {
                    const encrypted = TestSecureStorageEncryption.encrypt(value);
                    const isEncrypted = encrypted.startsWith('ENCRYPTED:');
                    
                    result += `<span class="info">${key}:</span>\n`;
                    result += `  原始: ${value}\n`;
                    result += `  加密: ${encrypted}\n`;
                    result += `  状态: <span class="${isEncrypted ? 'success' : 'error'}">${isEncrypted ? '✅ 加密成功' : '❌ 加密失败'}</span>\n\n`;
                });
                
                resultDiv.innerHTML = result;
            } catch (error) {
                resultDiv.innerHTML = '<span class="error">❌ 加密测试失败: ' + error.message + '</span>';
            }
        }

        // 强制加密现有数据
        function forceEncryptExisting() {
            const resultDiv = document.getElementById('encryptionResult');
            
            try {
                const sensitiveKeys = ['access_token', 'refresh_token', 'user_id'];
                let result = '<span class="info">🔄 强制加密现有数据:</span>\n\n';
                
                sensitiveKeys.forEach(key => {
                    const value = localStorage.getItem(key);
                    if (value) {
                        let plainValue = value;
                        if (value.startsWith('ENCRYPTED:')) {
                            plainValue = TestSecureStorageEncryption.decrypt(value);
                        }
                        
                        const encrypted = TestSecureStorageEncryption.encrypt(plainValue);
                        localStorage.setItem(key, encrypted);
                        
                        result += `<span class="success">✅ ${key}: 已重新加密</span>\n`;
                    } else {
                        result += `<span class="warning">⚠️ ${key}: 无数据可加密</span>\n`;
                    }
                });
                
                result += '\n<span class="info">请刷新页面查看效果</span>';
                resultDiv.innerHTML = result;
            } catch (error) {
                resultDiv.innerHTML = '<span class="error">❌ 强制加密失败: ' + error.message + '</span>';
            }
        }

        // 测试解密功能
        function testDecryption() {
            const resultDiv = document.getElementById('decryptionResult');
            
            try {
                const sensitiveKeys = ['access_token', 'refresh_token', 'user_id'];
                let result = '<span class="info">🔓 解密测试结果:</span>\n\n';
                
                sensitiveKeys.forEach(key => {
                    const value = localStorage.getItem(key);
                    if (value && value.startsWith('ENCRYPTED:')) {
                        const decrypted = TestSecureStorageEncryption.decrypt(value);
                        
                        result += `<span class="info">${key}:</span>\n`;
                        result += `  加密值: ${value.substring(0, 50)}...\n`;
                        result += `  解密值: ${decrypted}\n`;
                        result += `  状态: <span class="success">✅ 解密成功</span>\n\n`;
                    } else if (value) {
                        result += `<span class="warning">⚠️ ${key}: 数据未加密</span>\n`;
                    } else {
                        result += `<span class="warning">⚠️ ${key}: 无数据</span>\n`;
                    }
                });
                
                resultDiv.innerHTML = result;
            } catch (error) {
                resultDiv.innerHTML = '<span class="error">❌ 解密测试失败: ' + error.message + '</span>';
            }
        }

        // 完整流程测试
        function testFullFlow() {
            const resultDiv = document.getElementById('fullFlowResult');
            
            try {
                let result = '<span class="info">🔄 完整流程测试:</span>\n\n';
                
                // 测试数据
                const testValue = 'test_complete_flow_' + Date.now();
                const key = 'access_token';
                
                // 1. 加密
                const encrypted = TestSecureStorageEncryption.encrypt(testValue);
                result += `<span class="info">1. 加密测试:</span>\n`;
                result += `   原始值: ${testValue}\n`;
                result += `   加密值: ${encrypted}\n`;
                result += `   状态: <span class="${encrypted.startsWith('ENCRYPTED:') ? 'success' : 'error'}">${encrypted.startsWith('ENCRYPTED:') ? '✅ 成功' : '❌ 失败'}</span>\n\n`;
                
                // 2. 存储
                localStorage.setItem(key, encrypted);
                result += `<span class="info">2. 存储测试:</span>\n`;
                result += `   已存储到 localStorage\n\n`;
                
                // 3. 读取
                const stored = localStorage.getItem(key);
                result += `<span class="info">3. 读取测试:</span>\n`;
                result += `   读取值: ${stored}\n`;
                result += `   状态: <span class="${stored === encrypted ? 'success' : 'error'}">${stored === encrypted ? '✅ 一致' : '❌ 不一致'}</span>\n\n`;
                
                // 4. 解密
                const decrypted = TestSecureStorageEncryption.decrypt(stored);
                result += `<span class="info">4. 解密测试:</span>\n`;
                result += `   解密值: ${decrypted}\n`;
                result += `   状态: <span class="${decrypted === testValue ? 'success' : 'error'}">${decrypted === testValue ? '✅ 成功' : '❌ 失败'}</span>\n\n`;
                
                // 总结
                const allSuccess = encrypted.startsWith('ENCRYPTED:') && stored === encrypted && decrypted === testValue;
                result += `<span class="info">总体结果:</span> <span class="${allSuccess ? 'success' : 'error'}">${allSuccess ? '✅ 完整流程测试成功' : '❌ 存在问题'}</span>`;
                
                resultDiv.innerHTML = result;
            } catch (error) {
                resultDiv.innerHTML = '<span class="error">❌ 完整流程测试失败: ' + error.message + '</span>';
            }
        }

        // 页面加载时自动检查状态
        window.addEventListener('load', function() {
            console.log('🔐 加密功能测试页面已加载');
            checkCurrentStorage();
        });
    </script>
</body>
</html>

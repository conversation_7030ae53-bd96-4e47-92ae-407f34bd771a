/**
 * 浏览器扩展的内容脚本
 * 用于在网页中检测登录状态变化并同步到扩展存储
 * 支持安全存储加密数据
 */

// 防止无限循环的标志
let isSyncing = false;
let syncTimeout = null;

// 监听网页中的存储事件
window.addEventListener('storage', function(event) {
  // 检查是否是认证相关的存储项
  if (['access_token', 'refresh_token', 'user_id', 'user_email', 'subscription_info'].includes(event.key)) {
    scheduleSyncToExtension();
  }
});

// 监听自定义事件
window.addEventListener('storage-changed', function() {
  scheduleSyncToExtension();
});

// 监听从网页发送的同步请求
window.addEventListener('sync-to-extension', function(event) {
  if (event.detail && event.detail.key) {
    const data = {};
    data[event.detail.key] = event.detail.value;
    try {
      chrome.storage.local.set(data, function() {
        console.log('网页数据已同步到扩展:', event.detail.key);
      });
    } catch (error) {
      console.error('同步到扩展失败:', error);
    }
  }
});

// 使用节流防止频繁同步
function scheduleSyncToExtension() {
  if (syncTimeout) {
    clearTimeout(syncTimeout);
  }
  
  syncTimeout = setTimeout(() => {
    syncToExtensionStorage();
    syncTimeout = null;
  }, 100);
}

// 将网页的 localStorage 同步到扩展的 chrome.storage
function syncToExtensionStorage() {
  // 防止重复触发
  if (isSyncing) {
    // console.log('[ContentScript] Sync already in progress, skipping.');
    return;
  }

  isSyncing = true;
  // console.log('[ContentScript] Starting syncToExtensionStorage...');

  try {
    const authDataToRead = {
      access_token: localStorage.getItem('access_token'),
      refresh_token: localStorage.getItem('refresh_token'),
      token_type: localStorage.getItem('token_type'),
      access_token_expires_at: localStorage.getItem('access_token_expires_at'),
      user_id: localStorage.getItem('user_id'),
      user_email: localStorage.getItem('user_email'),
      subscription_info: localStorage.getItem('subscription_info')
    };

    // 记录同步状态（不记录敏感数据的实际值）
    const sensitiveKeys = ['access_token', 'refresh_token', 'user_id'];
    const logData = {};
    Object.keys(authDataToRead).forEach(key => {
      if (sensitiveKeys.includes(key)) {
        logData[key] = authDataToRead[key] ? '[ENCRYPTED_DATA]' : null;
      } else {
        logData[key] = authDataToRead[key];
      }
    });
    console.log('[ContentScript] Data read from localStorage:', JSON.stringify(logData, null, 2));

    const authDataForExtension = { ...authDataToRead }; // 克隆对象以便修改
    
    // 过滤掉空值
    Object.keys(authDataForExtension).forEach(key => {
      if (authDataForExtension[key] === null || authDataForExtension[key] === undefined) {
        // console.log(`[ContentScript] Removing null/undefined key: ${key}`);
        delete authDataForExtension[key];
      }
    });
    
    // 详细记录准备写入扩展存储的数据
    console.log('[ContentScript] authData prepared for chrome.storage.local:', JSON.stringify(authDataForExtension, null, 2));
    
    // 如果有数据，则保存到扩展存储
    if (Object.keys(authDataForExtension).length > 0) {
      try {
        chrome.storage.local.set(authDataForExtension, function() {
          if (chrome.runtime.lastError) {
            console.error('[ContentScript] Error setting to chrome.storage.local:', chrome.runtime.lastError.message);
          } else {
            console.log('[ContentScript] Webpage auth state successfully synced to extension storage.');
          }
          isSyncing = false;
          // console.log('[ContentScript] Sync finished for populated data.');
        });
      } catch (error) {
        console.error('[ContentScript] Exception during chrome.storage.local.set:', error);
        isSyncing = false;
      }
    } else {
      // 如果没有数据，需要更谨慎地处理
      console.log('[ContentScript] No auth data found in website storage.');

      // 检查扩展存储中是否有数据
      chrome.storage.local.get(['access_token', 'user_id'], function(extensionData) {
        if (chrome.runtime.lastError) {
          console.error('[ContentScript] Error reading extension storage:', chrome.runtime.lastError.message);
          isSyncing = false;
          return;
        }

        // 如果扩展中有数据但网站中没有，可能是网站数据丢失
        if (extensionData.access_token && extensionData.user_id) {
          console.warn('[ContentScript] 🚨 检测到异常：扩展有数据但网站无数据，可能是网站存储被意外清除');
          console.log('[ContentScript] 尝试从扩展恢复网站数据...');

          // 尝试恢复网站数据（从扩展同步到网站）
          try {
            // 触发从扩展到网站的同步
            syncFromExtensionToWebsite();
          } catch (error) {
            console.error('[ContentScript] 恢复网站数据失败:', error);
          }
        } else {
          // 两边都没有数据，这可能是正常的登出状态
          console.log('[ContentScript] 扩展和网站都没有认证数据，保持当前状态');
        }

        isSyncing = false;
      });
    }
  } catch (error) {
    console.error('[ContentScript] Error in syncToExtensionStorage function:', error);
    isSyncing = false;
  }
}

// 监听来自扩展的消息
chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
  if (message.action === 'syncFromExtension') {
    // 从扩展同步到网页（使用安全存储适配器）
    try {
      const keys = [
        'access_token',
        'refresh_token',
        'token_type',
        'access_token_expires_at',
        'user_id',
        'user_email',
        'subscription_info'
      ];

      // 使用安全存储适配器获取解密后的数据
      if (typeof SecureStorageAdapter !== 'undefined') {
        SecureStorageAdapter.getItems(keys, function(result) {
          try {
            // 将扩展存储的数据同步到网页的 localStorage
            // 注意：这里需要重新加密，因为网页端也使用相同的加密逻辑
            Object.keys(result).forEach(key => {
              if (result[key] !== null && result[key] !== undefined) {
                // 如果是敏感数据，需要重新加密后存储到localStorage
                if (SecureStorageEncryption.isSensitive(key)) {
                  const encryptedValue = SecureStorageEncryption.encrypt(
                    typeof result[key] === 'string' ? result[key] : JSON.stringify(result[key])
                  );
                  localStorage.setItem(key, encryptedValue);
                } else {
                  // 非敏感数据直接存储
                  localStorage.setItem(key, typeof result[key] === 'string' ? result[key] : JSON.stringify(result[key]));
                }
              }
            });

            console.log('[SecureStorage] 敏感数据已从插件安全同步到网页');

            // 触发自定义事件，通知网页更新状态
            window.dispatchEvent(new Event('storage'));

            // 确保在异步操作完成后发送响应
            sendResponse({ success: true });
          } catch (error) {
            console.error('[StorageBridge] 同步到网页时出错:', error);
            sendResponse({ success: false, error: error.message });
          }
        });
      } else {
        // 降级到原始方式
        chrome.storage.local.get(keys, function(result) {
          try {
            Object.keys(result).forEach(key => {
              if (result[key]) {
                localStorage.setItem(key, result[key]);
              }
            });

            window.dispatchEvent(new Event('storage'));
            sendResponse({ success: true });
          } catch (error) {
            console.error('[StorageBridge] 同步到网页时出错:', error);
            sendResponse({ success: false, error: error.message });
          }
        });
      }
    } catch (error) {
      console.error('[StorageBridge] 获取存储数据时出错:', error);
      sendResponse({ success: false, error: error.message });
      return false; // 不需要异步响应
    }
    return true; // 表示将异步发送响应
  } else if (message.action === 'fillContent') {
    // 填充内容到当前焦点元素
    fillContentToActiveElement(message.content);
    sendResponse({ success: true });
    return false;
  } else if (message.action === 'checkWebsiteParsing') {
    // 检查网站解析权限
    chrome.runtime.sendMessage({
      action: 'checkWebsiteParsing',
      language: navigator.language.startsWith('zh') ? 'zh' : 'en'
    }, (response) => {
      if (!response.allowed) {
        showWebsiteParsingLimitedNotification(response.message, response.upgradeUrl);
      }
      sendResponse(response);
    });
    return true; // 异步响应
  } else if (message.action === 'checkConnection') {
    // 添加一个简单的连接检查操作
    sendResponse({ connected: true });
    return false; // 同步响应，不需要保持通道开放
  }
});

// 初始化时同步一次
syncToExtensionStorage();

// 向页面注入通信脚本
function injectScript() {
  // 创建脚本元素
  const script = document.createElement('script');
  
  // 在CSP中已经允许unsafe-inline，所以不需要特别处理
  script.textContent = `
    // 检测 chrome.storage 是否可用
    window.hasExtensionStorage = typeof chrome !== 'undefined' &&
                               chrome.storage !== undefined &&
                               chrome.storage.local !== undefined;

    // 获取插件ID并暴露给网页
    window.extensionId = typeof chrome !== 'undefined' && chrome.runtime ? chrome.runtime.id : null;

    // 通知网页扩展已安装，并传递插件ID
    window.dispatchEvent(new CustomEvent('extension-installed', {
      detail: {
        extensionId: window.extensionId,
        timestamp: Date.now()
      }
    }));

    // 添加错误处理函数
    window.handleStorageError = function(error) {
      console.error('[StorageBridge] 捕获到存储错误:', error);
    };

    // 添加操作类型检查
    window.validateStorageOperation = function(operation) {
      const validOperations = ['get', 'set', 'remove', 'clear', 'sync', 'listen', 'storage_changed', 'storage_changed_result'];
      return validOperations.includes(operation);
    };

    // 添加插件ID验证函数
    window.getExtensionId = function() {
      return window.extensionId;
    };

    console.log('浏览器扩展通信脚本已注入，插件ID:', window.extensionId);
  `;
  
  // 将脚本添加到页面
  document.head.appendChild(script);
  
  // 注入完成后移除脚本元素
  script.remove();
  
  // 通知注入完成
  console.log('[StorageBridge] 客户端API脚本已注入');
}

// 从扩展同步到网页
function syncFromExtensionToWebsite() {
  console.log('[ContentScript] 🔄 开始从扩展恢复网页数据...');

  chrome.storage.local.get([
    'access_token',
    'refresh_token',
    'token_type',
    'access_token_expires_at',
    'user_id',
    'user_email',
    'subscription_info'
  ], function(items) {
    if (chrome.runtime.lastError) {
      console.error('[ContentScript] 读取扩展存储失败:', chrome.runtime.lastError.message);
      return;
    }

    console.log('[ContentScript] 扩展存储数据:', items);

    // 检查是否有必要的认证数据
    if (items.access_token && items.user_id) {
      console.log('[ContentScript] ✅ 找到认证数据，恢复到网页...');

      // 将数据同步到网页的 localStorage
      try {
        Object.keys(items).forEach(key => {
          if (items[key] !== undefined && items[key] !== null) {
            localStorage.setItem(key, items[key]);
            console.log(`[ContentScript] 恢复 ${key}: ${key.includes('token') ? '***' : items[key]}`);
          }
        });

        console.log('[ContentScript] ✅ 扩展数据已成功恢复到网页');

        // 触发 storage 事件通知网页更新
        window.dispatchEvent(new Event('storage'));

        // 额外触发自定义事件
        window.dispatchEvent(new CustomEvent('auth-restored', {
          detail: { source: 'extension', timestamp: Date.now() }
        }));

        console.log('[ContentScript] 🔄 已通知网页更新用户状态');
      } catch (error) {
        console.error('[ContentScript] 恢复数据到网页失败:', error);
      }
    } else {
      console.log('[ContentScript] ❌ 扩展中没有有效的认证数据，无法恢复');
    }
  });
}

// 注入脚本
injectScript();

// 填充内容到当前焦点元素
function fillContentToActiveElement(content) {
  const activeElement = document.activeElement;

  if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
    // 对于输入框和文本域
    activeElement.value = content;

    // 触发输入事件
    activeElement.dispatchEvent(new Event('input', { bubbles: true }));
    activeElement.dispatchEvent(new Event('change', { bubbles: true }));
  } else if (activeElement && activeElement.contentEditable === 'true') {
    // 对于可编辑的div等元素
    activeElement.textContent = content;

    // 触发输入事件
    activeElement.dispatchEvent(new Event('input', { bubbles: true }));
  }
}

// 显示网站解析限制提示
function showWebsiteParsingLimitedNotification(message, upgradeUrl) {
  // 创建提示框
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #ff6b6b;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    max-width: 300px;
    cursor: pointer;
  `;

  notification.innerHTML = `
    <div style="margin-bottom: 8px;">${message}</div>
    <div style="font-size: 12px; opacity: 0.9;">点击升级到高级版</div>
  `;

  // 点击跳转到升级页面
  notification.addEventListener('click', () => {
    window.open(upgradeUrl, '_blank');
    document.body.removeChild(notification);
  });

  document.body.appendChild(notification);

  // 5秒后自动消失
  setTimeout(() => {
    if (document.body.contains(notification)) {
      document.body.removeChild(notification);
    }
  }, 5000);
}
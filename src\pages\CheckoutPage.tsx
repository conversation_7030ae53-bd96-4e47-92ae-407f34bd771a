import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { setupInlineCheckout } from '../services/paddleService';
import { log } from '../utils/logger';

const CheckoutPage: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeCheckout = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // 等待DOM完全加载
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 设置嵌入式支付
        await setupInlineCheckout('checkout-container');
        
        setLoading(false);
      } catch (err) {
        log.error('支付页面初始化失败:', err);
        setError(err instanceof Error ? err.message : '支付系统加载失败');
        setLoading(false);
      }
    };

    initializeCheckout();
  }, []);

  const handleBack = () => {
    navigate('/pricing');
  };

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f8f9fa',
      padding: '20px'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        {/* 头部 */}
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          padding: '30px',
          textAlign: 'center'
        }}>
          <h1 style={{ margin: '0 0 10px 0', fontSize: '28px' }}>
            🚀 升级到 AutoFill Premium
          </h1>
          <p style={{ margin: 0, fontSize: '16px', opacity: 0.9 }}>
            解锁所有高级功能，提升您的工作效率
          </p>
        </div>

        {/* 功能对比 */}
        <div style={{
          padding: '30px',
          borderBottom: '1px solid #eee'
        }}>
          <h2 style={{ textAlign: 'center', marginBottom: '30px', color: '#333' }}>
            📊 功能对比
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '30px'
          }}>
            {/* 免费版 */}
            <div style={{
              border: '2px solid #dee2e6',
              borderRadius: '8px',
              padding: '20px',
              backgroundColor: '#f8f9fa'
            }}>
              <h3 style={{ color: '#6c757d', textAlign: 'center', marginBottom: '20px' }}>
                🆓 免费版
              </h3>
              <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
                <li style={{ padding: '8px 0', borderBottom: '1px solid #dee2e6' }}>
                  📁 最多 3 个分组
                </li>
                <li style={{ padding: '8px 0', borderBottom: '1px solid #dee2e6' }}>
                  📝 每个分组最多 10 条记录
                </li>
                <li style={{ padding: '8px 0', borderBottom: '1px solid #dee2e6' }}>
                  🌐 最多 3 个网站
                </li>
                <li style={{ padding: '8px 0', borderBottom: '1px solid #dee2e6' }}>
                  📄 每个网站最多 3 个子网站
                </li>
                <li style={{ padding: '8px 0' }}>
                  ⚙️ 每个子页面最多 10 条规则
                </li>
              </ul>
            </div>

            {/* 高级版 */}
            <div style={{
              border: '2px solid #28a745',
              borderRadius: '8px',
              padding: '20px',
              backgroundColor: '#f8fff9',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '-10px',
                right: '20px',
                background: '#28a745',
                color: 'white',
                padding: '4px 12px',
                borderRadius: '12px',
                fontSize: '12px',
                fontWeight: 'bold'
              }}>
                推荐
              </div>
              <h3 style={{ color: '#28a745', textAlign: 'center', marginBottom: '20px' }}>
                ⭐ Premium 版
              </h3>
              <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
                <li style={{ padding: '8px 0', borderBottom: '1px solid #d4edda', color: '#28a745' }}>
                  📁 <strong>无限</strong> 分组
                </li>
                <li style={{ padding: '8px 0', borderBottom: '1px solid #d4edda', color: '#28a745' }}>
                  📝 每个分组 <strong>无限</strong> 预设
                </li>
                <li style={{ padding: '8px 0', borderBottom: '1px solid #d4edda', color: '#28a745' }}>
                  🌐 <strong>无限</strong> 网站
                </li>
                <li style={{ padding: '8px 0', borderBottom: '1px solid #d4edda', color: '#28a745' }}>
                  📄 每个网站 <strong>无限</strong> 子页面
                </li>
                <li style={{ padding: '8px 0', color: '#28a745' }}>
                  ⚙️ 每个子页面 <strong>无限</strong> 规则
                </li>
              </ul>
              <div style={{
                textAlign: 'center',
                marginTop: '20px',
                padding: '15px',
                backgroundColor: '#28a745',
                color: 'white',
                borderRadius: '6px',
                fontSize: '18px',
                fontWeight: 'bold'
              }}>
                💰 仅需 $9.99/月
              </div>
            </div>
          </div>
        </div>

        {/* 支付区域 */}
        <div style={{ padding: '30px' }}>
          <h2 style={{ textAlign: 'center', marginBottom: '20px', color: '#333' }}>
            💳 安全支付 - Paddle
          </h2>
          <p style={{ textAlign: 'center', color: '#666', marginBottom: '30px' }}>
            使用Paddle安全支付系统，支持信用卡、PayPal等多种支付方式
          </p>
          
          {loading && (
            <div style={{
              textAlign: 'center',
              padding: '40px',
              color: '#666'
            }}>
              <div style={{
                display: 'inline-block',
                width: '40px',
                height: '40px',
                border: '4px solid #f3f3f3',
                borderTop: '4px solid #007bff',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                marginBottom: '20px'
              }}></div>
              <p>正在加载支付系统...</p>
            </div>
          )}

          {error && (
            <div style={{
              border: '2px solid #dc3545',
              borderRadius: '8px',
              padding: '20px',
              textAlign: 'center',
              backgroundColor: '#f8d7da',
              color: '#721c24',
              marginBottom: '20px'
            }}>
              <h3>❌ 加载失败</h3>
              <p>{error}</p>
              <button
                onClick={() => window.location.reload()}
                style={{
                  background: '#007bff',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                🔄 重新加载
              </button>
            </div>
          )}

          {/* 支付容器 */}
          <div id="checkout-container" style={{
            minHeight: loading ? '0' : '400px',
            border: loading || error ? 'none' : '1px solid #dee2e6',
            borderRadius: '8px',
            backgroundColor: '#fff'
          }}></div>

          {/* 返回按钮 */}
          <div style={{ textAlign: 'center', marginTop: '30px' }}>
            <button
              onClick={handleBack}
              style={{
                background: '#6c757d',
                color: 'white',
                border: 'none',
                padding: '12px 24px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '16px'
              }}
            >
              ← 返回定价页面
            </button>
          </div>
        </div>
      </div>

      {/* 添加旋转动画 */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default CheckoutPage;

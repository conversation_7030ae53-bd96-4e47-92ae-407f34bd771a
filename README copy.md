## 开始

首先，运行开发服务器：

```bash
npm run dev
# 或
   npm run dev:functions
```
编辑检查
```bash
npm run lint
npx eslint .
```

在浏览器中打开 [http://localhost:5173](http://localhost:5173) 查看结果。

你可以通过修改 `src/pages/HomePage.tsx` 文件来开始编辑页面。当你编辑文件时，页面会自动更新。

## 环境变量配置

在项目根目录创建 `.env.local` 文件，添加以下环境变量：

```
VITE_API_URL=https://autoapi.utilityapp.site/auth
VITE_API_KEY=your_api_key_here
VITE_USERAPI_URL=https://autoapi.utilityapp.site
```

## 部署

构建生产版本：

```bash
npm run build
```

预览构建结果：

```bash
npm run preview
```

部署 `dist` 目录到您的托管服务商。

## 项目概览 (请更新日期：2025-05-13)

本项目是一个基于 **React** 框架开发的网站，旨在介绍和推广一款浏览器插件。网站通过 **Cloudflare Pages** 进行部署和托管。

### 主要特性与进展：

*   **框架**: 项目采用 React 框架，使用 Vite 构建工具。
*   **接口**: 这是接口设计。
    *   实现基于邮箱验证码的登录注册流程。主要交互调整如下：
        *   调整了验证码请求逻辑：无论后端返回何种表示发送成功的状态，前端都会显示验证码输入框。

*   **核心功能**:
    *   实现基于邮箱验证码的登录注册流程。主要交互调整如下：
        *   调整了验证码请求逻辑：无论后端返回何种表示发送成功的状态，前端都会显示验证码输入框。
        *   移除了登录页面的显式用户提示信息框：操作结果（如验证码发送状态、登录成功或失败）不再通过页面上的特定消息区域显示，相关错误信息将记录在浏览器控制台。
        *   优化了验证码输入体验：点击"获取验证码"后，验证码输入框始终显示；点击"返回修改邮箱或重新获取验证码"按钮或重新请求验证码时，已输入的验证码会被清空。
        *   修复了登录验证逻辑：根据后端API实际返回的字段名（`access_token`），正确解析用户Token以完成登录流程并跳转至用户中心。
    *   **令牌管理与自动刷新**:
        *   成功登录后，系统会同时存储 `access_token`（访问令牌）和 `refresh_token`（刷新令牌）。
        *   应用加载时，会检查 `access_token` 的有效性（例如，通过客户端JWT解码检查过期时间，或未来通过专门的API端点验证）。
        *   若 `access_token` 无效或已过期，但存在 `refresh_token`，应用将自动尝试使用 `refresh_token` 向后端API（端点：`VITE_USERAPI_URL` + `/refresh-token`）请求新的令牌组。
        *   令牌刷新成功后，用户将保持登录状态并使用新的令牌；若刷新失败，用户将被登出。
    *   用户登录后可访问个人中心页面，查看账户信息并执行退出登录操作。
        *   个人中心页面现在会显示用户的 `ID`（从后端 `user_id` 映射）和 `subscription_info`（订阅状态）。
    *   创建了产品定价页面。
*   **用户界面 (UI)**:
    *   页眉组件已更新，移除了"退出登录"按钮。用户登录后页眉显示"个人中心"链接，退出操作通过个人中心页面的按钮进行。
*   **环境变量管理**:
    *   生产环境 (main 分支) 和测试环境 (test 分支等预览分支) 使用不同的 API 端点和密钥。
    *   这些环境变量可以通过 Cloudflare Pages 的 UI 进行配置（推荐方式），或者通过在构建命令中加入条件脚本来动态设置。
*   **Docker化**:
    *   项目已配置 `Dockerfile` 和 `.dockerignore` 文件，支持通过 Docker 容器化运行。
    *   Docker 镜像的构建和运行方式应遵循 React 应用的 Docker化最佳实践。(具体命令待更新)
*   **本地开发与测试**:
    *   使用 `npm run dev` (或其他包管理器命令) 启动本地开发服务器。
    *   通过 `.env.local` 文件管理本地环境变量。
    *   可使用 `vite preview` 命令预览构建后的项目。
*   **当前工作**:
    *   正在根据提供的设计图重构首页。

## 近期重要更新与决策日志

本部分记录了项目近期的一些关键技术调整和决策过程，旨在帮助团队成员理解当前架构的演进。

### 1. API 配置与环境变量统一

*   **目标**：统一服务端接口的调用方式，并使用环境变量进行配置。
*   **过程**：
    *   最初考虑使用 `NEXT_PUBLIC_API_URL`，但项目中实际使用的是 Vite，因此迁移到 Vite 的环境变量体系。
    *   最终确定使用 `.env` 文件中的 `VITE_API_URL` (指定后端服务根地址) 和 `VITE_API_KEY` (用于请求认证接口的 `x-api-key` 头)。
    *   创建了 `src/config/api.ts` 文件，用于集中管理和导出 API URL 和 API Key，确保所有服务模块使用统一的配置源。该文件会优先从环境变量中读取配置。
    *   移除了所有在代码中硬编码 API URL 或 Key 的做法，强调必须通过环境变量配置。

### 2. Token 管理与安全存储

*   **目标**：安全地管理用户认证令牌 (Access Token 和 Refresh Token)，并实现自动续期。
*   **过程**：
    *   在 `src/services/authService.ts` 中实现了完整的 Token 管理逻辑：
        *   **保存**：登录成功后，将 `access_token`、`refresh_token` 及其过期时间安全地存储到浏览器的 `localStorage`。
        *   **获取**：提供函数获取当前有效的 `access_token` 和 `refresh_token`。
        *   **刷新**：实现了 `refreshAccessToken` 函数，当 `access_token` 过期时，使用 `refresh_token` 静默请求新的令牌。
        *   **清除**：用户退出登录时，调用 `logout` 函数会清除 `localStorage` 中的所有认证相关信息（tokens, user_id 等）。
    *   用户信息（如 `user_id`, `subscription_info`）也随 Token 一并缓存和清除。

### 3. API 请求拦截与自动化处理

*   **目标**：简化 API 调用，自动处理通用请求头和 Token 刷新。
*   **过程**：
    *   创建了 `src/services/apiInterceptor.ts`，并导出了一个全局的 `apiClient` (基于 `fetch`封装)。
    *   **自动添加请求头**：`apiClient` 会在每个发出的请求中自动附加：
        *   `Content-Type: application/json` (可覆盖)
        *   `x-api-key`: 从 `src/config/api.ts` 获取。
        *   `Authorization: Bearer <access_token>`: 如果存在有效的 `access_token`。
    *   **自动刷新 Token**：在发送请求前，如果检测到 `access_token` 已过期（或即将过期），拦截器会尝试使用 `refresh_token` 调用 `authService.refreshAccessToken`。刷新成功后，使用新的 `access_token` 继续原请求；刷新失败则可能导致后续操作失败或用户被登出。

### 4. CORS 问题处理与代理演进

*   **目标**：解决开发过程中遇到的跨域资源共享 (CORS) 问题。
*   **过程**：
    *   初期由于后端 API 不允许前端直接跨域携带 `x-api-key` 等头部，导致请求失败。
    *   **Vite 代理尝试**：作为一种常见的开发环境解决方案，曾在 `vite.config.ts` 中配置了针对 `/api` 路径的代理，将请求转发至实际后端 `https://autoapi.utilityapp.site`。此时，`src/config/api.ts` 在开发模式下会将 `API_URL` 设置为 `/api`。
    *   **移除代理，直连 API**：根据后续明确指令，**已移除 Vite 代理配置**。现在所有环境（开发和生产）的 API 请求都直接发往由环境变量 `VITE_API_URL` 指定的完整 URL。`src/config/api.ts` 现在总是使用 `VITE_API_URL` 的值（或其默认值）。
    *   CORS 问题最终需要在服务端配置 `Access-Control-Allow-Origin`, `Access-Control-Allow-Headers` (允许 `x-api-key`, `Authorization` 等) 和 `Access-Control-Allow-Methods` 来彻底解决。
    *   项目中引入了 `src/utils/corsProxy.ts` 作为备选方案，但当前并未激活使用。详细的 CORS 解决方案和讨论记录在 `README-CORS.md` 文件中。

### 5. 环境变量检查与设置指导

*   **目标**：确保开发和部署时必要的环境变量已正确设置。
*   **过程**：
    *   创建了 Node.js 脚本 `scripts/check-env.js`，用于在应用启动前检查 `.env` 文件中是否定义了如 `VITE_API_URL` 和 `VITE_API_KEY` 等关键环境变量。
    *   在 `package.json` 的 `scripts` 中添加了 `check-env` 命令，并可能集成到 `dev` 或 `build` 流程中（具体视 `package.json` 配置）。
    *   创建了 `API_SETUP.md` 文件，提供了关于如何获取和配置 API 密钥、设置环境变量以及解决常见认证问题的详细步骤。

### 6. 退出登录逻辑优化

* **目标**：优化退出登录流程，确保即使后端请求失败，也能清除本地存储并重定向到登录页面。
* **过程**：
  * 修改了 `ProfilePage.tsx` 中的 `handleLogout` 函数：
    * 先调用 `authLogout` 清除本地状态
    * 直接清除所有本地存储的 token 和缓存
    * 尝试调用后端登出 API，但不等待结果
    * 立即重定向到首页
    * 即使出错，也会清除本地存储并重定向
  * 修改了 `authService.ts` 中的 `logout` 函数：
    * 如果没有找到令牌，直接清除本地数据并返回
    * 即使后端请求失败，也清除本地数据
    * 确保在出错的情况下也清除本地数据
  * 修改了 `AuthContext.tsx` 中的 `logout` 函数：
    * 先清除本地用户状态
    * 尝试调用 `authService` 的 `logout`，但不阻塞流程
    * 确保本地存储被清除
    * 即使出错，也确保本地存储和用户状态被清除
* **结果**：
  * 用户退出登录时，无论后端请求是否成功，都会立即清除本地存储并重定向到首页
  * 增强了应用的健壮性，避免因后端服务不可用而导致用户无法退出的情况
  * 提高了用户体验，使退出登录操作更加流畅和可靠

### 7. 定价页面重构与国际化

*   **目标**: 更新定价方案，并实现页面的中英文国际化（i18n）。
*   **过程**:
        *   **更新价格与功能**:
            *   将高级版（Premium）价格调整为 **$9.99/年**，并提供 **30天试用期**。
            *   根据最新需求，详细区分了高级版与免费版的功能列表。具体区别如下：

| 功能点 | 高级版 (Premium) | 免费版 (Free) |
| :--- | :--- | :--- |
| **云同步** | ✅ 支持 | ❌ 不支持 |
| **分组数量** | 无限制 | 最多 3 个 |
| **每组数据数量** | 无限制 | 最多 10 条 |
| **网站数量** | 无限制 | 只能 1 个 |
| **网站K值数量**| 无限制 | 最多 5 个 |

        *   **引入国际化 (i18n)**:
            *   使用 `react-i18next` 和 `i18next-browser-languagedetector` 库来支持多语言。
            *   重构了 `src/pages/PricingPage.tsx`，将所有硬编码的文本（如标题、按钮文字、功能描述等）替换为 `t()` 函数调用。
            *   创建并更新了语言包文件 `src/i18n/locales/en.ts` (英文) 和 `src/i18n/locales/zh.ts` (中文)，包含了定价页面的所有翻译文本。
*   **结果**:
    *   定价页面现在准确地展示了最新的产品定价和功能差异。
    *   页面内容可以根据用户的浏览器语言或URL参数自动切换中英文，提高了对不同地区用户的友好度。

### 8. 移除登录功能

*   **目标**: 临时移除所有登录功能，包括相关入口和页面。
*   **过程**:
    *   **屏蔽登录和个人中心路由**:
        *   在 `src/App.tsx` 中注释掉 `/login` 和 `/profile` 路由配置，使其无法通过直接访问 URL。
    *   **隐藏导航栏中的登录入口**:
        *   在 `src/components/MainNavigation.tsx` 中注释掉登录/个人中心的条件判断代码块，完全移除这些入口。
*   **结果**:
    *   用户界面中不再显示任何登录相关的入口。
    *   直接访问登录或个人中心页面的 URL 会被重定向到首页。
    *   用户无法看到或使用登录功能，即使在后端仍然支持该功能的情况下。

### 9. 优化语言切换器

*   **目标**: 将语言切换器从按钮组改为下拉框形式，提升用户体验。
*   **过程**:
    *   **重构 `LanguageSwitcher` 组件**:
        *   移除原有的两个按钮（一个用于英文，一个用于中文）。
        *   添加单个 `<select>` 下拉框，包含 "English" 和 "中文" 两个选项。
        *   实现 `onChange` 事件处理器，当选择变化时调用 `i18n.changeLanguage()` 方法切换语言。
        *   添加下拉箭头图标，并应用符合整体设计风格的样式。
*   **结果**:
    *   语言切换操作更加符合用户习惯，采用标准的下拉框交互模式。
    *   界面更加整洁，减少了页面上的按钮数量。
    *   保持了多语言切换功能的完整性，同时优化了用户界面。

这些更新旨在提高项目的可维护性、安全性和开发效率。

## 项目结构

```
/src
  /components       # 共享组件
  /pages            # 页面组件
  /assets           # 静态资源
  /contexts         # React上下文
  /services         # API服务
  main.tsx          # 主入口文件
  App.tsx           # 应用根组件
/public             # 公共资源
```

## 后端API接口

本项目前端应用依赖以下后端API接口进行用户认证和数据获取。

### 环境变量说明

*   `${VITE_API_URL}`: 指向认证相关基础API路径 (例如: `https://logindev.utilityapp.site/auth`)。主要用于请求验证码和验证码登录。
*   `${VITE_API_KEY}`: 上述认证API所需的API密钥。
*   `${VITE_USERAPI_URL}`: 指向用户数据及令牌刷新相关API的基础路径 (例如: `https://autoapi.utilityapp.site`)。注意：实际使用中，`/user/me`, `/refresh-token`, `/logout` 等端点是直接位于此基础路径下，而非如先前示例中可能暗示的 `/auth` 子路径下。

### 1. 请求验证码

*   **端点**: `/request-code`
*   **完整URL**: `${VITE_API_URL}/request-code`
*   **方法**: `POST`
*   **请求头**:
    *   `Content-Type: application/json`
    *   `x-api-key: ${VITE_API_KEY}`
*   **请求体 (JSON)**:
    ```json
    {
      "email": "<EMAIL>"
    }
    ```
*   **用途**: 用户输入邮箱后，请求后端向该邮箱发送验证码。
*   **成功响应**: 通常返回数字 `1` 表示成功发送。其他数字可能表示特定状态（例如 `2` 可能表示已发送但频率限制等）。
    ```json
    1
    ```
*   **失败响应 (示例)**:
    ```json
    {
      "message": "请求过于频繁，请稍后再试"
    }
    ```

### 2. 验证验证码并登录/注册

*   **端点**: `/verify-code`
*   **完整URL**: `${VITE_API_URL}/verify-code`
*   **方法**: `POST`
*   **请求头**:
    *   `Content-Type: application/json`
    *   `x-api-key: ${VITE_API_KEY}`
*   **请求体 (JSON)**:
    ```json
    {
      "email": "<EMAIL>",
      "code": "123456"
    }
    ```
*   **用途**: 用户提交邮箱和6位验证码，进行登录或首次注册。
*   **成功响应 (示例)**: 返回包含访问令牌和刷新令牌。
    ```json
    {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "def50200283c280702772774937000351d1...",
      "access_token_expires_in": 3600,
      "token_type": "bearer"
    }
    ```
*   **失败响应 (示例)**:
    ```json
    {
      "message": "验证码错误或已过期"
    }
    ```

### 3. 刷新访问令牌

*   **端点**: `/refresh-token`
*   **完整URL**: `${VITE_USERAPI_URL}/refresh-token`
*   **方法**: `POST`
*   **请求头**:
    *   `Content-Type: application/json`
*   **请求体 (JSON)**:
    ```json
    {
      "refresh_token": "def50200283c280702772774937000351d1..."
    }
    ```
    *(注意: 部分后端实现可能也需要在请求体或请求头中附带已过期的 `access_token`)*
*   **用途**: 使用长期有效的刷新令牌获取新的访问令牌和刷新令牌。
*   **成功响应 (示例)**:
    ```json
    {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "def50200283c280702772774937000351d1...",
      "access_token_expires_in": 3600,
      "token_type": "bearer"
    }
    ```
*   **失败响应 (示例)**:
    ```json
    {
      "message": "刷新令牌无效或已过期"
    }
    ```

### 4. 获取用户信息

*   **端点**: `/user/me`
*   **完整URL**: `${VITE_USERAPI_URL}/user/me`
*   **方法**: `GET`
*   **请求头**:
    *   `Content-Type: application/json`
    *   `Authorization: Bearer <access_token>`
*   **用途**: 获取当前已认证用户的详细个人信息。
*   **成功响应 (示例)**:
    ```json
    {
      "user_id": "3a84d328-82ff-4e50-bd23-059fa00ad0d5",
      "email": "<EMAIL>",
      "subscription_info": null
    }
    ```
*   **失败响应 (示例)**: 返回 401 Unauthorized 或 403 Forbidden 状态码。
    ```json
    {
      "message": "用户未认证"
    }
    ```

### 5. 服务端登出

*   **端点**: `/logout`
*   **完整URL**: `${VITE_USERAPI_URL}/logout`
*   **方法**: `POST`
*   **描述**: 使提供的 Refresh Token 关联的会话在服务端失效，实现服务端登出。
*   **认证**: **需要**有效的 Access Token。
*   **请求头**:
    *   `Content-Type: application/json`
    *   `Authorization: Bearer <有效的 Access Token>`
*   **请求体 (JSON)**:
    ```json
    {
      "refresh_token": "string (需要作废的 Refresh Token JWT)"
    }
    ```
*   **请求体示例**:
    ```json
    {
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... (要登出的那个)"
    }
    ```
*   **成功响应 (200 OK)**:
    ```json
    {
      "message": "登出成功。"
    }
    ```
*   **错误响应**:
    *   `401 Unauthorized`: `{"detail": "无法验证凭据"}` (如果 Access Token 无效) 或 `{"detail": "Invalid token type, expected access token"}`。
    *   `422 Unprocessable Entity`: 请求体字段缺失或格式错误 (例如 `{"detail":[{"type":"missing","loc":["body","refresh_token"],"msg":"Field required"}]}`) 。
    *   `500 Internal Server Error`: 服务端内部错误。

## 前端服务

### 1. 用户服务 (UserService)

用户服务提供了与用户相关的所有API调用功能。

#### 接口定义

```typescript
interface UserProfile {
  user_id: string;
  email: string;
  subscription_info: any | null;
}
```

#### 方法

##### getUserProfile

获取当前登录用户的详细信息。

*   **参数**:
    *   `user: User` - Firebase用户对象
*   **返回**: `Promise<UserProfile>`
*   **用途**: 获取用户的ID、邮箱和订阅信息
*   **错误处理**: 如果API调用失败，将抛出错误

### 2. 认证上下文 (AuthContext)

认证上下文提供了用户认证状态管理和相关功能。

#### 主要功能

*   用户登录状态管理
*   令牌自动刷新
*   登出功能
*   认证状态持久化

#### 使用方式

```typescript
import { useAuth } from '../contexts/AuthContext';

function YourComponent() {
  const { currentUser, logout } = useAuth();
  
  // 使用认证状态
  return (
    <div>
      {currentUser ? (
        <p>欢迎, {currentUser.email}</p>
      ) : (
        <p>请登录</p>
      )}
    </div>
  );
}
```

### 3. 用户上下文 (UserContext)

用户上下文提供了用户数据的全局状态管理。

#### 主要功能

*   用户资料管理
*   加载状态管理
*   错误处理
*   数据刷新机制

#### 使用方式

```typescript
import { useUser } from '../contexts/UserContext';

function YourComponent() {
  const { userProfile, loading, error, refreshUserProfile } = useUser();
  
  // 使用用户数据
  return (
    <div>
      {loading ? (
        <p>加载中...</p>
      ) : error ? (
        <p>错误: {error}</p>
      ) : (
        <p>用户ID: {userProfile?.user_id}</p>
      )}
    </div>
  );
}
```

## 了解更多

*   [React 文档](https://reactjs.org/)
*   [Vite 文档](https://vitejs.dev/)
*   [Tailwind CSS 文档](https://tailwindcss.com/)
*   [Firebase 文档](https://firebase.google.com/docs)
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle 403错误诊断</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .log { max-height: 400px; overflow-y: auto; }
        .step { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007cba; }
        .token-input { width: 400px; padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Paddle 403错误诊断工具</h1>
        
        <div class="section warning">
            <h2>⚠️ 当前问题</h2>
            <p>Paddle支付系统返回403错误，通常原因：</p>
            <ul>
                <li><strong>Token无效或格式错误</strong> - 最常见原因</li>
                <li><strong>域名未在Paddle Dashboard中配置白名单</strong></li>
                <li><strong>产品ID不存在或无效</strong></li>
                <li><strong>Token权限不足</strong></li>
            </ul>
        </div>

        <div class="section">
            <h2>📋 当前配置</h2>
            <pre id="config-display"></pre>
        </div>

        <div class="section">
            <h2>🔧 Token测试</h2>
            <div>
                <label>测试新Token:</label><br>
                <input type="text" id="test-token" class="token-input" placeholder="输入新的client-side token (test_xxxxxxxxxxxxxxxxxxxxxxxxx)">
                <button onclick="testNewToken()">测试Token</button>
            </div>
            <div class="step">
                <strong>获取正确Token的步骤：</strong><br>
                1. 访问 <a href="https://sandbox-vendors.paddle.com/authentication-v2" target="_blank">Paddle Sandbox Dashboard</a><br>
                2. 登录后进入 Developer tools > Authentication > Client-side tokens<br>
                3. 点击 "New client-side token"<br>
                4. 复制生成的完整token（应该是30个字符：test_ + 27个字符）
            </div>
        </div>

        <div class="section">
            <h2>🧪 诊断测试</h2>
            <button onclick="step1_validateToken()">步骤1: 验证Token格式</button>
            <button onclick="step2_testDomainAccess()">步骤2: 测试域名访问</button>
            <button onclick="step3_testProductAPI()">步骤3: 测试产品API</button>
            <button onclick="step4_testSDKLoad()">步骤4: 测试SDK加载</button>
            <button onclick="step5_testInitialize()">步骤5: 测试初始化</button>
            <button onclick="clearResults()">清空结果</button>
        </div>

        <div class="section">
            <h2>📝 诊断结果</h2>
            <div id="test-results" class="log"></div>
        </div>

        <div class="section info">
            <h2>💡 解决方案建议</h2>
            <div id="solutions"></div>
        </div>
    </div>

    <script>
        const config = {
            token: 'test_459728092d35d36bc173c60e52d',
            productId: 'pri_01jxa2azwznm06dqbptzagtm0z',
            vendorId: '32634',
            environment: 'sandbox',
            domain: window.location.hostname
        };

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            resultsDiv.appendChild(logEntry);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('solutions').innerHTML = '';
        }

        function showConfig() {
            document.getElementById('config-display').textContent = JSON.stringify(config, null, 2);
        }

        function addSolution(solution) {
            const solutionsDiv = document.getElementById('solutions');
            const solutionDiv = document.createElement('div');
            solutionDiv.className = 'step';
            solutionDiv.innerHTML = solution;
            solutionsDiv.appendChild(solutionDiv);
        }

        // 步骤1: 验证Token格式
        function step1_validateToken() {
            log('🔍 步骤1: 验证Token格式...', 'info');
            
            const token = config.token;
            const expectedLength = 30; // test_ (5) + 27个字符
            const tokenRegex = /^test_[a-zA-Z0-9]{27}$/;
            
            log(`Token: ${token}`, 'info');
            log(`Token长度: ${token.length} (期望: ${expectedLength})`, token.length === expectedLength ? 'success' : 'error');
            
            if (token.length !== expectedLength) {
                log('❌ Token长度不正确！', 'error');
                addSolution('🔧 <strong>解决方案1:</strong> Token长度错误，请从Paddle Dashboard获取完整的30字符token');
                return false;
            }
            
            if (!tokenRegex.test(token)) {
                log('❌ Token格式不正确！', 'error');
                addSolution('🔧 <strong>解决方案2:</strong> Token格式错误，应该是 test_xxxxxxxxxxxxxxxxxxxxxxxxx 格式');
                return false;
            }
            
            log('✅ Token格式验证通过', 'success');
            return true;
        }

        // 步骤2: 测试域名访问
        function step2_testDomainAccess() {
            log('🌐 步骤2: 检查域名配置...', 'info');
            
            const currentDomain = window.location.hostname;
            const allowedDomains = ['localhost', 'autofill.utilityapp.site', '127.0.0.1'];
            
            log(`当前域名: ${currentDomain}`, 'info');
            log(`建议配置的域名: ${allowedDomains.join(', ')}`, 'info');
            
            if (!allowedDomains.includes(currentDomain)) {
                log('⚠️ 当前域名可能未在Paddle中配置', 'warning');
                addSolution(`🔧 <strong>解决方案3:</strong> 在Paddle Dashboard中添加域名白名单：${currentDomain}`);
            } else {
                log('✅ 域名检查通过', 'success');
            }
        }

        // 步骤3: 测试产品API
        async function step3_testProductAPI() {
            log('💰 步骤3: 测试产品API访问...', 'info');
            
            try {
                const response = await fetch(`https://sandbox-api.paddle.com/prices/${config.productId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${config.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log(`API响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.status === 401) {
                    log('❌ Token无效或已过期', 'error');
                    addSolution('🔧 <strong>解决方案4:</strong> Token无效，请重新生成client-side token');
                } else if (response.status === 403) {
                    log('❌ Token权限不足或域名限制', 'error');
                    addSolution('🔧 <strong>解决方案5:</strong> 检查token权限和域名白名单配置');
                } else if (response.status === 404) {
                    log('❌ 产品ID不存在', 'error');
                    addSolution('🔧 <strong>解决方案6:</strong> 检查产品ID是否正确');
                } else if (response.ok) {
                    const data = await response.json();
                    log('✅ 产品API访问成功', 'success');
                    log(`产品信息: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    const errorText = await response.text();
                    log(`❌ API错误: ${errorText}`, 'error');
                }
            } catch (error) {
                log(`❌ API请求失败: ${error.message}`, 'error');
            }
        }

        // 步骤4: 测试SDK加载
        function step4_testSDKLoad() {
            log('📦 步骤4: 测试Paddle SDK加载...', 'info');
            
            if (window.Paddle) {
                log('✅ Paddle SDK已加载', 'success');
                return true;
            }
            
            log('🔄 正在加载Paddle SDK...', 'info');
            
            const script = document.createElement('script');
            script.src = 'https://cdn.paddle.com/paddle/v2/paddle.js';
            script.onload = () => {
                log('✅ Paddle SDK加载成功', 'success');
            };
            script.onerror = () => {
                log('❌ Paddle SDK加载失败', 'error');
                addSolution('🔧 <strong>解决方案7:</strong> 网络问题或CDN访问受限，检查网络连接');
            };
            
            document.head.appendChild(script);
        }

        // 步骤5: 测试初始化
        function step5_testInitialize() {
            log('🚀 步骤5: 测试Paddle初始化...', 'info');
            
            if (!window.Paddle) {
                log('❌ Paddle SDK未加载', 'error');
                return;
            }
            
            try {
                window.Paddle.Initialize({
                    token: config.token
                });
                log('✅ Paddle初始化成功', 'success');
            } catch (error) {
                log(`❌ Paddle初始化失败: ${error.message}`, 'error');
                addSolution('🔧 <strong>解决方案8:</strong> 初始化失败，通常是token问题');
            }
        }

        // 测试新Token
        function testNewToken() {
            const newToken = document.getElementById('test-token').value.trim();
            if (!newToken) {
                alert('请输入Token');
                return;
            }
            
            config.token = newToken;
            showConfig();
            clearResults();
            log(`🔄 使用新Token进行测试: ${newToken}`, 'info');
            
            // 重新运行所有测试
            setTimeout(() => step1_validateToken(), 100);
            setTimeout(() => step2_testDomainAccess(), 200);
            setTimeout(() => step3_testProductAPI(), 300);
        }

        // 页面加载时显示配置
        window.onload = function() {
            showConfig();
            log('🎯 Paddle 403错误诊断工具已加载', 'info');
            log('💡 请按顺序点击测试按钮进行诊断', 'info');
        };
    </script>
</body>
</html>

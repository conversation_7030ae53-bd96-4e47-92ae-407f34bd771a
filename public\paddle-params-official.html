<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle官方参数格式验证</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .log { max-height: 400px; overflow-y: auto; }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0; }
        .param-box { padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .official { border-color: #28a745; background-color: #f8fff9; }
        .current { border-color: #dc3545; background-color: #fff8f8; }
        .fixed { border-color: #17a2b8; background-color: #f0f9ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Paddle官方参数格式验证</h1>
        
        <div class="section info">
            <h2>📋 官方文档参数格式分析</h2>
            
            <h3>1. Paddle.Initialize() 正确格式</h3>
            <div class="comparison">
                <div class="param-box official">
                    <h4>✅ 官方标准格式</h4>
                    <pre>Paddle.Initialize({
  token: "live_xxx" | "test_xxx",
  eventCallback: (data) => {
    // 处理事件
  }
});</pre>
                </div>
                
                <div class="param-box current">
                    <h4>❌ 我们当前的格式</h4>
                    <pre>Paddle.Initialize({
  token: "live_xxx",
  checkout: {
    settings: {
      displayMode: 'overlay',
      theme: 'light',
      locale: 'zh'
    }
  },
  eventCallback: callback
});</pre>
                </div>
            </div>
            
            <h3>2. Paddle.Checkout.open() 正确格式</h3>
            <div class="comparison">
                <div class="param-box official">
                    <h4>✅ 官方标准格式</h4>
                    <pre>Paddle.Checkout.open({
  items: [{
    priceId: "pri_xxx",
    quantity: 1
  }],
  customer: {
    email: "<EMAIL>"
  },
  customData: {
    userId: "123"
  },
  settings: {
    displayMode: "overlay",
    theme: "light",
    locale: "en"
  }
});</pre>
                </div>
                
                <div class="param-box current">
                    <h4>❌ 我们当前的格式</h4>
                    <pre>// usePaddle Hook中的问题：
const fullConfig = {
  items: checkoutConfig.items,
  customer: checkoutConfig.customer,
  customData: checkoutConfig.customData,
  ...checkoutConfig.settings  // ❌ 错误展开
};

window.Paddle.Checkout.open(fullConfig);</pre>
                </div>
            </div>
        </div>

        <div class="section error">
            <h2>❌ 发现的问题</h2>
            <ol>
                <li><strong>Initialize配置错误:</strong> 不应该在Initialize中设置checkout.settings</li>
                <li><strong>参数展开错误:</strong> settings应该作为独立属性，不应该展开到根级别</li>
                <li><strong>locale值问题:</strong> 使用了"zh"，应该用标准locale如"en"或"zh-CN"</li>
                <li><strong>类型定义不完整:</strong> 缺少完整的参数类型定义</li>
            </ol>
        </div>

        <div class="section success">
            <h2>✅ 修正后的格式</h2>
            
            <div class="param-box fixed">
                <h3>🔧 正确的Initialize格式</h3>
                <pre>Paddle.Initialize({
  token: "live_6534d079211be149aae16b81d86",
  eventCallback: (data) => {
    console.log('Paddle事件:', data);
  }
});</pre>
            </div>
            
            <div class="param-box fixed">
                <h3>🔧 正确的Checkout.open格式</h3>
                <pre>Paddle.Checkout.open({
  items: [{
    priceId: "pri_01k0rfjdvjgsyrqxbf6sn6kk3w",
    quantity: 1
  }],
  customer: {
    email: "<EMAIL>"
  },
  customData: {
    userId: "123",
    plan: "yearly"
  },
  settings: {
    displayMode: "overlay",
    theme: "light",
    locale: "en"
  }
});</pre>
            </div>
        </div>

        <div class="section">
            <h2>🧪 测试修正后的格式</h2>
            <button onclick="testOfficialInitialize()">1. 测试官方Initialize格式</button>
            <button onclick="testOfficialCheckout()">2. 测试官方Checkout格式</button>
            <button onclick="testFixedFormat()">3. 测试修正后的完整格式</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section warning">
            <h2>🔧 需要修复的代码</h2>
            <h3>1. paddleService.ts 中的 Initialize 方法</h3>
            <pre>// ❌ 错误的格式
const initConfig = {
  token: token,
  checkout: { settings: {...} },  // 不需要这个
  eventCallback: callback
};

// ✅ 正确的格式
const initConfig = {
  token: token,
  eventCallback: callback
};</pre>
            
            <h3>2. usePaddle Hook 中的 checkout 方法</h3>
            <pre>// ❌ 错误的格式
const fullConfig = {
  items: checkoutConfig.items,
  customer: checkoutConfig.customer,
  customData: checkoutConfig.customData,
  ...checkoutConfig.settings  // 错误展开
};

// ✅ 正确的格式
const fullConfig = {
  items: checkoutConfig.items,
  customer: checkoutConfig.customer,
  customData: checkoutConfig.customData,
  settings: checkoutConfig.settings  // 作为独立属性
};</pre>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'live_6534d079211be149aae16b81d86',
            productId: 'pri_01k0rfjdvjgsyrqxbf6sn6kk3w'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        // 1. 测试官方Initialize格式
        async function testOfficialInitialize() {
            log('🧪 测试1: 官方Initialize格式...', 'info');
            
            try {
                // 按照官方文档的简洁格式
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            log(`❌ Checkout错误: ${data.detail}`, 'error');
                        } else if (data.type === 'checkout.loaded') {
                            log('✅ Checkout加载成功！', 'success');
                        }
                    }
                });
                
                log('✅ 官方Initialize格式初始化成功', 'success');
                
            } catch (error) {
                log(`❌ 官方Initialize格式失败: ${error.message}`, 'error');
            }
        }

        // 2. 测试官方Checkout格式
        async function testOfficialCheckout() {
            log('🧪 测试2: 官方Checkout格式...', 'info');
            
            try {
                // 确保先初始化
                await testOfficialInitialize();
                
                // 按照官方文档的完整格式
                const officialConfig = {
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }],
                    customer: {
                        email: "<EMAIL>"
                    },
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                log(`官方配置: ${JSON.stringify(officialConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(officialConfig);
                log('✅ 官方Checkout格式请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 官方Checkout格式失败: ${error.message}`, 'error');
            }
        }

        // 3. 测试修正后的完整格式
        async function testFixedFormat() {
            log('🧪 测试3: 修正后的完整格式...', 'info');
            
            try {
                // 修正后的完整配置
                const fixedConfig = {
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }],
                    customer: {
                        email: "<EMAIL>"
                    },
                    customData: {
                        userId: "test123",
                        plan: "yearly",
                        source: "website",
                        timestamp: Date.now()
                    },
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en-US"
                    }
                };
                
                log(`修正配置: ${JSON.stringify(fixedConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(fixedConfig);
                log('✅ 修正后格式请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 修正后格式失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示信息
        window.onload = function() {
            log('🔍 Paddle官方参数格式验证工具已加载', 'info');
            log('💡 发现了项目中的参数格式问题', 'warning');
            log('🎯 测试官方标准格式以验证修复方案', 'info');
        };
    </script>
</body>
</html>

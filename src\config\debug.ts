/**
 * 调试工具
 * 用于检查和解决白屏问题
 */

// 检查DOM是否正常渲染
export function checkDOMRendering(): void {
  // 检查root元素
  const rootElement = document.getElementById('root');

  if (rootElement && import.meta.env.VITE_DEBUG === 'true') {
    console.log('Root元素:', rootElement ? '存在' : '不存在');
    console.log('Root元素子节点数量:', rootElement.childNodes.length);
  }
}

// 检查环境变量是否正确加载
export function checkEnvironmentVariables(): void {
  if (import.meta.env.VITE_DEBUG === 'true') {
    console.log('VITE_API_URL:', import.meta.env.VITE_API_URL || '未设置');
    console.log('VITE_API_KEY:', import.meta.env.VITE_API_KEY ? '已设置' : '未设置');
  }
}

// 检查JavaScript错误
export function setupErrorHandling(): void {
  // 捕获未处理的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    if (import.meta.env.VITE_DEBUG === 'true') {
      console.error('未处理的Promise错误:', event.reason);
    }
  });

  // 捕获全局错误
  window.addEventListener('error', (event) => {
    if (import.meta.env.VITE_DEBUG === 'true') {
      console.error('全局错误:', event.error);
    }
  });
}

// 检查网络请求
export function monitorNetworkRequests(): void {
  // 如果支持Fetch API拦截且启用调试
  if (window.fetch && import.meta.env.VITE_DEBUG === 'true') {
    const originalFetch = window.fetch;
    window.fetch = async function(...args) {
      const url = args[0];

      try {
        const response = await originalFetch.apply(this, args);
        console.log('请求完成:', url, response.status);
        return response;
      } catch (error) {
        console.error('请求失败:', url, error);
        throw error;
      }
    };
  }
}

// 初始化调试工具
export function initDebugTools(): void {
  // 检查是否启用调试模式
  const debugEnabled = import.meta.env.VITE_DEBUG === 'true';

  if (debugEnabled) {
    checkDOMRendering();
    checkEnvironmentVariables();
    setupErrorHandling();
    monitorNetworkRequests();
  }

  // 添加全局调试函数
  (window as any).debugApp = {
    checkDOMRendering,
    checkEnvironmentVariables,
    clearLocalStorage: () => {
      localStorage.clear();
    },
    showLocalStorage: () => {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          const value = localStorage.getItem(key);
          console.log(`${key}: ${value}`);
        }
      }
    }
  };
}
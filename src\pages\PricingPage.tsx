import React, { useEffect, useState, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useUser } from '../contexts/UserContext';
import { initializePaddle } from '@paddle/paddle-js';
import { PADDLE_CLIENT_TOKEN, PADDLE_PRODUCT_ID, PADDLE_ENVIRONMENT } from '../config/paddle';
import { useTranslation } from 'react-i18next';
import { log } from '../utils/logger';
import { hasTrialEligibility, getTrialStatusDescription } from '../services/userService';

// Placeholder SVG for checkmark
const CheckIcon = ({ className = "h-5 w-5 text-blue-600" }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 20 20">
    <path
      fillRule="evenodd"
      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
      clipRule="evenodd"
    ></path>
  </svg>
);

// Placeholder SVG for a 'not available' or 'limited' feature icon
const LimitedFeatureIcon = ({ className = "h-5 w-5 text-gray-400" }: { className?: string }) => (
    <svg className={className} fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"></path>
    </svg>
);


// Placeholder for the illustration on the left
const IllustrationPlaceholder = ({ className = "w-full h-auto max-w-xs mx-auto" }: { className?: string }) => (
  <div className={`bg-orange-100/50 dark:bg-orange-800/20 rounded-lg p-8 flex items-center justify-center ${className}`}>
    <svg className="h-32 w-32 text-orange-400 dark:text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1"
        d="M5.5 11.5L9.5 15.5L18.5 6.5M15 19H9C6.79086 19 5 17.2091 5 15V9C5 6.79086 6.79086 5 9 5H15C17.2091 5 19 6.79086 19 9V15C19 17.2091 17.2091 19 15 19Z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="0.5" d="M10 3L8 5M14 3L16 5M3 10L5 8M3 14L5 16M21 10L19 8M21 14L19 16M10 21L8 19M14 21L16 19" />
        <text x="50%" y="55%" dominantBaseline="middle" textAnchor="middle" fontSize="8" className="fill-orange-600 dark:fill-orange-400 font-semibold">%</text>
    </svg>
  </div>
);

// Placeholder for the small icon next to "Choose"
const SwitchIcon = ({ className = "inline-block h-8 w-8 -mt-1" }: { className?: string }) => (
    <svg className={className} viewBox="0 0 32 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="32" height="20" rx="10" fill="#D1D5DB"/> {/* gray-300 */}
        <circle cx="10" cy="10" r="7" fill="white"/>
    </svg>
);

const PricingPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useAuth();
  const { userProfile } = useUser();
  const [isProcessing, setIsProcessing] = useState(false);
  const [notification, setNotification] = useState<{ message: string; type: 'success' | 'error' | null }>({
    message: '',
    type: null,
  });

  const paddleRef = useRef<any>(null);
  const [paddleReady, setPaddleReady] = useState(false);
  const { i18n } = useTranslation();

  // 显示临时通知的辅助函数
  const showTemporaryNotification = (message: string, type: 'success' | 'error') => {
    setNotification({
      message,
      type,
    });

    // 3秒后自动清除通知
    setTimeout(() => {
      setNotification({
        message: '',
        type: null,
      });
    }, 3000);
  };

  // 监听Paddle支付成功消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      log.debug('定价页面收到窗口消息:', event.data);
      
      // 处理直接的支付成功消息
      if (event.data && event.data.type === 'paddle_payment_success') {
        const source = event.data.source || 'unknown';
        log.info(`🎉 定价页面收到Paddle支付成功消息 (来源: ${source})，跳转到个人中心`);
        showTemporaryNotification(t('pricing.notifications.success'), 'success');
        
        // 跳转到个人中心并触发刷新
        setTimeout(() => {
          navigate('/profile?paddle_payment_success=true');
        }, 1500);
        return;
      }
      
      // 处理Paddle事件格式的消息
      if (event.data && event.data.action === 'event') {
        const eventName = event.data.event_name;
        const callbackData = event.data.callback_data;
        
        log.debug(`🎯 收到Paddle事件: ${eventName}`);
        
        // 处理支付完成事件
        if (eventName === 'checkout.completed') {
          log.info('🎉 检测到checkout.completed事件，支付成功！');
          showTemporaryNotification(t('pricing.notifications.success'), 'success');
          
          // 尝试关闭Paddle窗口
          setTimeout(() => {
            try {
              log.info('🔒 尝试关闭Paddle窗口');
              if (window.Paddle?.Checkout?.close) {
                window.Paddle.Checkout.close();
                log.info('✅ Paddle窗口已关闭');
              } else {
                log.warn('⚠️ Paddle.Checkout.close方法不可用');
              }
            } catch (error) {
              log.error('❌ 关闭Paddle窗口失败', error);
            }
          }, 3000); // 3秒后关闭窗口
          
          // 跳转到个人中心
          setTimeout(() => {
            navigate('/profile?paddle_payment_success=true');
          }, 3500); // 3.5秒后跳转，确保窗口先关闭
          return;
        }
        
        // 处理支付窗口关闭事件
        if (eventName === 'checkout.closed') {
          log.info('🔒 检测到checkout.closed事件');
          
          // 检查是否有订单数据（表示支付成功）
          if (callbackData && callbackData.data && callbackData.data.id) {
            log.info('🎉 支付窗口关闭且有订单ID，支付成功！');
            showTemporaryNotification(t('pricing.notifications.success'), 'success');
            
            // 跳转到个人中心
            setTimeout(() => {
              navigate('/profile?paddle_payment_success=true');
            }, 1500);
          } else {
            log.info('ℹ️ 支付窗口关闭但无订单ID，可能是用户取消了支付');
          }
          return;
        }
      }
    };
    
    window.addEventListener('message', handleMessage);
    
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [navigate, t]);

  // 工具函数：检查是否在试用期内
  const isInTrialPeriod = (subscriptionInfo: any) => {
    if (!subscriptionInfo) return false;

    // 方法1: 检查订阅状态
    if (subscriptionInfo.status === 'trialing') {
      return true;
    }

    // 方法2: 检查试用期结束时间
    const trialEndsAt = subscriptionInfo.trial_ends_at || subscriptionInfo.canceled_at;
    if (trialEndsAt) {
      try {
        const trialEndDate = new Date(trialEndsAt);
        const currentDate = new Date();
        return currentDate < trialEndDate;
      } catch (error) {
        log.error('解析试用期时间失败:', error);
        return false;
      }
    }

    return false;
  };


  const premiumFeatures = [
    { text: t('pricing.features.unlimitedGroups'), available: true },
    { text: t('pricing.features.unlimitedItems'), available: true },
    { text: t('pricing.features.unlimitedWebsites'), available: true },
    { text: t('pricing.features.unlimitedKeys'), available: true },
    { text: t('pricing.features.unlimitedRules'), available: true },
  ];

  const freeFeatures = [
    { text: t('pricing.features.limitedGroups'), available: true },
    { text: t('pricing.features.limitedItems'), available: true },
    { text: t('pricing.features.limitedWebsites'), available: true },
    { text: t('pricing.features.limitedKeys'), available: true },
    { text: t('pricing.features.limitedRules'), available: true },
  ];

  const getPaddleLocale = () => {
    if (i18n.language.startsWith('zh')) return 'zh-Hans';
    return 'en';
  };

  useEffect(() => {
    (async () => {
      try {
        paddleRef.current = await initializePaddle({
          token: PADDLE_CLIENT_TOKEN,
          environment: PADDLE_ENVIRONMENT as 'sandbox' | 'production'
        });
        setPaddleReady(true);
      } catch (e) {
        setPaddleReady(false);
        log.error('Paddle 初始化失败', e);
      }
    })();
  }, []);

  // 监听Paddle支付成功和关闭事件
  useEffect(() => {
    const onCheckoutComplete = (event: any) => {
      if (event?.detail?.event === 'checkout.completed') {
        alert('支付成功！');
      }
      if (event?.detail?.event === 'checkout.closed') {
        log.debug('支付弹窗已关闭');
      }
    };
    window.addEventListener('paddle-event', onCheckoutComplete);
    return () => window.removeEventListener('paddle-event', onCheckoutComplete);
  }, []);

  const handleSubscribe = async () => {
    if (!paddleRef.current) {
      alert('Paddle SDK 未初始化');
      return;
    }
    try {
      await paddleRef.current.Checkout.open({
        items: [{ priceId: PADDLE_PRODUCT_ID, quantity: 1 }],
        customer: { email: currentUser?.email || '' },
        customData: {
          userId: currentUser?.id || '',
          userEmail: currentUser?.email || '',
          plan: 'yearly',
          timestamp: Date.now()
        },
        settings: {
          displayMode: 'overlay',
          locale: getPaddleLocale()
        }
      });
    } catch (err: any) {
      alert('支付弹窗打开失败: ' + (err?.message || err));
    }
  };

  // 显示通知组件
  const Notification = () => {
    if (!notification.type) return null;

    return (
      <div
        className={`fixed top-4 right-4 p-4 rounded-md shadow-md ${
          notification.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}
      >
        <div className="flex items-center">
          {notification.type === 'success' ? (
            <CheckIcon className="h-5 w-5 mr-2 text-green-500" />
          ) : (
            <svg className="h-5 w-5 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          )}
          <span>{notification.message}</span>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-slate-50 dark:bg-slate-900 py-12 md:py-20">
      <div className="mx-auto max-w-5xl px-4">
        {/* 通知 */}
        <Notification />

        {/* Header Title */}
        <div className="text-center mb-12 md:mb-16">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl">
            {t('pricing.chooseYour')} <SwitchIcon /> Your
          </h1>
          <h2 className="text-4xl font-bold tracking-tight text-orange-500 sm:text-5xl mt-1">
            {t('pricing.subscriptionPlan')}
          </h2>

        </div>

        {/* Pricing Grid */}
        <div className="flex flex-col items-start gap-8 lg:flex-row lg:gap-6">
          {/* Left Column: Description & Illustration */}
          <div className="w-full lg:w-1/3 space-y-6 rounded-lg bg-purple-100/50 dark:bg-purple-900/30 p-6 md:p-8">
            <p className="text-lg text-gray-700 dark:text-gray-200">
              {t('pricing.description')}
            </p>
            <IllustrationPlaceholder />
          </div>

          {/* Middle Column: PREMIUM Plan */}
          <div className="w-full lg:w-1/3 rounded-xl border-2 border-orange-500 bg-white dark:bg-gray-800 p-6 shadow-xl md:p-8">
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">{t('pricing.premium.title')}</h3>
            <div className="mt-4 flex items-baseline gap-x-2">
              <span className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white">{t('pricing.premium.price')}</span>
              <span className="text-sm font-semibold leading-6 tracking-wide text-gray-600 dark:text-gray-300">{t('pricing.premium.period')}</span>
              <span className="ml-auto rounded-full bg-gray-100 px-3 py-1 text-xs font-medium text-gray-700 dark:bg-gray-700 dark:text-gray-200">{t('pricing.premium.trial')}</span>
            </div>
            
            {/* 试用期资格状态 */}
            {userProfile?.subscription_info && (() => {
              const trialStatus = getTrialStatusDescription(userProfile.subscription_info);
              if (!trialStatus.hasEligibility) {
                return (
                  <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-md">
                    <div className="flex items-start">
                      <svg className="w-4 h-4 text-red-600 dark:text-red-400 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <div className="text-sm text-red-800 dark:text-red-200">
                        <strong>试用期资格：</strong>由于之前取消过试用期，该账号已永久失去试用资格。重新订阅将直接付费。
                      </div>
                    </div>
                  </div>
                );
              }
              return null;
            })()}
            <button
              type="button"
              onClick={handleSubscribe}
              disabled={!paddleReady}
              className="mt-8 block w-full rounded-md bg-orange-500 px-3 py-3 text-center text-lg font-semibold text-white shadow-sm hover:bg-orange-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isProcessing
                ? t('common.processing')
                : t('pricing.premium.button')
              }
            </button>

            {/* 显示 Paddle 状态信息 */}
            {/* 移除 Paddle 错误提示 */}

            {/* 移除 Paddle 初始化提示 */}


            <ul className="mt-8 space-y-3 text-sm leading-6 text-gray-600 dark:text-gray-300">
              {premiumFeatures.map((feature) => (
                <li key={feature.text} className="flex gap-x-3 items-center">
                  <CheckIcon className="h-5 w-5 flex-none text-orange-500" aria-hidden="true" />
                  {feature.text}
                </li>
              ))}
            </ul>
          </div>

          {/* Right Column: FREE Plan */}
          <div className="w-full lg:w-1/3 rounded-xl bg-white dark:bg-gray-800/80 p-6 shadow-lg md:p-8">
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">{t('pricing.free.title')}</h3>
            <div className="mt-4 flex items-baseline gap-x-2">
              <span className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white">{t('pricing.free.price')}</span>
              <span className="text-sm font-semibold leading-6 tracking-wide text-gray-600 dark:text-gray-300">{t('pricing.free.period')}</span>
              <span className="ml-auto rounded-full bg-gray-100 px-3 py-1 text-xs font-medium text-gray-700 dark:bg-gray-700 dark:text-gray-200">{t('pricing.free.forever')}</span>
            </div>
            <div className="mt-8 block w-full rounded-md border border-gray-300 bg-white px-3 py-3 text-center text-lg font-semibold text-gray-900 shadow-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white">
              永久免费使用
            </div>

            <ul className="mt-8 space-y-3 text-sm leading-6 text-gray-600 dark:text-gray-300">
              {freeFeatures.map((feature) => (
                <li key={feature.text} className="flex gap-x-3 items-center">
                  {feature.available ? (
                    <CheckIcon className="h-5 w-5 flex-none text-blue-600" aria-hidden="true" />
                  ) : (
                    <LimitedFeatureIcon className="h-5 w-5 flex-none text-gray-400" aria-hidden="true" />
                  )}
                  {feature.text}
                </li>
              ))}
            </ul>
          </div>
        </div>


      </div>
    </div>
  );
};

export default PricingPage; 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle新产品测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .log { max-height: 400px; overflow-y: auto; }
        .product-comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0; }
        .product-box { padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .old-product { border-color: #dc3545; background-color: #fff8f8; }
        .new-product { border-color: #28a745; background-color: #f8fff9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Paddle新产品测试</h1>
        
        <div class="section success">
            <h2>🎉 新产品配置</h2>
            <p><strong>使用新创建的简化产品进行测试！</strong></p>
            
            <div class="product-comparison">
                <div class="product-box old-product">
                    <h3>❌ 旧产品（有问题）</h3>
                    <p><strong>ID:</strong> <code>pri_01k0xttmf9wekbg957y66xawj</code></p>
                    <p><strong>类型:</strong> $9.99/year</p>
                    <p><strong>试用期:</strong> 30天</p>
                    <p><strong>状态:</strong> 产品数据获取失败</p>
                </div>
                
                <div class="product-box new-product">
                    <h3>✅ 新产品（测试中）</h3>
                    <p><strong>ID:</strong> <code>pri_01k1ae8ecd1yj4njgwec3vvs13</code></p>
                    <p><strong>类型:</strong> 月度订阅</p>
                    <p><strong>试用期:</strong> 无</p>
                    <p><strong>状态:</strong> 待测试</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🧪 新产品测试</h2>
            <button onclick="testNewProduct()">1. 测试新产品ID</button>
            <button onclick="testNewProductMinimal()">2. 测试新产品（最简格式）</button>
            <button onclick="testNewProductFull()">3. 测试新产品（完整格式）</button>
            <button onclick="compareProducts()">4. 对比新旧产品</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section success" id="success-section" style="display: none;">
            <h2>🎉 新产品测试成功！</h2>
            <div id="success-details"></div>
        </div>

        <div class="section info" id="next-steps" style="display: none;">
            <h2>🚀 下一步操作</h2>
            <div id="next-steps-content"></div>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'test_459728092d35d36bc173c60e52d',
            oldProductId: 'pri_01k0xttmf9wekbg957y66xawj',  // 有问题的旧产品
            newProductId: 'pri_01k1ae8ecd1yj4njgwec3vvs13'   // 新创建的产品
        };

        let testResults = {
            newProduct: null,
            oldProduct: null
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('success-section').style.display = 'none';
            document.getElementById('next-steps').style.display = 'none';
        }

        function showSuccess(productData) {
            const successDiv = document.getElementById('success-details');
            successDiv.innerHTML = `
                <h3>✅ 产品数据获取成功！</h3>
                <ul>
                    <li><strong>价格ID:</strong> ${productData.price_id}</li>
                    <li><strong>产品ID:</strong> ${productData.product.id}</li>
                    <li><strong>产品名称:</strong> "${productData.product.name}"</li>
                    <li><strong>产品描述:</strong> "${productData.product.description}"</li>
                    <li><strong>数量:</strong> ${productData.quantity}</li>
                </ul>
            `;
            document.getElementById('success-section').style.display = 'block';
            
            showNextSteps();
        }

        function showNextSteps() {
            const nextStepsDiv = document.getElementById('next-steps-content');
            nextStepsDiv.innerHTML = `
                <h3>🔧 现在需要更新主应用配置：</h3>
                <ol>
                    <li><strong>更新环境变量：</strong>
                        <br>将 <code>.env.local</code> 中的产品ID改为：
                        <br><code>VITE_PADDLE_PRODUCT_YEARLY=${config.newProductId}</code>
                    </li>
                    <li><strong>重启开发服务器：</strong>
                        <br><code>npm run dev</code>
                    </li>
                    <li><strong>测试主应用：</strong>
                        <br>访问 <code>/pricing</code> 页面测试支付功能
                    </li>
                    <li><strong>如果成功：</strong>
                        <br>说明问题确实是旧产品的配置问题
                    </li>
                </ol>
            `;
            document.getElementById('next-steps').style.display = 'block';
        }

        async function initializePaddle() {
            return new Promise((resolve) => {
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            log(`❌ Checkout错误: ${data.detail}`, 'error');
                        } else if (data.type === 'checkout.loaded') {
                            log('✅ Checkout加载成功！', 'success');
                        } else if (data.name === 'checkout.closed' && data.data) {
                            const item = data.data.items?.[0];
                            if (item) {
                                log('🔍 产品数据分析:', 'info');
                                log(`- 价格ID: ${item.price_id || 'null'}`, item.price_id ? 'success' : 'error');
                                log(`- 产品ID: ${item.product?.id || 'null'}`, item.product?.id ? 'success' : 'error');
                                log(`- 产品名称: "${item.product?.name || ''}"`, item.product?.name ? 'success' : 'error');
                                log(`- 数量: ${item.quantity || 'null'}`, item.quantity ? 'success' : 'error');
                                
                                if (item.price_id && item.product?.id && item.product?.name) {
                                    log('🎉 产品数据完整！新产品配置成功！', 'success');
                                    testResults.newProduct = item;
                                    showSuccess(item);
                                } else {
                                    log('❌ 产品数据仍然不完整', 'error');
                                }
                            }
                        }
                    }
                });
                resolve();
            });
        }

        // 1. 测试新产品ID
        async function testNewProduct() {
            log('🧪 测试1: 新产品ID基本测试...', 'info');
            log(`使用新产品ID: ${config.newProductId}`, 'info');
            
            try {
                await initializePaddle();
                
                const testConfig = {
                    items: [{
                        priceId: config.newProductId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                log(`配置: ${JSON.stringify(testConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(testConfig);
                log('✅ 新产品测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 新产品测试失败: ${error.message}`, 'error');
            }
        }

        // 2. 测试新产品（最简格式）
        async function testNewProductMinimal() {
            log('🧪 测试2: 新产品最简格式...', 'info');
            
            try {
                await initializePaddle();
                
                const minimalConfig = {
                    items: [{
                        priceId: config.newProductId,
                        quantity: 1
                    }]
                };
                
                log(`最简配置: ${JSON.stringify(minimalConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(minimalConfig);
                log('✅ 新产品最简格式请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 新产品最简格式测试失败: ${error.message}`, 'error');
            }
        }

        // 3. 测试新产品（完整格式）
        async function testNewProductFull() {
            log('🧪 测试3: 新产品完整格式...', 'info');
            
            try {
                await initializePaddle();
                
                const fullConfig = {
                    items: [{
                        priceId: config.newProductId,
                        quantity: 1
                    }],
                    customer: {
                        email: "<EMAIL>"
                    },
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en-US"
                    },
                    customData: {
                        userId: "test123",
                        plan: "monthly",
                        source: "website"
                    }
                };
                
                log(`完整配置: ${JSON.stringify(fullConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(fullConfig);
                log('✅ 新产品完整格式请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 新产品完整格式测试失败: ${error.message}`, 'error');
            }
        }

        // 4. 对比新旧产品
        async function compareProducts() {
            log('🧪 测试4: 新旧产品对比测试...', 'info');
            
            try {
                await initializePaddle();
                
                // 先测试旧产品
                log('🔄 测试旧产品...', 'info');
                setTimeout(() => {
                    window.Paddle.Checkout.open({
                        items: [{ priceId: config.oldProductId, quantity: 1 }]
                    });
                    log(`旧产品ID: ${config.oldProductId}`, 'info');
                }, 1000);
                
                // 再测试新产品
                setTimeout(() => {
                    log('🔄 测试新产品...', 'info');
                    window.Paddle.Checkout.open({
                        items: [{ priceId: config.newProductId, quantity: 1 }]
                    });
                    log(`新产品ID: ${config.newProductId}`, 'info');
                }, 4000);
                
                log('✅ 对比测试已启动，请观察两次测试的差异', 'success');
                
            } catch (error) {
                log(`❌ 对比测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            log('🧪 Paddle新产品测试工具已加载', 'info');
            log(`🎯 测试新产品ID: ${config.newProductId}`, 'info');
            log('💡 这个产品没有试用期，应该能正常工作', 'success');
        };
    </script>
</body>
</html>

/**
 * 控制台调试命令工具
 * 在浏览器控制台中提供便捷的调试命令
 */

import { extensionMonitor } from './ExtensionMonitor';
import { cookieProtection } from './CookieProtection';
import { log } from './logger';

// 确保在全局范围内定义 showExtensionStatus
declare global {
  interface Window {
    showExtensionStatus?: () => void;
  }
}

// 定义全局调试命令
const debugCommands = {
  /**
   * 查看插件活动记录
   */
  showExtensionActivities: (count: number = 10) => {
    const activities = extensionMonitor.getRecentActivities(count);
    console.group(`🔍 最近 ${count} 条插件活动记录`);
    activities.forEach((activity, index) => {
      const time = new Date(activity.timestamp).toLocaleTimeString();
      const status = activity.success ? '✅' : '❌';
      log.info(`${index + 1}. [${time}] ${status} ${activity.type}: ${activity.message}`);
      if (activity.extensionId) {
        log.info(`   插件ID: ${activity.extensionId}`);
      }
      if (activity.data) {
        log.info('   数据:', activity.data);
      }
    });
    console.groupEnd();
    return activities;
  },

  /**
   * 查看插件统计信息
   */
  showExtensionStats: () => {
    const stats = extensionMonitor.getStatistics();
    console.group('📊 插件活动统计');
    log.info('总活动数:', stats.total);
    log.info('成功率:', `${stats.successRate.toFixed(1)}%`);
    log.info('最近活动:', stats.recentActivity);
    log.info('按类型统计:', stats.byType);
    log.info('按插件统计:', stats.byExtension);
    console.groupEnd();
    return stats;
  },

  /**
   * 查看当前插件状态
   */
  showExtensionStatus: () => {
    let allowedIds = 'Not configured';
    let environment = 'unknown';

    try {
      // 安全地访问import.meta.env
      const env = (globalThis as any).import?.meta?.env || (window as any).__VITE_ENV__ || {};
      allowedIds = env.VITE_ALLOWED_EXTENSION_IDS || 'Not configured';
      environment = env.MODE || 'unknown';
    } catch (error) {
      // 静默处理错误
    }

    const status = {
      extensionId: (window as any).extensionId || null,
      hasExtensionStorage: (window as any).hasExtensionStorage || false,
      allowedIds,
      environment
    };

    console.group('🔧 当前插件状态');
    log.info('插件ID:', status.extensionId || '未检测到');
    log.info('扩展存储可用:', status.hasExtensionStorage ? '是' : '否');
    log.info('允许的插件ID:', status.allowedIds);
    log.info('当前环境:', status.environment);
    console.groupEnd();
    return status;
  },

  /**
   * 查看本地存储数据
   */
  showLocalStorage: () => {
    const storage: Record<string, string> = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        storage[key] = localStorage.getItem(key) || '';
      }
    }
    
    console.group('💾 本地存储数据');
    Object.entries(storage).forEach(([key, value]) => {
      // 隐藏敏感信息
      if (key.includes('token') || key.includes('password')) {
        log.info(`${key}:`, '***隐藏***');
      } else {
        log.info(`${key}:`, value);
      }
    });
    console.groupEnd();
    return storage;
  },

  /**
   * 导出插件活动记录
   */
  exportActivities: () => {
    const data = extensionMonitor.exportActivities();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `extension-activities-${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    log.info('📁 插件活动记录已导出');
    return data;
  },

  /**
   * 清除插件活动记录
   */
  clearActivities: () => {
    extensionMonitor.clearActivities();
    log.info('🗑️ 插件活动记录已清除');
  },

  /**
   * 测试插件通信
   */
  testExtensionCommunication: () => {
    log.info('🧪 测试插件通信...');
    
    // 触发一个测试事件
    window.dispatchEvent(new CustomEvent('test-extension-communication', {
      detail: { timestamp: Date.now(), test: true }
    }));
    
    // 检查插件响应
    setTimeout(() => {
      const hasExtension = !!(window as any).extensionId;
      log.info('插件响应:', hasExtension ? '✅ 检测到插件' : '❌ 未检测到插件');
    }, 100);
  },

  /**
   * 监听插件活动（实时）
   */
  watchExtensionActivities: () => {
    log.info('👀 开始监听插件活动（输入 stopWatching() 停止）');
    
    const listener = (activity: any) => {
      const time = new Date(activity.timestamp).toLocaleTimeString();
      const status = activity.success ? '✅' : '❌';
      log.info(`[${time}] ${status} ${activity.type}: ${activity.message}`);
    };
    
    extensionMonitor.addListener(listener);
    
    // 添加停止监听的命令
    (window as any).stopWatching = () => {
      extensionMonitor.removeListener(listener);
      delete (window as any).stopWatching;
      log.info('⏹️ 已停止监听插件活动');
    };
  },

  /**
   * 查看Cookie保护状态
   */
  showCookieProtection: () => {
    const summary = cookieProtection.getProtectionSummary();
    console.group('🍪 Cookie保护状态');
    log.info('保护状态:', summary.enabled ? '✅ 已启用' : '❌ 已禁用');
    log.info('受保护的Cookie数量:', summary.totalProtected);
    log.info('受保护的Cookie列表:', summary.protectedCookies);
    log.info('允许的域名数量:', summary.totalAllowedDomains);
    log.info('允许的域名列表:', summary.allowedDomains);
    console.groupEnd();
    return summary;
  },

  /**
   * 测试Cookie保护
   */
  testCookieProtection: () => {
    cookieProtection.testProtection();
  },

  /**
   * 启用Cookie保护
   */
  enableCookieProtection: () => {
    cookieProtection.enableProtection();
    log.info('✅ Cookie保护已启用');
    log.info('⚠️ 注意：这可能会影响Paddle等第三方支付服务');
  },

  /**
   * 禁用Cookie保护
   */
  disableCookieProtection: () => {
    cookieProtection.disableProtection();
    log.info('❌ Cookie保护已禁用');
    log.info('✅ Paddle等第三方支付服务现在应该可以正常工作');
  },

  /**
   * 添加允许的域名
   */
  addAllowedDomain: (domain: string) => {
    if (!domain) {
      log.info('请提供域名，例如: addAllowedDomain("example.com")');
      return;
    }
    cookieProtection.addAllowedDomain(domain);
  },

  /**
   * 测试Paddle支付兼容性
   */
  testPaddleCompatibility: () => {
    console.group('🏪 Paddle支付兼容性测试');

    // 检查Paddle相关域名是否在允许列表中
    const summary = cookieProtection.getProtectionSummary();
    const paddleDomains = ['paddle.com', 'buy.paddle.com', 'checkout.paddle.com'];

    log.info('Paddle域名检查:');
    paddleDomains.forEach(domain => {
      const isAllowed = summary.allowedDomains.some(allowed => allowed.includes(domain));
      log.info(`  ${domain}: ${isAllowed ? '✅ 已允许' : '❌ 未允许'}`);
    });

    // 检查Paddle全局对象
    const hasPaddle = typeof (window as any).Paddle !== 'undefined';
    log.info('Paddle对象:', hasPaddle ? '✅ 已加载' : '❌ 未加载');

    // 模拟Cookie访问测试
    log.info('模拟Cookie访问测试...');
    const testCookie = 'paddle_test=test_value';
    document.cookie = testCookie;
    const canAccess = document.cookie.includes('paddle_test');
    log.info('Cookie访问:', canAccess ? '✅ 正常' : '❌ 受限');

    console.groupEnd();
  },

  /**
   * 显示帮助信息
   */
  help: () => {
    console.group('🆘 插件调试命令帮助');
    log.info('=== 插件活动监控 ===');
    log.info('showExtensionActivities(count?) - 查看插件活动记录');
    log.info('showExtensionStats() - 查看插件统计信息');
    log.info('showExtensionStatus() - 查看当前插件状态');
    log.info('testExtensionCommunication() - 测试插件通信');
    log.info('watchExtensionActivities() - 实时监听插件活动');
    log.info('');
    log.info('=== 数据访问 ===');
    log.info('showLocalStorage() - 查看本地存储数据');
    log.info('exportActivities() - 导出插件活动记录');
    log.info('clearActivities() - 清除插件活动记录');
    log.info('');
    log.info('=== Cookie保护 ===');
    log.info('showCookieProtection() - 查看Cookie保护状态');
    log.info('testCookieProtection() - 测试Cookie保护');
    log.info('enableCookieProtection() - 启用Cookie保护');
    log.info('disableCookieProtection() - 禁用Cookie保护');
    log.info('addAllowedDomain(domain) - 添加允许的域名');
    log.info('testPaddleCompatibility() - 测试Paddle支付兼容性');
    log.info('');
    log.info('help() - 显示此帮助信息');
    console.groupEnd();
  }
};

// 将 showExtensionStatus 函数添加到全局 window 对象
if (typeof window !== 'undefined') {
  window.showExtensionStatus = () => {
    log.info('showExtensionStatus() - 查看当前插件状态');
    // 这里可以添加插件状态检查逻辑
  };
}

// 延迟初始化调试命令，避免在模块顶层执行
const initializeDebugCommands = () => {
  try {
    if (typeof window !== 'undefined') {
      // 检查是否为开发环境（避免在模块顶层使用import.meta.env）
      const env = (globalThis as any).import?.meta?.env || (window as any).__VITE_ENV__ || {};
      const isDev = env.DEV || env.MODE === 'development';

      if (isDev) {
        Object.assign(window, debugCommands);
        log.info('🔧 插件调试命令已加载！输入 help() 查看可用命令');
      }
    }
  } catch (error) {
    // 静默处理错误，避免影响应用启动
  }
};

// 在DOM加载后初始化
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeDebugCommands);
  } else {
    setTimeout(initializeDebugCommands, 0);
  }
}

export default debugCommands;

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle产品验证</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .log { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; }
        .product-test { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Paddle产品验证</h1>
        
        <div class="section warning">
            <h2>🎯 问题确认</h2>
            <p><strong>JWT错误 + 产品数据为空</strong> = 产品ID或Token配置问题</p>
            <ul>
                <li>✅ 支付页面能打开（无CSP错误）</li>
                <li>❌ JWT获取失败</li>
                <li>❌ 产品数据为空</li>
            </ul>
        </div>

        <div class="section info">
            <h2>📋 当前配置验证</h2>
            <div class="product-test">
                <p><strong>Token:</strong> <code>test_27fb172b17e19acd4fbad97de75</code></p>
                <p><strong>当前产品ID:</strong> <code>pri_01k1aenmy30gyzrq9wayftn5z4</code></p>
                <p><strong>备用产品ID:</strong> <code>pri_01k1ae8ecd1yj4njgwec3vvs13</code></p>
            </div>
        </div>

        <div class="section">
            <h2>🧪 产品ID验证测试</h2>
            <button onclick="testCurrentProduct()">1. 测试当前产品ID</button>
            <button onclick="testBackupProduct()">2. 测试备用产品ID</button>
            <button onclick="testMinimalConfig()">3. 测试最简配置</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 验证日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section success" id="working-product" style="display: none;">
            <h2>🎉 找到有效产品！</h2>
            <div id="working-product-info"></div>
        </div>

        <div class="section error">
            <h2>🔧 如果所有产品都失败</h2>
            <ol>
                <li><strong>检查Token:</strong>
                    <ul>
                        <li>登录 <a href="https://sandbox-vendors.paddle.com/" target="_blank">Sandbox Dashboard</a></li>
                        <li>进入 Developer tools > Authentication > Client-side tokens</li>
                        <li>确认Token存在且Active</li>
                    </ul>
                </li>
                <li><strong>检查产品:</strong>
                    <ul>
                        <li>进入 Catalog > Products</li>
                        <li>确认产品存在且状态为Active</li>
                        <li>复制正确的价格ID</li>
                    </ul>
                </li>
                <li><strong>重新生成Token:</strong>
                    <ul>
                        <li>删除现有Token</li>
                        <li>创建新的Client-side Token</li>
                        <li>更新.env.local配置</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'test_27fb172b17e19acd4fbad97de75',
            currentProductId: 'pri_01k1aenmy30gyzrq9wayftn5z4',
            backupProductId: 'pri_01k1ae8ecd1yj4njgwec3vvs13'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '5px';
            logEntry.style.padding = '5px';
            logEntry.style.borderRadius = '3px';
            
            if (type === 'success') {
                logEntry.style.backgroundColor = '#d4edda';
                logEntry.style.color = '#155724';
            } else if (type === 'error') {
                logEntry.style.backgroundColor = '#f8d7da';
                logEntry.style.color = '#721c24';
            } else {
                logEntry.style.backgroundColor = '#d1ecf1';
                logEntry.style.color = '#0c5460';
            }
            
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('working-product').style.display = 'none';
        }

        function showWorkingProduct(productId) {
            const infoDiv = document.getElementById('working-product-info');
            infoDiv.innerHTML = `
                <h3>✅ 有效产品ID: <code>${productId}</code></h3>
                <p><strong>下一步:</strong> 更新.env.local中的VITE_PADDLE_PRODUCT_YEARLY为此产品ID</p>
                <pre>VITE_PADDLE_PRODUCT_YEARLY=${productId}</pre>
            `;
            document.getElementById('working-product').style.display = 'block';
        }

        async function initializePaddle() {
            return new Promise((resolve) => {
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            log(`❌ Checkout错误: ${data.detail}`, 'error');
                            
                            if (data.detail && data.detail.includes('JWT')) {
                                log('🔍 JWT错误 - Token或产品配置问题', 'error');
                            }
                        } else if (data.type === 'checkout.loaded') {
                            log('✅ 支付页面加载成功！', 'success');
                        } else if (data.name === 'checkout.closed' && data.data) {
                            const item = data.data.items?.[0];
                            if (item?.price_id && item?.product?.id && item?.product?.name) {
                                log('🎉 产品数据完整！找到有效产品！', 'success');
                                log(`产品ID: ${item.product.id}, 价格ID: ${item.price_id}, 名称: "${item.product.name}"`, 'success');
                                showWorkingProduct(lastTestedProductId);
                            } else {
                                log('❌ 产品数据仍然为空', 'error');
                                log(`price_id: ${item?.price_id || 'null'}, product.id: ${item?.product?.id || 'null'}`, 'error');
                            }
                        }
                    }
                });
                resolve();
            });
        }

        let lastTestedProductId = null;

        async function testCurrentProduct() {
            log('🧪 测试1: 当前产品ID...', 'info');
            lastTestedProductId = config.currentProductId;
            
            try {
                await initializePaddle();
                
                const testConfig = {
                    items: [{
                        priceId: config.currentProductId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                log(`测试产品ID: ${config.currentProductId}`, 'info');
                log(`配置: ${JSON.stringify(testConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(testConfig);
                log('✅ 当前产品测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 当前产品测试失败: ${error.message}`, 'error');
            }
        }

        async function testBackupProduct() {
            log('🧪 测试2: 备用产品ID...', 'info');
            lastTestedProductId = config.backupProductId;
            
            try {
                await initializePaddle();
                
                const testConfig = {
                    items: [{
                        priceId: config.backupProductId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                log(`测试备用产品ID: ${config.backupProductId}`, 'info');
                log(`配置: ${JSON.stringify(testConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(testConfig);
                log('✅ 备用产品测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 备用产品测试失败: ${error.message}`, 'error');
            }
        }

        async function testMinimalConfig() {
            log('🧪 测试3: 最简配置...', 'info');
            lastTestedProductId = config.currentProductId;
            
            try {
                await initializePaddle();
                
                const minimalConfig = {
                    items: [{
                        priceId: config.currentProductId,
                        quantity: 1
                    }]
                };
                
                log(`最简配置: ${JSON.stringify(minimalConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(minimalConfig);
                log('✅ 最简配置测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 最简配置测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示信息
        window.onload = function() {
            log('🔍 Paddle产品验证工具已加载', 'info');
            log('🎯 目标：找到能返回完整产品数据的有效产品ID', 'info');
            log('💡 如果所有产品都失败，需要检查Sandbox Dashboard配置', 'warning');
        };
    </script>
</body>
</html>

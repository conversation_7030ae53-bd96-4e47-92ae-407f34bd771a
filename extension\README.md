# AutoFill 扩展功能限制实现

## 概述

本实现为 AutoFill 扩展添加了付费版到期后的功能限制，确保免费用户只能使用限制范围内的功能，超出限制的功能将被禁用并提示升级。

## 功能限制

### 免费版限制
- 最多 3 个分组
- 每个分组最多 10 条记录
- 最多 3 个网站
- 每个网站最多 3 个子网站
- 每个子网站最多 10 条规则

### 限制实现

#### 1. 快速填充限制
- **右键菜单过滤**：只显示免费版限制内的预设内容
- **超出限制提示**：在右键菜单中显示升级提示
- **数据过滤**：自动过滤超出限制的数据

#### 2. 网站解析限制
- **解析权限检查**：点击解析按钮时检查订阅状态
- **限制提示**：显示升级提示并引导用户订阅
- **功能禁用**：免费用户无法使用网站解析功能

## 文件结构

```
extension/
├── background.js           # 后台脚本，处理右键菜单和权限检查
├── content-script.js       # 内容脚本，处理页面交互和限制提示
├── subscription-limits.js  # 订阅限制工具函数
├── website-parser.js       # 网站解析功能（演示用）
├── manifest.json          # 扩展清单文件
└── README.md              # 说明文档
```

## 核心功能

### 1. 订阅状态检查 (`subscription-limits.js`)
```javascript
checkSubscriptionStatus(callback)  // 检查用户是否为付费用户
getUserDataStats(callback)         // 获取用户数据统计
filterQuickFillData(data)          // 过滤快速填充数据
filterWebsiteRules(rules)          // 过滤网站规则数据
```

### 2. 右键菜单管理 (`background.js`)
- 动态创建右键菜单
- 根据订阅状态过滤显示内容
- 处理菜单点击事件
- 显示升级提示

### 3. 网站解析限制 (`website-parser.js`)
- 创建解析按钮和结果面板
- 权限检查和限制提示
- 解析表单字段信息

### 4. 限制提示 (`content-script.js`)
- 显示网站解析限制通知
- 处理内容填充
- 引导用户升级

## 使用方法

### 1. 安装扩展
1. 打开 Chrome 浏览器
2. 进入 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `extension` 文件夹

### 2. 测试功能
1. 访问测试页面：`http://localhost:5173/test-extension.html`
2. 测试快速填充：右键点击输入框查看菜单
3. 测试网站解析：点击页面右侧的"解析表单"按钮

### 3. 模拟不同用户状态
- **免费用户**：清空 `subscription_info` 存储
- **付费用户**：设置有效的 `subscription_info`
- **过期用户**：设置过期的 `subscription_info`

## 限制逻辑

### 快速填充限制
1. 检查用户订阅状态
2. 如果是免费用户，过滤数据：
   - 只保留前 3 个分组
   - 每个分组只保留前 10 条预设
3. 创建对应的右键菜单
4. 如有超出限制的内容，显示升级提示

### 网站解析限制
1. 用户点击解析按钮
2. 发送权限检查请求到后台脚本
3. 后台脚本检查订阅状态
4. 如果是免费用户且超出限制：
   - 返回限制信息
   - 显示升级提示通知
   - 禁用解析功能

## 升级引导

当用户触发限制时，系统会：
1. 显示友好的限制提示
2. 说明当前限制和升级后的权益
3. 提供升级按钮，跳转到定价页面
4. 引导用户完成订阅流程

## 技术特点

1. **无侵入性**：不影响现有功能的正常使用
2. **用户友好**：提供清晰的限制说明和升级引导
3. **实时检查**：动态检查订阅状态，及时应用限制
4. **数据安全**：使用安全存储，保护用户数据
5. **可扩展性**：易于添加新的限制规则

## 注意事项

1. 确保 `subscription_info` 数据格式正确
2. 限制检查依赖于本地存储的订阅信息
3. 网站解析功能仅为演示，实际使用需要完善
4. 建议在生产环境中添加更多的错误处理

## 测试场景

### 场景 1：免费用户正常使用
- 数据在限制范围内
- 所有功能正常可用
- 不显示任何限制提示

### 场景 2：免费用户超出限制
- 快速填充只显示限制内容
- 网站解析功能被禁用
- 显示升级提示和引导

### 场景 3：付费用户到期
- 从付费状态切换到免费限制
- 超出限制的功能被禁用
- 引导用户续费

### 场景 4：用户升级
- 从免费切换到付费状态
- 所有功能解锁
- 右键菜单显示完整内容

## 后续优化

1. 添加更精细的权限控制
2. 优化用户体验和提示文案
3. 增加使用统计和分析
4. 完善错误处理和日志记录
5. 支持更多的限制规则配置

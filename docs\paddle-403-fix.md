# Paddle 403错误修复指南

## 问题描述

部署到Cloudflare后出现Paddle支付错误：
```
POST https://sandbox-checkout-service.paddle.com/transaction-checkout 403 (Forbidden)
Failed to retrieve JWT
```

本地测试正常，但生产环境失败。

## 问题原因分析

### 1. **Token不完整**
生产环境的 `VITE_PADDLE_CLIENT_TOKEN` 不完整：
```bash
# 当前（不完整）
VITE_PADDLE_CLIENT_TOKEN=live_6534d079211be149aae16b81d86  # 只有26个字符

# 应该是（完整）
VITE_PADDLE_CLIENT_TOKEN=live_XXXXXXXXXXXXXXXXXXXXXXXXX  # live_ + 27个字符
```

### 2. **环境不匹配**
- 生产环境设置为 `production`
- 但token可能是测试环境的token
- 或者token不完整导致验证失败

### 3. **域名限制**
Cloudflare部署的域名可能未在Paddle控制台中配置

## 解决方案

### 🔧 临时解决方案（已实施）

1. **修改生产环境配置**：
   ```bash
   # .env.production
   VITE_PADDLE_ENVIRONMENT=sandbox  # 临时使用sandbox
   VITE_PADDLE_CLIENT_TOKEN=test_6534d079211be149aae16b81d86
   ```

2. **放宽Token验证**：
   - 修改了token验证逻辑，允许不完整的token
   - 让Paddle服务器来最终验证token有效性

### 🎯 永久解决方案

#### 步骤1：获取完整的Paddle Token

1. **登录Paddle控制台**：
   - 访问 [Paddle Dashboard](https://vendors.paddle.com/)

2. **获取正确的Token**：
   - 进入 **Developer Tools** → **Authentication**
   - 复制完整的Client Token
   - 格式应为：`live_XXXXXXXXXXXXXXXXXXXXXXXXX` (live_ + 27个字符)

3. **更新生产环境配置**：
   ```bash
   # .env.production
   VITE_PADDLE_ENVIRONMENT=production
   VITE_PADDLE_CLIENT_TOKEN=live_完整的27个字符token
   ```

#### 步骤2：配置域名白名单

1. **在Paddle控制台中**：
   - 进入 **Settings** → **Checkout Settings**
   - 在 **Allowed Domains** 中添加：
     - 您的Cloudflare域名
     - `*.pages.dev` (如果使用Cloudflare Pages)

#### 步骤3：验证产品ID

确保生产环境的产品ID正确：
```bash
VITE_PADDLE_PRODUCT_YEARLY=pri_01k0rfjdvjgsyrqxbf6sn6kk3w
```

## 测试方法

### 1. 本地测试
```bash
# 使用生产环境配置进行本地测试
npm run build
npm run preview
```

### 2. 生产环境测试
1. 部署到Cloudflare
2. 访问定价页面
3. 点击订阅按钮
4. 检查浏览器控制台是否还有403错误

### 3. 调试命令
在浏览器控制台中：
```javascript
// 查看当前Paddle配置
console.log('Paddle Environment:', import.meta.env.VITE_PADDLE_ENVIRONMENT);
console.log('Paddle Token:', import.meta.env.VITE_PADDLE_CLIENT_TOKEN?.substring(0, 10) + '...');
console.log('Product ID:', import.meta.env.VITE_PADDLE_PRODUCT_YEARLY);

// 测试Paddle兼容性
testPaddleCompatibility();
```

## 常见问题

### Q1: 为什么本地正常，生产环境失败？
**A**: 本地使用 `.env.local` 配置，生产环境使用 `.env.production` 配置。两个文件的token可能不同。

### Q2: 如何获取完整的Paddle Token？
**A**: 
1. 登录Paddle控制台
2. 进入 Developer Tools → Authentication
3. 复制完整的Client Token（应该是live_开头+27个字符）

### Q3: 403错误还有其他可能原因吗？
**A**: 是的，可能的原因包括：
- 域名未在Paddle白名单中
- 产品ID不存在或无效
- Paddle账户权限问题
- 网络或CORS问题

### Q4: 如何确认token是否有效？
**A**: 在浏览器控制台中运行：
```javascript
fetch('https://api.paddle.com/products', {
  headers: {
    'Authorization': 'Bearer ' + 'your_token_here'
  }
}).then(r => console.log('Token status:', r.status));
```

## 检查清单

部署前请确认：

- [ ] `.env.production` 中的token是完整的（live_ + 27个字符）
- [ ] 环境设置正确（production 或 sandbox）
- [ ] 产品ID存在且有效
- [ ] 域名已添加到Paddle白名单
- [ ] 本地测试通过
- [ ] Cookie保护已禁用（`VITE_ENABLE_COOKIE_PROTECTION=false`）

## 联系支持

如果问题仍然存在：

1. **检查Paddle状态页面**：https://status.paddle.com/
2. **联系Paddle支持**：通过控制台提交支持票据
3. **查看Paddle文档**：https://developer.paddle.com/

## 更新日志

- **2025-01-30**: 临时修改为sandbox环境，放宽token验证
- **待完成**: 获取完整的live token，恢复production环境

/*
 * 按照官方文档实现的 Paddle 服务
 * 参考: https://developer.paddle.com/paddlejs/overview
 */

import { useEffect, useState, useCallback } from 'react';
import { PADDLE_CLIENT_TOKEN, PADDLE_ENVIRONMENT, PADDLE_PRODUCT_ID } from '../config/paddle';
import { log } from '../utils/logger';

// 移除模拟支付，直接使用Paddle测试环境

// 全局类型声明 - 按照官方文档
declare global {
  interface Window {
    Paddle?: {
      Initialize: (config: {
        token: string;
        checkout?: {
          settings?: {
            displayMode?: string;
            theme?: string;
            locale?: string;
          };
        };
        eventCallback?: (data: any) => void;
      }) => void;
      Environment?: {
        set: (env: string) => void;
      };
      Checkout: {
        open: (config: {
          items: Array<{
            priceId: string;
            quantity: number;
          }>;
          customer?: {
            email?: string;
            id?: string;
          };
          customData?: any;
          // 直接在根级别设置URL，不在settings对象中
          successUrl?: string;
          cancelUrl?: string;
          displayMode?: "overlay" | "inline";
          theme?: "light" | "dark";
          locale?: string;
          settings?: {
            displayMode?: "overlay" | "inline";
            theme?: "light" | "dark";
            locale?: string;
            successUrl?: string;
            frameTarget?: string;
          };
        }) => void;
        close: () => void; // 添加 close 方法定义
      };
      PricePreview: (config: {
        items: Array<{
          priceId: string;
          quantity: number;
        }>;
      }) => Promise<any>;
      Setup: (config: {
        container: string | HTMLElement;
        items: Array<{
          priceId: string;
          quantity: number;
        }>;
      }) => void;
    };
  }
}

// 按照官方文档实现的Paddle服务类
class OfficialPaddleService {
  private initialized = false;
  private scriptLoaded = false;
  private eventCallback?: (data: any) => void;

  /**
   * 验证token格式 - 按照官方文档（宽松验证）
   */
  private validateToken(): boolean {
    // 如果没有token，使用默认的测试token
    if (!PADDLE_CLIENT_TOKEN || PADDLE_CLIENT_TOKEN.length < 10) {
      log.warn('未配置有效的Paddle token，将使用默认测试配置');
      return false;
    }

    // 宽松的token验证：只检查前缀和最小长度
    const tokenRegex = /^(test|live)_[a-zA-Z0-9]{20,}$/;
    const isValid = tokenRegex.test(PADDLE_CLIENT_TOKEN);

    log.debug('Token验证', {
      token: PADDLE_CLIENT_TOKEN.substring(0, 10) + '...',
      length: PADDLE_CLIENT_TOKEN.length,
      format: isValid ? '正确' : '错误',
      environment: PADDLE_ENVIRONMENT
    });

    if (!isValid) {
      log.warn('Token格式可能不完整，但将尝试继续初始化');
    }

    // 即使token格式不完整，也允许继续（让Paddle服务器来验证）
    return PADDLE_CLIENT_TOKEN.length > 10;
  }

  /**
   * 加载Paddle.js脚本 - 按照官方文档
   */
  private async loadScript(): Promise<void> {
    if (this.scriptLoaded) {
      return;
    }

    // 检查是否已经加载
    if (document.querySelector('script[src*="paddle.js"]')) {
      log.debug('📦 Paddle.js 脚本已存在');
      this.scriptLoaded = true;
      return;
    }

    log.debug('📦 加载 Paddle.js 脚本...');
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://cdn.paddle.com/paddle/v2/paddle.js';
      script.onload = () => {
        log.debug('✅ Paddle.js 脚本加载成功');
        this.scriptLoaded = true;
        resolve();
      };
      script.onerror = () => {
        log.error('❌ Paddle.js 脚本加载失败');
        reject(new Error('Failed to load Paddle.js'));
      };
      document.head.appendChild(script);
    });
  }

  /**
   * 等待Paddle对象可用
   */
  private async waitForPaddle(): Promise<void> {
    return new Promise((resolve, reject) => {
      let attempts = 0;
      const maxAttempts = 50;

      const checkPaddle = () => {
        if (window.Paddle) {
          log.debug('✅ Paddle 对象已可用');
          resolve();
        } else if (attempts >= maxAttempts) {
          log.error('❌ Paddle 对象加载超时');
          reject(new Error('Paddle not available after timeout'));
        } else {
          attempts++;
          setTimeout(checkPaddle, 100);
        }
      };

      checkPaddle();
    });
  }

  /**
   * 公共方法：加载SDK
   */
  async loadSDK(): Promise<void> {
    await this.loadScript();
  }

  /**
   * 公共方法：设置事件回调
   */
  setEventCallback(callback: (data: any) => void): void {
    this.eventCallback = callback;
  }

  /**
   * 初始化Paddle - 按照官方文档
   */
  async initialize(token?: string): Promise<void> {
    if (this.initialized) {
      log.debug('✅ Paddle 已初始化，跳过');
      return;
    }

    // 1. 验证token格式
    if (!this.validateToken()) {
      throw new Error('Invalid Paddle token format. Expected format: test_XXXXXXXXXXXXXXXXXXXXXXXXX or live_XXXXXXXXXXXXXXXXXXXXXXXXX');
    }

    // 2. 加载脚本
    await this.loadScript();

    // 3. 等待Paddle对象可用
    await this.waitForPaddle();

    // 4. 按照官方文档初始化
    log.debug('🚀 初始化 Paddle...');
    try {
      // 按照官方文档的简洁格式
      const initConfig: any = {
        token: token || PADDLE_CLIENT_TOKEN,
        eventCallback: (data: any) => {
          log.debug('📊 Paddle事件:', data);
          
          // Paddle Billing v2 的正确事件处理
          // 根据官方文档，主要依赖URL重定向而不是这些事件
          if (data.name === 'checkout.loaded') {
            log.debug('✅ 支付页面已加载');
          }
          
          if (data.name === 'checkout.customer.created') {
            log.debug('👤 客户信息已创建');
          }
          
          if (data.name === 'checkout.payment.initiated') {
            log.debug('💳 支付已发起');
          }
          
          if (data.name === 'checkout.completed') {
            log.debug('🎉 支付已完成！发送成功通知');
            // 发送消息通知支付成功
            window.postMessage({ 
              type: 'paddle_payment_success',
              source: 'checkout_completed',
              data: data.data 
            }, '*');
            
            // 延迟关闭Paddle窗口
            setTimeout(() => {
              try {
                log.debug('🔒 尝试关闭Paddle窗口');
                if (window.Paddle?.Checkout?.close) {
                  window.Paddle.Checkout.close();
                  log.debug('✅ Paddle窗口已关闭');
                } else {
                  log.debug('⚠️ Paddle.Checkout.close方法不可用');
                }
              } catch (error) {
                log.error('❌ 关闭Paddle窗口失败:', error);
              }
            }, 3000); // 3秒后关闭，给用户时间看到成功消息
          }
          
          if (data.name === 'checkout.closed') {
            log.debug('🔒 支付窗口已关闭');
            // 如果有订单数据，说明支付成功
            if (data.data && data.data.id) {
              log.debug('🎉 支付窗口关闭且有订单ID，确认支付成功！');
              window.postMessage({ 
                type: 'paddle_payment_success',
                source: 'checkout_closed',
                orderId: data.data.id,
                data: data.data 
              }, '*');
            }
          }
          
          // 如果有自定义事件回调，也调用它
          if (this.eventCallback) {
            this.eventCallback(data);
          }
        }
      };

      window.Paddle!.Initialize(initConfig);

      log.debug('✅ Paddle 初始化成功');
      this.initialized = true;
    } catch (error) {
      log.error('❌ Paddle 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 打开支付弹窗 - 按照官方文档
   */
  async openCheckout(): Promise<void> {
    try {
      log.debug('🔧 开始初始化Paddle...');
      await this.initialize();

      if (!window.Paddle?.Checkout?.open) {
        throw new Error('Paddle Checkout not available');
      }

      log.debug('🚀 准备打开支付弹窗，配置:', {
        token: PADDLE_CLIENT_TOKEN.substring(0, 10) + '...',
        productId: PADDLE_PRODUCT_ID,
        environment: PADDLE_ENVIRONMENT
      });

      // 按照官方文档打开支付弹窗 - 使用自定义成功页面
      window.Paddle.Checkout.open({
        items: [{
          priceId: PADDLE_PRODUCT_ID,
          quantity: 1
        }],
        settings: {
          displayMode: 'overlay',
          theme: 'light',
          locale: 'en'
        }
      });

      log.debug('✅ 支付弹窗调用成功');
    } catch (error) {
      log.error('❌ 支付弹窗打开失败:', error);

      // 根据错误类型显示不同的提示
      if (error instanceof Error) {
        let errorMessage = '支付系统初始化失败，请刷新页面重试';

        if (error.message.includes('403') || error.message.includes('Forbidden')) {
          errorMessage = `🔧 Paddle配置问题检测到！

❌ 403 Forbidden错误通常是配置问题：

📋 请按以下步骤修复：

1️⃣ 登录Paddle Sandbox Dashboard:
   https://sandbox-vendors.paddle.com

2️⃣ 添加域名白名单:
   Settings > Checkout settings > Allowed domains
   添加: localhost:5173

3️⃣ 检查Token权限:
   Developer tools > Authentication
   确认token状态为Active

4️⃣ 验证产品配置:
   Catalog > Products
   确认产品ID存在且为Active状态

当前配置:
- 域名: ${window.location.hostname}:${window.location.port}
- Token: ${PADDLE_CLIENT_TOKEN.substring(0, 10)}...
- 产品ID: ${PADDLE_PRODUCT_ID}

💡 配置完成后刷新页面重试`;
        } else if (error.message.includes('token') || error.message.includes('Invalid')) {
          errorMessage = `Token配置错误：
当前token: ${PADDLE_CLIENT_TOKEN.substring(0, 10)}...
请确认token格式正确且有效`;
        }

        // 显示详细的错误信息
        const shouldShowAlert = confirm(errorMessage + '\n\n点击"确定"查看详细错误信息，点击"取消"关闭');
        if (shouldShowAlert) {
          log.error('详细错误信息:', error);
        }
      }

      throw error;
    }
  }

  /**
   * 设置嵌入式支付页面
   */
  async setupInlineCheckout(containerId: string): Promise<void> {
    try {
      log.debug('🔧 开始初始化Paddle嵌入式支付...');
      await this.initialize();

      const container = document.getElementById(containerId);
      if (!container) {
        throw new Error(`Container with id "${containerId}" not found`);
      }

      log.debug('🚀 准备设置嵌入式支付，配置:', {
        token: PADDLE_CLIENT_TOKEN.substring(0, 10) + '...',
        productId: PADDLE_PRODUCT_ID,
        environment: PADDLE_ENVIRONMENT,
        containerId: containerId
      });



      // 使用Paddle的嵌入式支付
      if (!window.Paddle?.Setup) {
        throw new Error('Paddle Setup not available');
      }

      // 清空容器
      container.innerHTML = '<div id="paddle-checkout-container"></div>';

      // 设置嵌入式支付
      window.Paddle.Setup({
        container: '#paddle-checkout-container',
        items: [{
          priceId: PADDLE_PRODUCT_ID,
          quantity: 1
        }]
      });

      log.debug('✅ 嵌入式支付设置成功');

    } catch (error) {
      log.error('❌ 嵌入式支付设置失败:', error);

      const container = document.getElementById(containerId);
      if (container) {
        container.innerHTML = `
          <div style="
            border: 2px solid #dc3545;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #f8d7da;
            color: #721c24;
            margin: 20px 0;
          ">
            <h3>❌ 支付系统加载失败</h3>
            <p>请刷新页面重试，或联系客服支持。</p>
            <button
              onclick="window.location.reload()"
              style="
                background: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
              "
            >
              🔄 刷新页面
            </button>
          </div>
        `;
      }

      throw error;
    }
  }
}

// 创建全局实例
const paddleService = new OfficialPaddleService();

// 导出函数
export const openPaddleCheckout = () => paddleService.openCheckout();
export const setupInlineCheckout = (containerId: string) => paddleService.setupInlineCheckout(containerId);

// React Hook for Paddle
export const usePaddle = (config?: {
  token?: string;
  eventCallback?: (data: any) => void;
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 初始化Paddle
  useEffect(() => {
    const initializePaddle = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 加载Paddle SDK
        await paddleService.loadSDK();

        // 初始化Paddle
        await paddleService.initialize(config?.token || PADDLE_CLIENT_TOKEN);

        // 设置事件回调 - 合并默认回调和用户自定义回调
        if (config?.eventCallback) {
          const originalCallback = config.eventCallback;
          paddleService.setEventCallback((data: any) => {
            // Paddle Billing v2 的正确事件处理
            if (data.name === 'checkout.payment.completed') {
              log.debug('🎉 usePaddle检测到支付完成事件');
              window.postMessage({ type: 'paddle_payment_success' }, '*');
            }
            // 调用用户自定义回调
            originalCallback(data);
          });
        }

        setIsInitialized(true);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '初始化失败';
        setError(errorMessage);
        log.error('Paddle初始化失败:', err);
      } finally {
        setIsLoading(false);
      }
    };

    initializePaddle();
  }, [config?.token]);

  // 打开支付页面
  const checkout = useCallback(async (checkoutConfig: {
    items: Array<{
      priceId: string;
      quantity: number;
    }>;
    customer?: {
      email?: string;
    };
    customData?: any;
    settings?: {
      displayMode?: 'overlay' | 'inline';
      theme?: 'light' | 'dark';
      locale?: string;
      successUrl?: string;
      frameTarget?: string;
    };
  }) => {
    try {
      if (!isInitialized) {
        throw new Error('Paddle未初始化');
      }
      
      // 构建完整的配置 - Paddle Billing v2 正确格式
      const fullConfig = {
        items: checkoutConfig.items,
        customer: checkoutConfig.customer,
        customData: checkoutConfig.customData,
        settings: {
          displayMode: checkoutConfig.settings?.displayMode || 'overlay',
          theme: checkoutConfig.settings?.theme || 'light',
          locale: checkoutConfig.settings?.locale || 'en'
        }
      };
      
      // 调用Paddle Checkout
      if (window.Paddle?.Checkout?.open) {
        window.Paddle.Checkout.open(fullConfig);
      } else {
        throw new Error('Paddle Checkout不可用');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '支付页面打开失败';
      setError(errorMessage);
      throw err;
    }
  }, [isInitialized]);

  return {
    isInitialized,
    error,
    isLoading,
    checkout
  };
};

export default paddleService;

const translation = {
  // 通用
  common: {
    loading: "Loading...",
    refresh: "Refresh",
    logout: "Logout",
    login: "Login",
    processing: "Processing...",
    error: "Error",
    success: "Success",
  },
  
  // 导航
  nav: {
    home: "Home",
    features: "Features",
    pricing: "Pricing",
    about: "About",
    profile: "Profile",
  },
  
  // 页脚
  footer: {
    description: "Intelligent auto-fill browser extension that boosts your web operation efficiency and eliminates repetitive typing.",
    services: "Services",
    contactUs: "Contact Us",
    changelog: "Changelog",
    about: "About",
    userAgreement: "User Agreement",
    privacyPolicy: "Privacy Policy",
    refundPolicy: "Refund Policy",
    followUs: "Follow Us",
    copyright: "Copyright 2025. AutoFill. All rights reserved.",
  },
  
  // 首页
  home: {
    title: "Install AutoFill Assistant",
    subtitle: "Boost your input efficiency by 80%",
    description: "Say goodbye to repetitive typing with one-click smart filling - a web efficiency revolution! Precise and fast content matching input, works on all websites, completing complex forms in seconds, allowing you to focus on more valuable work.",
    getStarted: "Get AutoFill Assistant",
    features: {
      fast: "Fast",
      fastDesc: "Save time and effort, efficiency soars",
      accurate: "Accurate",
      accurateDesc: "Smart recognition, error-free",
      flexible: "Flexible",
      flexibleDesc: "Customizable to meet your needs",
      safe: "Safe",
      safeDesc: "Secure and easy to use",
    },
    faq: {
      title: "Frequently Asked Questions",
      questions: [
        {
          question: "How do I install and use the extension?",
          answer: "1) Search for the extension name in Chrome/Firefox/Edge store; 2) Click 'Add to Browser'; 3) After installation, click the browser toolbar icon to open the management interface; 4) Start adding preset content or configure website filling rules. Link to detailed tutorial page."
        },
        {
          question: "Which browsers are supported?",
          answer: "Chrome and Edge are already available, Firefox and Safari are in development. If you need us to support other browsers, please contact us ()."
        },
        {
          question: "How does the 'Website Fill Management' feature identify input fields? Is it accurate?",
          answer: "We primarily identify target input fields by recognizing the fixed label text (K) before them. For well-structured forms with clear labels, the accuracy is very high. For special or dynamically generated forms, manual confirmation or rule adjustment may be needed."
        },
        {
          question: "Are my preset data and website configurations secure? Where are they stored?",
          answer: "User data security is our top priority. All preset content and website configurations are stored locally in your browser by default. We also offer cloud synchronization backup, which is optional. Data is strongly encrypted during transmission and storage, and we will never use your data for any other purposes. Please link to detailed privacy policy."
        },
        {
          question: "Who is this extension suitable for?",
          answer: "It's perfect for people with the following work needs: Users who frequently fill out forms (registrations, shopping, expense reports, applications). Users who need to enter the same information on different websites (such as addresses, contact information). Customer service, sales, and data entry positions requiring quick input of standard responses or information. Developers and testers who need to fill test data. Anyone looking to improve web operation efficiency and reduce repetitive work."
        },
        {
          question: "Is it free? Are there premium features?",
          answer: "The basic version is free and includes core quick fill management and website fill management features that meet most user needs. We offer a premium/professional version (optional) with advanced features such as: cloud synchronization, ad removal (ad feature not yet online), support for more websites and groups, etc."
        }
      ]
    }
  },
  
  // 关于页面
  about: {
    title: "About Us",
    content: "This is placeholder content for the About Us page. Actual team and company introduction content will be added later.",
  },
  
  // 功能页面
  features: {
    title: "Features",
    subtitle: "Making form filling easy and simple",
    description: "AutoFill Assistant provides a variety of powerful features to help you quickly and accurately enter information on different websites, greatly improving your work efficiency.",
    quickFill: {
      title: "Quick Fill",
      description: "Preset commonly used content, input with one right-click, never type the same information repeatedly.",
      features: [
        "Support for unlimited preset entries",
        "Group management for easy content organization",
        "Quick access via right-click menu",
        "Support for plain text, formatted text, and rich text"
      ]
    },
    siteFill: {
      title: "Website Fill",
      description: "Intelligently recognizes web form structures and automatically fills form content based on keyword matching.",
      features: [
        "Automatically identify input fields through label text (K)",
        "Auto-match and fill corresponding content (V)",
        "Support for complex forms and multi-page forms",
        "High-accuracy intelligent matching algorithm"
      ]
    },
    management: {
      title: "Easy Management",
      description: "Intuitive user interface to easily manage all your preset content and filling rules.",
      features: [
        "Drag-and-drop group and item management",
        "Search and filter functionality",
        "One-click import/export of configurations",
        "Create and edit website rules"
      ]
    },
    security: {
      title: "Security Guarantee",
      description: "Your data security is our top priority, all data is stored locally by default.",
      features: [
        "Local-first storage policy",
        "Optional encrypted cloud synchronization",
        "Strict data privacy protection",
        "Complete control over your data"
      ]
    },
    compatibility: {
      title: "Wide Compatibility",
      description: "Supports mainstream browsers and various websites, ensuring you can use it anywhere.",
      features: [
        "Supports Chrome and Edge browsers",
        "Firefox and Safari coming soon",
        "Adapts to various form structures",
        "Continuous updates to support more websites"
      ]
    },
    content: "AutoFill Assistant is your powerful ally for improving web input efficiency, saying goodbye to repetitive typing and focusing on more valuable work."
  },
  
  // 定价页面
  pricing: {
    chooseYour: "Choose Your",
    subscriptionPlan: "Subscription Plan",
    description: "AutoFill helps you quickly fill in forms and save time by automatically entering your information on websites.",
    premium: {
      title: "PREMIUM",
      price: "$9.99",
      period: "/year",
      trial: "30 day trial",
      button: "Get Premium (Annual)"
    },
    free: {
      title: "FREE",
      price: "$0",
      period: "/year",
      forever: "Forever Free",
      button: "Continue with Free Version"
    },
    features: {
      cloudSync: "Cloud Sync",
      dataPrivacy: "Data Privacy Protection",
      unlimitedGroups: "Unlimited Groups",
      unlimitedItems: "Unlimited Presets per Group",
      unlimitedWebsites: "Unlimited Websites",
      unlimitedKeys: "Unlimited Subpages per Website",
      unlimitedRules: "Unlimited Rules per Subpage",
      devices: "Multi-device Sync",
      limitedGroups: "Up to 3 Groups",
             limitedItems: "10 Records per Group",
      limitedWebsites: "Up to 3 Websites",
             limitedKeys: "3 Subpages per Website",
             limitedRules: "10 Rules per Subpage"
    },
    notifications: {
      success: "Subscription successful! Thank you for your support!",
      cancelled: "Subscription cancelled",
      error: "Error processing subscription. Please try again later.",
      alreadySubscribed: "You are already subscribed and cannot pay again. Thank you for your support!"
    },
    limits: {
      quickFillLimited: "This preset content is limited, please upgrade to subscription user for full functionality",
      websiteParseLimited: "This website parsing feature is limited, please upgrade to subscription user",
      upgradePrompt: "Upgrade to Premium to unlock all features",
      upgradeButton: "Upgrade Now"
    }
  },
  
  // 登录页面
  login: {
    title: "Welcome Back",
    subtitle: "Sign in to your account",
    quickLogin: "Quick Login",
    quickLoginSubtitle: "Verify your identity to continue",
    email: "Email Address",
    emailPlaceholder: "Enter your email address",
    verificationCode: "Verification Code",
    verificationCodePlaceholder: "Enter 6-digit verification code",
    getVerificationCode: "Get Verification Code",
    resendVerificationCode: "Resend Verification Code",
    sending: "Sending...",
    verifying: "Verifying...",
    loginButton: "Sign In",
    loginInProgress: "Signing in...",
    codeSent: "Verification code sent, please check your email",
    codeAlreadySent: "Verification code already sent, please check your email or wait before retrying",
    backToEmail: "Back to Edit",
    secureLogin: "Secure Login",
    autoRegisterNote: "Login with verification code. If account doesn't exist, it will be automatically registered",
    secureEncryption: "Secure Encryption",
    privacyProtection: "Privacy Protection",
    countdownText: "seconds until you can resend",
    agreement: {
      text: "I have read and agree to the",
      userAgreement: "User Agreement",
      and: "and",
      privacyPolicy: "Privacy Policy",
      mustAgree: "Please agree to the User Agreement and Privacy Policy first"
    },
    errors: {
      emailRequired: "Please enter your email address",
      codeRequired: "Please enter verification code",
      emailAndCodeRequired: "Please enter email address and verification code",
      sendCodeFailed: "Failed to send verification code, please try again later",
      loginFailed: "Login failed, please check your verification code and try again",
      agreementRequired: "Please agree to the User Agreement and Privacy Policy first"
    }
  },

  // 个人中心页面
  profile: {
    title: "My Account",
    welcome: "Welcome Back",
    memberSince: "Member Since",
    lastLogin: "Last Login",
    basic: {
      title: "Basic Information",
      userId: "User ID",
      email: "Account",
      notSet: "Not set",
      joinDate: "Join Date",
      accountType: "Account Type",
      status: "Account Status",
      active: "Active",
      verified: "Verified"
    },
    subscription: {
      title: "Subscription Info",
      status: "Subscription Status",
      subscribed: "Subscribed",
      active: "Active",
      abnormal: "Status abnormal",
      paused: "Paused",
      cancelled: "Cancelled",
      past_due: "Payment Past Due",
      unknown: "Status Unknown",
      nextBilling: "Next Billing Date",
      startDate: "Subscription Start Date",
      updatePayment: "Update Payment Method",
      cancel: "Cancel Subscription",
      cancelSubscription: "Cancel",
      cancelling: "Cancelling...",
      daysRemaining: "{days} days remaining to cancel",
      trialCanCancel: "Trial can be cancelled anytime",
      notSubscribed: "Not Subscribed",
      noActiveSubscription: "No Active Subscription",
      viewPlans: "View Plans",
      subscribe: "Subscribe Now",
      subscribePlan: "Subscribe Plan",
      goSubscribe: "Go Subscribe",
      plan: "Current Plan",
      subtitle: "Choose the plan that suits you, enjoy a better user experience",
      recommended: "Recommended",
      premium: "Premium",
      free: "Free",
      features: {
        unlimitedGroups: "Unlimited Groups",
        unlimitedWebsites: "Unlimited Websites",
        unlimitedSubpages: "Unlimited Subpages per Website",
        unlimitedRules: "Unlimited Rules per Subpage",
        unlimitedPresets: "Unlimited Presets per Group"
      },
      freeFeatures: {
        limitedGroups: "Up to 3 Groups",
        limitedPresets: "10 Records per Group",
        limitedWebsites: "Up to 3 Websites",
        limitedSubpages: "3 Subpages per Website",
        limitedRules: "10 Rules per Subpage"
      },
      billing: {
        nextBillingDate: "Next billing date",
        billingDescription: "You will be charged $9.99 on each renewal until you cancel it by submitting a help desk request. If you cancel, previous charges will not be refunded, but you may continue to use the service until the end of the term you paid for.",
        yearSuffix: "/ year",
        monthSuffix: "/ month"
      },
      labels: {
        plan: "Plan",
        premium: "PREMIUM",
        free: "FREE",
        freePlan: "Free Plan"
      },
      freePlan: {
        price: "$0",
        forever: "Forever Free",
        description: "Basic features for individual users"
      },
      premiumPlan: {
        price: "$9.99",
        description: "Best choice for professional users"
      }
    },
    stats: {
      title: "Usage Statistics",
      totalLogins: "Total Logins",
      daysActive: "Active Days",
      lastActive: "Last Active",
      dataUsage: "Data Usage",
      featuresUsed: "Features Used"
    },
    quickActions: {
      title: "Quick Actions",
      manageSubscription: "Manage Subscription",
      downloadData: "Download Data",
      contactSupport: "Contact Support",
      viewDocumentation: "View Documentation",
      reportIssue: "Report Issue",
      shareApp: "Share App"
    },
    security: {
      title: "Security Settings",
      changePassword: "Change Password",
      twoFactor: "Two-Factor Authentication",
      loginDevices: "Login Devices",
      privacySettings: "Privacy Settings",
      dataExport: "Data Export",
      deleteAccount: "Delete Account"
    },
    loading: "Loading user information...",
    reload: "Reload",
    refresh: "Refresh",
    refreshSuccess: "Refresh successful",
    logout: "Logout"
  },
  
  // 设置页面
  settings: {
    general: "General Settings",
    accountStatus: "Account Status",
    notLoggedIn: "Not Logged In",
    loggedIn: "Logged In",
    refresh: "Refresh",
    logout: "Logout"
  },
  
  // 错误显示
  error: {
    refreshPage: "Refresh Page",
    apiConfig: {
      title: "API Configuration Error",
      checkEnv: "Please check your environment variables to ensure the correct API URL and API key are set.",
      createFile: "Create a .env.local file in the project root directory and add:",
      reload: "Reload",
    },
  },
};

export default translation; 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle Sandbox环境修复</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .log { max-height: 400px; overflow-y: auto; }
        .config-info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .domain-list { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .urgent { background-color: #f8d7da; border-color: #f5c6cb; animation: pulse 2s infinite; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Paddle Sandbox环境修复</h1>
        
        <div class="section urgent">
            <h2>🚨 检测到的问题</h2>
            <ul>
                <li>❌ <strong>CSP错误:</strong> "frame-ancestors http://localhost" - Sandbox域名白名单未配置</li>
                <li>❌ <strong>403 Forbidden:</strong> checkout-service.paddle.com - Sandbox权限问题</li>
            </ul>
        </div>

        <div class="section info">
            <h2>📋 当前Sandbox配置</h2>
            <div class="config-info">
                <h3>🔧 从.env.local读取的配置</h3>
                <p><strong>环境:</strong> Sandbox ✅</p>
                <p><strong>Vendor ID:</strong> <code>32634</code></p>
                <p><strong>产品ID:</strong> <code>pri_01k1aenmy30gyzrq9wayftn5z4</code></p>
                <p><strong>Token:</strong> <code>test_27fb172b17e19acd4fbad97de75</code></p>
                <p><strong>当前域名:</strong> <code id="current-domain"></code></p>
            </div>
        </div>

        <div class="section warning">
            <h2>🎯 立即需要配置Sandbox域名白名单</h2>
            
            <div class="domain-list">
                <h3>📋 Sandbox域名白名单配置</h3>
                <p><strong>⚠️ 重要:</strong> 必须在<strong>Sandbox Dashboard</strong>中配置，不是正式环境！</p>
                
                <p><strong>配置路径:</strong></p>
                <ol>
                    <li>登录 <a href="https://sandbox-vendors.paddle.com/" target="_blank">Sandbox Dashboard</a></li>
                    <li>进入 <strong>Developer tools > Checkout settings</strong></li>
                    <li>在 <strong>Allowed domains</strong> 中添加以下域名:</li>
                </ol>
                
                <h4>需要添加的域名:</h4>
                <ul>
                    <li>✅ <code>http://localhost:5173</code></li>
                    <li>✅ <code>http://localhost:5174</code></li>
                    <li>✅ <code>http://localhost</code></li>
                    <li>✅ <code>https://autofill.utilityapp.site</code></li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🧪 Sandbox环境测试</h2>
            <button onclick="testSandboxConfig()">1. 测试Sandbox配置</button>
            <button onclick="testModifiedParams()">2. 测试修复后的参数格式</button>
            <button onclick="testMinimalSandbox()">3. 测试最简Sandbox配置</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section success" id="success-section" style="display: none;">
            <h2>🎉 Sandbox配置成功！</h2>
            <div id="success-details"></div>
        </div>

        <div class="section error">
            <h2>🔧 解决步骤</h2>
            <ol>
                <li><strong>立即配置Sandbox域名白名单:</strong>
                    <ul>
                        <li>登录 <a href="https://sandbox-vendors.paddle.com/" target="_blank">Sandbox Dashboard</a></li>
                        <li>进入 Developer tools > Checkout settings</li>
                        <li>在 Allowed domains 中添加localhost域名</li>
                        <li>保存配置并等待2-5分钟生效</li>
                    </ul>
                </li>
                
                <li><strong>验证Token权限:</strong>
                    <ul>
                        <li>确认Token <code>test_27fb172b17e19acd4fbad97de75</code> 在Sandbox中有效</li>
                        <li>检查Token状态为Active</li>
                        <li>如有问题，重新生成Token</li>
                    </ul>
                </li>
                
                <li><strong>验证产品配置:</strong>
                    <ul>
                        <li>确认产品 <code>pri_01k1aenmy30gyzrq9wayftn5z4</code> 在Sandbox中存在</li>
                        <li>确认产品状态为Active</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'test_27fb172b17e19acd4fbad97de75',
            productId: 'pri_01k1aenmy30gyzrq9wayftn5z4',
            environment: 'sandbox'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('success-section').style.display = 'none';
        }

        function showCurrentDomain() {
            document.getElementById('current-domain').textContent = window.location.origin;
        }

        function showSuccess() {
            const successDiv = document.getElementById('success-details');
            successDiv.innerHTML = `
                <h3>✅ Sandbox环境配置成功！</h3>
                <p>现在可以在主应用中正常使用支付功能了。</p>
                <p><strong>下一步:</strong> 在主应用的 /pricing 页面测试支付功能。</p>
            `;
            document.getElementById('success-section').style.display = 'block';
        }

        async function initializePaddle() {
            return new Promise((resolve) => {
                // 使用修复后的官方格式
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            log(`❌ Checkout错误: ${data.detail}`, 'error');
                            
                            if (data.detail && data.detail.includes('403')) {
                                log('🔍 403错误 - 检查Sandbox Token和产品配置', 'error');
                            } else if (data.detail && data.detail.includes('frame-ancestors')) {
                                log('🔍 CSP错误 - 需要配置Sandbox域名白名单', 'error');
                            }
                        } else if (data.type === 'checkout.loaded') {
                            log('✅ Checkout加载成功！', 'success');
                            showSuccess();
                        } else if (data.name === 'checkout.closed' && data.data) {
                            const item = data.data.items?.[0];
                            if (item?.price_id && item?.product?.id) {
                                log('🎉 Sandbox环境配置完全成功！', 'success');
                                showSuccess();
                            }
                        }
                    }
                });
                resolve();
            });
        }

        async function testSandboxConfig() {
            log('🧪 测试1: Sandbox环境配置...', 'info');
            log(`环境: ${config.environment}`, 'info');
            log(`Token: ${config.token}`, 'info');
            log(`产品ID: ${config.productId}`, 'info');
            
            try {
                await initializePaddle();
                
                const testConfig = {
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                log(`配置: ${JSON.stringify(testConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(testConfig);
                log('✅ Sandbox测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ Sandbox测试失败: ${error.message}`, 'error');
            }
        }

        async function testModifiedParams() {
            log('🧪 测试2: 修复后的参数格式...', 'info');
            
            try {
                await initializePaddle();
                
                // 使用修复后的参数格式
                const modifiedConfig = {
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }],
                    customer: {
                        email: "<EMAIL>"
                    },
                    customData: {
                        userId: "sandbox123",
                        plan: "test"
                    },
                    settings: {  // settings作为独立属性
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                log(`修复后配置: ${JSON.stringify(modifiedConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(modifiedConfig);
                log('✅ 修复后参数格式测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 修复后参数格式测试失败: ${error.message}`, 'error');
            }
        }

        async function testMinimalSandbox() {
            log('🧪 测试3: 最简Sandbox配置...', 'info');
            
            try {
                await initializePaddle();
                
                const minimalConfig = {
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }]
                };
                
                log(`最简配置: ${JSON.stringify(minimalConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(minimalConfig);
                log('✅ 最简配置测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 最简配置测试失败: ${error.message}`, 'error');
            }
        }

        // 监听CSP错误
        window.addEventListener('securitypolicyviolation', function(e) {
            log(`🚨 CSP违规检测: ${e.violatedDirective} - ${e.blockedURI}`, 'error');
            log('💡 确认域名白名单配置在Sandbox Dashboard中', 'warning');
        });

        // 页面加载时初始化
        window.onload = function() {
            showCurrentDomain();
            log('🔧 Paddle Sandbox环境修复工具已加载', 'info');
            log('⚠️ 检测到CSP和403错误，需要配置Sandbox域名白名单', 'warning');
            log('🎯 请先在Sandbox Dashboard中配置域名白名单', 'info');
        };

        // 添加脉冲动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.8; }
                100% { opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

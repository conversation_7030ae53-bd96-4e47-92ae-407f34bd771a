/**
 * 统一日志管理模块
 * 生产环境自动屏蔽日志输出
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogConfig {
  enabled: boolean;
  level: LogLevel;
  prefix: string;
}

class Logger {
  private config: LogConfig;

  constructor() {
    // 强制在生产环境屏蔽所有日志
    const isDev = import.meta.env.DEV || import.meta.env.MODE === 'development';
    const isProduction = import.meta.env.PROD || import.meta.env.MODE === 'production';
    const isLocalhost = typeof window !== 'undefined' && 
      (window.location.hostname === 'localhost' || 
       window.location.hostname === '127.0.0.1' ||
       window.location.hostname.includes('localhost'));
    
    // 多重检查确保生产环境绝对不会输出日志
    const shouldEnable = isDev && !isProduction && isLocalhost;
    
    this.config = {
      enabled: shouldEnable, // 只在开发环境且本地环境启用
      level: 'debug',
      prefix: '[AutoFill]'
    };
    
    // 生产环境额外保护：即使配置被修改，也强制禁用
    if (isProduction || (!isDev && !isLocalhost)) {
      this.config.enabled = false;
    }
  }

  /**
   * 设置日志配置
   */
  configure(config: Partial<LogConfig>): void {
    // 生产环境强制禁用，不允许通过配置启用
    const isProduction = import.meta.env.PROD || import.meta.env.MODE === 'production';
    if (isProduction) {
      this.config = { ...this.config, ...config, enabled: false };
    } else {
      this.config = { ...this.config, ...config };
    }
  }

  /**
   * 强制禁用所有日志（用于生产环境部署）
   */
  forceDisable(): void {
    this.config.enabled = false;
  }

  /**
   * 检查日志是否启用
   */
  isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * 格式化日志消息
   */
  private formatMessage(level: LogLevel, message: string, data?: any): string {
    const timestamp = new Date().toLocaleTimeString();
    const levelIcon = this.getLevelIcon(level);
    return `${this.config.prefix} ${levelIcon} [${timestamp}] ${message}`;
  }

  /**
   * 获取日志级别图标
   */
  private getLevelIcon(level: LogLevel): string {
    const icons = {
      debug: '🔧',
      info: 'ℹ️',
      warn: '⚠️',
      error: '❌'
    };
    return icons[level];
  }

  /**
   * 检查是否应该输出日志
   */
  private shouldLog(level: LogLevel): boolean {
    if (!this.config.enabled) return false;
    
    const levels = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(this.config.level);
    const messageLevelIndex = levels.indexOf(level);
    
    return messageLevelIndex >= currentLevelIndex;
  }

  /**
   * 调试日志
   */
  debug(message: string, data?: any): void {
    if (!this.shouldLog('debug')) return;
    
    const formattedMessage = this.formatMessage('debug', message);
    if (data !== undefined) {
      console.log(formattedMessage, data);
    } else {
      console.log(formattedMessage);
    }
  }

  /**
   * 信息日志
   */
  info(message: string, data?: any): void {
    if (!this.shouldLog('info')) return;
    
    const formattedMessage = this.formatMessage('info', message);
    if (data !== undefined) {
      console.info(formattedMessage, data);
    } else {
      console.info(formattedMessage);
    }
  }

  /**
   * 警告日志
   */
  warn(message: string, data?: any): void {
    if (!this.shouldLog('warn')) return;
    
    const formattedMessage = this.formatMessage('warn', message);
    if (data !== undefined) {
      console.warn(formattedMessage, data);
    } else {
      console.warn(formattedMessage);
    }
  }

  /**
   * 错误日志
   */
  error(message: string, error?: any): void {
    if (!this.shouldLog('error')) return;
    
    const formattedMessage = this.formatMessage('error', message);
    if (error !== undefined) {
      console.error(formattedMessage, error);
    } else {
      console.error(formattedMessage);
    }
  }

  /**
   * 分组日志开始
   */
  group(label: string): void {
    if (!this.config.enabled) return;
    console.group(`${this.config.prefix} 📋 ${label}`);
  }

  /**
   * 分组日志结束
   */
  groupEnd(): void {
    if (!this.config.enabled) return;
    console.groupEnd();
  }

  /**
   * 性能计时开始
   */
  time(label: string): void {
    if (!this.config.enabled) return;
    console.time(`${this.config.prefix} ⏱️ ${label}`);
  }

  /**
   * 性能计时结束
   */
  timeEnd(label: string): void {
    if (!this.config.enabled) return;
    console.timeEnd(`${this.config.prefix} ⏱️ ${label}`);
  }
}

// 创建全局日志实例
export const logger = new Logger();

// 导出便捷方法
export const log = {
  debug: (message: string, data?: any) => logger.debug(message, data),
  info: (message: string, data?: any) => logger.info(message, data),
  warn: (message: string, data?: any) => logger.warn(message, data),
  error: (message: string, error?: any) => logger.error(message, error),
  group: (label: string) => logger.group(label),
  groupEnd: () => logger.groupEnd(),
  time: (label: string) => logger.time(label),
  timeEnd: (label: string) => logger.timeEnd(label)
};

// 默认导出
export default logger;
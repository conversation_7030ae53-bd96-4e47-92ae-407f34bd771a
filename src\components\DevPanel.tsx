import React, { useState, useEffect } from 'react';

interface DevPanelProps {
  isVisible: boolean;
  onClose: () => void;
}

const DevPanel: React.FC<DevPanelProps> = ({ isVisible, onClose }) => {
  const [mockPaymentEnabled, setMockPaymentEnabled] = useState(false);

  useEffect(() => {
    // 读取当前设置
    const enabled = localStorage.getItem('enableMockPayment') === 'true';
    setMockPaymentEnabled(enabled);
  }, []);

  const handleMockPaymentToggle = (enabled: boolean) => {
    setMockPaymentEnabled(enabled);
    localStorage.setItem('enableMockPayment', enabled.toString());
    
    if (enabled) {
      alert('🎭 模拟支付已启用！\n\n现在点击订阅按钮将显示模拟支付对话框，而不是真实的Paddle支付页面。');
    } else {
      alert('🔧 模拟支付已禁用！\n\n现在将使用真实的Paddle支付系统。');
    }
  };

  const clearAllData = () => {
    const confirmed = confirm('⚠️ 确定要清空所有本地数据吗？\n\n这将清除：\n- 用户登录状态\n- 订阅信息\n- 所有设置\n\n页面将自动刷新。');
    
    if (confirmed) {
      localStorage.clear();
      sessionStorage.clear();
      window.location.reload();
    }
  };

  const showPaddleInfo = () => {
    const info = `🔧 Paddle配置信息

🏷️ 环境: Sandbox (测试环境)
🔑 Token: test_459728092d35d36bc173c60e52d
📦 产品ID: pri_01jxa2azwznm06dqbptzagtm0z
🌐 当前域名: ${window.location.hostname}:${window.location.port}

📋 如果遇到403错误，请检查：

1️⃣ Paddle Dashboard设置:
   https://sandbox-vendors.paddle.com

2️⃣ 添加域名白名单:
   Settings > Checkout settings > Allowed domains
   添加: localhost:5173

3️⃣ 检查Token状态:
   Developer tools > Authentication
   确认token为Active状态

4️⃣ 验证产品配置:
   Catalog > Products
   确认产品存在且为Active状态`;

    alert(info);
  };

  if (!isVisible) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      zIndex: 10000,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '24px',
        maxWidth: '500px',
        width: '90%',
        maxHeight: '80vh',
        overflow: 'auto'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px'
        }}>
          <h2 style={{ margin: 0, color: '#333' }}>🛠️ 开发者面板</h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ×
          </button>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h3 style={{ color: '#333', marginBottom: '10px' }}>💳 支付设置</h3>
          <label style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            cursor: 'pointer',
            padding: '8px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            backgroundColor: mockPaymentEnabled ? '#e8f5e8' : '#f8f9fa'
          }}>
            <input
              type="checkbox"
              checked={mockPaymentEnabled}
              onChange={(e) => handleMockPaymentToggle(e.target.checked)}
            />
            <span>启用模拟支付 (跳过真实Paddle支付)</span>
          </label>
          <p style={{ fontSize: '12px', color: '#666', margin: '8px 0 0 0' }}>
            启用后，点击订阅按钮将显示模拟支付对话框，用于测试支付流程
          </p>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h3 style={{ color: '#333', marginBottom: '10px' }}>🔧 工具</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <button
              onClick={showPaddleInfo}
              style={{
                padding: '8px 16px',
                border: '1px solid #007bff',
                borderRadius: '4px',
                backgroundColor: '#007bff',
                color: 'white',
                cursor: 'pointer'
              }}
            >
              📋 查看Paddle配置信息
            </button>
            <button
              onClick={clearAllData}
              style={{
                padding: '8px 16px',
                border: '1px solid #dc3545',
                borderRadius: '4px',
                backgroundColor: '#dc3545',
                color: 'white',
                cursor: 'pointer'
              }}
            >
              🗑️ 清空所有本地数据
            </button>
          </div>
        </div>

        <div style={{ fontSize: '12px', color: '#666' }}>
          <p><strong>提示:</strong> 这个面板只在开发环境中显示</p>
          <p><strong>快捷键:</strong> Ctrl+Shift+D 打开/关闭面板</p>
        </div>
      </div>
    </div>
  );
};

export default DevPanel;

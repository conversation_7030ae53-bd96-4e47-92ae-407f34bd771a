<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle产品状态检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .log { max-height: 400px; overflow-y: auto; }
        .status-check { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Paddle产品状态检查</h1>
        
        <div class="section success">
            <h2>✅ 已确认的问题</h2>
            <p><strong>支付页面能打开，但产品信息为空：</strong></p>
            <ul>
                <li>✅ Token配置正确</li>
                <li>✅ 域名白名单正确</li>
                <li>✅ API调用成功</li>
                <li>❌ <strong>产品数据获取失败</strong> - price_id: null, product.id: 0</li>
            </ul>
        </div>

        <div class="section warning">
            <h2>🎯 需要检查的产品配置</h2>
            <div class="status-check">
                <h3>📋 在Paddle Dashboard中检查：</h3>
                <ol>
                    <li><strong>产品状态：</strong> 确保产品状态为 "Active"</li>
                    <li><strong>价格状态：</strong> 确保价格状态为 "Active"</li>
                    <li><strong>试用期配置：</strong> 检查30天试用是否正确配置</li>
                    <li><strong>产品可见性：</strong> 确保产品对客户可见</li>
                    <li><strong>价格可用性：</strong> 确保价格在当前环境中可用</li>
                </ol>
            </div>
        </div>

        <div class="section info">
            <h2>📋 当前产品ID</h2>
            <p><strong>工作的产品ID:</strong> <code>pri_01k0xttmf9wekbg957y66xawj</code> ($9.99/year)</p>
            <p><strong>有问题的产品ID:</strong> <code>pri_01jxa2azwznm06dqbptzagtm0z</code> ($6.66/year)</p>
        </div>

        <div class="section">
            <h2>🧪 产品状态测试</h2>
            <button onclick="testProductVisibility()">1. 测试产品可见性</button>
            <button onclick="testWithProductDetails()">2. 测试产品详情显示</button>
            <button onclick="testDifferentSettings()">3. 测试不同设置</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section error">
            <h2>🔧 解决步骤</h2>
            <ol>
                <li><strong>检查产品状态：</strong>
                    <ul>
                        <li>登录Paddle Dashboard</li>
                        <li>进入 Catalog > Products</li>
                        <li>找到 "autoinput" 产品</li>
                        <li>确认状态为 "Active"</li>
                    </ul>
                </li>
                <li><strong>检查价格状态：</strong>
                    <ul>
                        <li>点击产品进入详情页</li>
                        <li>查看价格列表</li>
                        <li>确认 $9.99/year 价格状态为 "Active"</li>
                    </ul>
                </li>
                <li><strong>检查试用期配置：</strong>
                    <ul>
                        <li>确认30天试用期配置正确</li>
                        <li>检查试用期是否影响产品显示</li>
                    </ul>
                </li>
                <li><strong>如果仍有问题：</strong>
                    <ul>
                        <li>尝试创建一个新的简单产品（无试用期）</li>
                        <li>联系Paddle技术支持</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'test_459728092d35d36bc173c60e52d',
            productId: 'pri_01k0xttmf9wekbg957y66xawj'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        async function initializePaddle() {
            return new Promise((resolve) => {
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        // 详细分析返回的数据
                        if (data.name === 'checkout.closed' && data.data) {
                            const checkoutData = data.data;
                            
                            log('🔍 详细分析checkout数据:', 'info');
                            log(`- 订单ID: ${checkoutData.id || 'null'}`, checkoutData.id ? 'success' : 'error');
                            log(`- 货币: ${checkoutData.currency_code || 'unknown'}`, 'info');
                            
                            if (checkoutData.items && checkoutData.items.length > 0) {
                                const item = checkoutData.items[0];
                                log(`- 价格ID: ${item.price_id || 'null'}`, item.price_id ? 'success' : 'error');
                                log(`- 产品ID: ${item.product?.id || 'null'}`, item.product?.id ? 'success' : 'error');
                                log(`- 产品名称: "${item.product?.name || ''}"`, item.product?.name ? 'success' : 'error');
                                log(`- 产品描述: "${item.product?.description || ''}"`, 'info');
                                log(`- 数量: ${item.quantity || 'null'}`, item.quantity ? 'success' : 'error');
                                
                                if (!item.price_id || !item.product?.id || !item.product?.name) {
                                    log('❌ 产品数据不完整 - 这是问题所在！', 'error');
                                    log('💡 建议检查Paddle Dashboard中的产品配置', 'warning');
                                }
                            }
                            
                            if (checkoutData.totals) {
                                log(`- 小计: ${checkoutData.totals.subtotal || 0}`, checkoutData.totals.subtotal > 0 ? 'success' : 'error');
                                log(`- 总计: ${checkoutData.totals.total || 0}`, checkoutData.totals.total > 0 ? 'success' : 'error');
                            }
                        }
                    }
                });
                resolve();
            });
        }

        async function testProductVisibility() {
            log('🔍 测试1: 产品可见性检查...', 'info');
            
            try {
                await initializePaddle();
                
                window.Paddle.Checkout.open({
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }]
                });
                
                log('✅ 产品可见性测试请求已发送', 'success');
                log('💡 请观察返回的产品数据是否完整', 'info');
                
            } catch (error) {
                log(`❌ 产品可见性测试失败: ${error.message}`, 'error');
            }
        }

        async function testWithProductDetails() {
            log('🔍 测试2: 强制显示产品详情...', 'info');
            
            try {
                await initializePaddle();
                
                // 尝试添加更多产品信息
                window.Paddle.Checkout.open({
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: 'overlay',
                        theme: 'light',
                        showAddDiscounts: true,
                        showAddTaxId: true
                    }
                });
                
                log('✅ 产品详情测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 产品详情测试失败: ${error.message}`, 'error');
            }
        }

        async function testDifferentSettings() {
            log('🔍 测试3: 不同设置配置...', 'info');
            
            try {
                await initializePaddle();
                
                // 尝试不同的设置组合
                window.Paddle.Checkout.open({
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: 'inline',
                        theme: 'dark',
                        locale: 'en'
                    }
                });
                
                log('✅ 不同设置测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 不同设置测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            log('🔍 Paddle产品状态检查工具已加载', 'info');
            log('💡 现在我们知道问题是产品数据为空，需要检查产品配置', 'warning');
            log(`当前测试产品ID: ${config.productId}`, 'info');
        };
    </script>
</body>
</html>

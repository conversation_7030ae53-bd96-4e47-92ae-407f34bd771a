import { buildApiUrl, createApiRequestOptions, USERAPI_URL } from '../config/api';
import {
  setStorageItem,
  setStorageItems,
  getStorageItem,
  removeStorageItem
} from './storageAdapter';
import { getDeviceId } from './deviceIdService';
import { log } from '../utils/logger';

// API服务已初始化

interface LoginResponse {
  access_token: string;
  refresh_token: string;
  access_token_expires_in: number;
  token_type: string;
  user_id: string;
  subscription_info: any | null;
  email?: string;
}

interface RefreshTokenResponse {
  access_token: string;
  token_type: string;
  access_token_expires_in: number;
}

/**
 * 请求验证码
 * @param email 用户邮箱
 * @returns 验证码状态码 (1: 成功发送，2: 已有未过期验证码，3: 发送失败，4: 超过设备限制) 或 void
 */
export async function requestVerificationCode(email: string): Promise<number | void> {
  try {
    // 获取设备ID（使用新的确定性设备ID服务）
    const deviceId = await getDeviceId();
    log.debug('请求验证码时获取设备ID', { deviceId: deviceId.substring(0, 16) + '...' });

    // 创建请求选项
    const requestOptions = createApiRequestOptions({
      method: 'POST',
      body: JSON.stringify({ 
        email,
        device_id: deviceId,
        device_info: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          screen: `${screen.width}x${screen.height}`,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        }
      }),
      mode: 'cors',
      credentials: 'omit'
    });

    const url = buildApiUrl('auth/request-code');

    // 发送请求
    const response = await fetch(url, requestOptions);

    if (!response.ok) {
      let errorData = { message: '请求验证码失败', detail: '' };
      try {
        errorData = await response.json();
        console.error('验证码错误响应数据:', errorData);
      } catch (jsonError) {
        console.error('无法解析错误响应:', jsonError);
      }
      throw new Error(errorData.message || errorData.detail || '请求验证码失败');
    }

    // 检查响应内容
    const contentType = response.headers.get("content-type");
    if (contentType && contentType.indexOf("application/json") !== -1) {
      const data = await response.json();

      // 返回验证码发送状态
      if (data && data.message) {
        return parseInt(data.message); // 返回状态码 (1, 2, 3 或 4)
      } else {
        return 1; // 默认返回成功
      }
    } else {
      return 1; // 默认返回成功
    }
  } catch (error) {
    console.error('请求验证码失败:', error);
    throw error;
  }
}

/**
 * 验证验证码并登录/注册
 * @param email 用户邮箱
 * @param code 验证码
 * @returns 登录响应，包含token和用户信息
 */
export async function verifyCodeAndLogin(email: string, code: string): Promise<LoginResponse> {
  try {
    // 获取设备ID（必须与请求验证码时使用的设备ID一致）
    const deviceId = await getDeviceId();
    log.debug('验证码登录时获取设备ID', { deviceId: deviceId.substring(0, 16) + '...' });

    const requestOptions = createApiRequestOptions({
      method: 'POST',
      body: JSON.stringify({
        email,
        code,
        device_id: deviceId
      }),
      mode: 'cors',
      credentials: 'omit'
    });

    const url = buildApiUrl('auth/verify-code');

    // 发送请求
    const response = await fetch(url, requestOptions);

    if (!response.ok) {
      let errorData = { message: '验证失败', detail: '' };
      try {
        errorData = await response.json();
        log.error('验证码验证错误响应:', errorData);
      } catch (jsonError) {
        log.error('无法解析验证错误响应:', jsonError);
      }

      // 统一错误提示：所有失败情况都显示相同的错误信息
      throw new Error('验证失败，请稍后重试，请联系***********************');
    }

    const responseData = await response.json();
    
    // 处理嵌套的响应格式
    let tokenData: any;
    
    if (responseData.success && responseData.data) {
      // 新的嵌套响应格式
      tokenData = {
        access_token: responseData.data.access_token,
        refresh_token: responseData.data.refresh_token,
        access_token_expires_in: responseData.data.access_token_expires_in,
        token_type: 'bearer', // 假设类型为bearer
        user_id: email.split('@')[0], // 临时使用邮箱用户名作为ID
        subscription_info: null
      };
    } else {
      // 旧的直接响应格式
      tokenData = responseData;
    }
    
    // 确保所有必要字段都存在
    if (!tokenData.access_token || !tokenData.refresh_token) {
      console.error('响应数据缺少必要的令牌字段');
      throw new Error('登录响应缺少必要的令牌信息');
    }
    
    // 安全地保存令牌和用户信息到本地存储
    await saveAuthData(tokenData);

    // 保存邮箱信息
    await setStorageItem('user_email', email);

    // 登录成功后立即获取用户信息
    try {
      const userProfile = await fetchUserProfile(tokenData.access_token, email);

      // 更新本地存储的用户信息
      if (userProfile.user_id) {
        await setStorageItem('user_id', userProfile.user_id);
      }
      if (userProfile.email) {
        await setStorageItem('user_email', userProfile.email);
      }

      // 立即触发storage事件，确保AuthContext能够快速响应
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new Event('storage'));
      }
    } catch (userError) {
      // 即使获取用户信息失败，登录仍然算成功
      // 使用邮箱作为临时用户ID
      const tempUserId = email.split('@')[0];
      await setStorageItem('user_id', tempUserId);
      await setStorageItem('user_email', email);

      // 立即触发storage事件
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new Event('storage'));
      }
    }

    return tokenData;
  } catch (error) {
    log.error('验证码验证失败:', error);

    // 如果是网络错误或其他非预期错误，也使用统一的错误提示
    if (error instanceof Error && error.message.includes('验证失败，请稍后重试')) {
      throw error; // 保持已设置的统一错误信息
    } else {
      throw new Error('验证失败，请稍后重试，请联系***********************');
    }
  }
}

/**
 * 获取用户信息
 * @param accessToken 访问令牌
 * @param email 用户邮箱
 * @returns 用户信息
 */
async function fetchUserProfile(accessToken: string, email: string): Promise<{user_id: string, email: string}> {
  try {
    const requestOptions = createApiRequestOptions({
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`
      },
      mode: 'cors',
      credentials: 'omit'
    });

    const url = `${USERAPI_URL}/user/me`;
    const response = await fetch(url, requestOptions);

    if (!response.ok) {
      throw new Error(`获取用户信息失败: ${response.status}`);
    }

    const userData = await response.json();

    // 解析用户信息
    let userId = '';
    let userEmail = email; // 默认使用登录时的邮箱

    if (userData.user_id) {
      userId = userData.user_id;
      userEmail = userData.email || email;
    } else if (userData.id) {
      userId = userData.id;
      userEmail = userData.email || email;
    } else if (userData.data && userData.data.user_id) {
      userId = userData.data.user_id;
      userEmail = userData.data.email || email;
    } else if (userData.data && userData.data.id) {
      userId = userData.data.id;
      userEmail = userData.data.email || email;
    } else {
      // 如果没有找到用户ID，使用邮箱前缀作为临时ID
      userId = email.split('@')[0];
    }

    return { user_id: userId, email: userEmail };
  } catch (error) {
    console.error('获取用户信息过程中出错:', error);
    throw error;
  }
}

/**
 * 刷新访问令牌
 * @param refreshToken 刷新令牌
 * @returns 新的访问令牌信息
 */
export async function refreshAccessToken(refreshToken: string): Promise<RefreshTokenResponse> {
  try {
    // 获取设备ID（必须与登录时使用的设备ID一致）
    const deviceId = await getDeviceId();
    log.debug('刷新令牌时获取设备ID', { deviceId: deviceId.substring(0, 16) + '...' });

    const requestOptions = createApiRequestOptions({
      method: 'POST',
      body: JSON.stringify({
        refresh_token: refreshToken,
        device_id: deviceId
      }),
      mode: 'cors',
      credentials: 'omit'
    });

    const url = buildApiUrl('auth/refresh-token');

    // 发送请求
    const response = await fetch(url, requestOptions);
    
    if (!response.ok) {
      let errorData = { message: '刷新令牌失败', detail: '', success: false, code: 0, type: '' };
      try {
        errorData = await response.json();
        log.error('刷新令牌错误响应:', errorData);
      } catch (jsonError) {
        log.error('无法解析刷新令牌错误响应:', jsonError);
      }

      // 所有刷新令牌失败都清理本地缓存并要求重新登录
      log.warn('刷新令牌失败，清理本地缓存');
      clearAuthData();

      // 触发重新登录事件
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('auth-relogin-required', {
          detail: { reason: 'refresh_token_failed', code: errorData.code, type: errorData.type }
        }));
      }

      throw new Error('登录已过期，请重新登录');
    }

    const responseData = await response.json();

    // 处理嵌套的响应格式
    let tokenData: any;

    if (responseData.success && responseData.data) {
      // 新的嵌套响应格式
      tokenData = {
        access_token: responseData.data.access_token,
        refresh_token: responseData.data.refresh_token || refreshToken, // 如果没有返回新的刷新令牌，使用原来的
        access_token_expires_in: responseData.data.access_token_expires_in,
        token_type: 'bearer' // 假设类型为bearer
      };
    } else {
      // 旧的直接响应格式
      tokenData = responseData;
    }
    
    // 确保访问令牌字段存在
    if (!tokenData.access_token) {
      console.error('响应数据缺少必要的访问令牌字段');
      throw new Error('刷新令牌响应缺少必要的访问令牌信息');
    }
    
    // 更新本地存储中的访问令牌（使用安全存储适配器）
    await setStorageItem('access_token', tokenData.access_token);
    await setStorageItem('access_token_expires_at', String(Date.now() + tokenData.access_token_expires_in * 1000));

    // 如果返回了新的刷新令牌，也更新它
    if (tokenData.refresh_token && tokenData.refresh_token !== refreshToken) {
      await setStorageItem('refresh_token', tokenData.refresh_token);
    }
    
    return tokenData;
  } catch (error) {
    log.error('刷新令牌失败:', error);

    // 网络错误或其他异常也清理缓存并要求重新登录
    if (!(error instanceof Error && error.message.includes('登录已过期'))) {
      log.warn('刷新令牌网络错误，清理本地缓存');
      clearAuthData();

      // 触发重新登录事件
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('auth-relogin-required', {
          detail: { reason: 'refresh_network_error' }
        }));
      }
    }

    // 统一错误信息
    throw new Error('登录已过期，请重新登录');
  }
}

/**
 * 登出用户
 * @returns void
 */
export async function logout(): Promise<void> {
  try {
    const token = await getAccessToken();
    const refreshToken = await getRefreshToken();

    // 如果没有找到令牌，直接清除本地数据并返回
    if (!token || !refreshToken) {
      clearAuthData();
      return;
    }

    const response = await fetch(buildApiUrl('auth/logout'), createApiRequestOptions({
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        refresh_token: refreshToken
      })
    }));

    if (!response.ok) {
      console.error('退出登录错误响应:', await response.text());
      // 即使后端请求失败，也清除本地数据
      clearAuthData();
      throw new Error('退出登录请求失败，但本地数据已清除');
    }

    // 清除所有本地存储的令牌和用户信息
    clearAuthData();
  } catch (error) {
    console.error('退出登录过程中出错:', error);
    // 确保在出错的情况下也清除本地数据
    clearAuthData();
    throw error;
  }
}

/**
 * 保存认证数据到存储
 * @param data 登录响应数据
 */
async function saveAuthData(data: LoginResponse): Promise<void> {

  // 安全处理订阅信息
  let subscriptionInfoStr = null;
  if (data.subscription_info) {
    try {
      // 如果已经是字符串，使用它；否则转换为JSON字符串
      subscriptionInfoStr = typeof data.subscription_info === 'string' 
        ? data.subscription_info 
        : JSON.stringify(data.subscription_info);
      
      // 验证是否是有效的JSON
      try {
        JSON.parse(subscriptionInfoStr);
      } catch (e) {
        console.error('订阅信息不是有效的JSON:', subscriptionInfoStr);
        subscriptionInfoStr = null;
      }
    } catch (e) {
      console.error('转换订阅信息失败:', e);
      subscriptionInfoStr = null;
    }
  }
  
  // 使用存储适配器保存数据
  const storageData = {
    'access_token': data.access_token,
    'refresh_token': data.refresh_token,
    'token_type': data.token_type,
    'access_token_expires_at': String(Date.now() + data.access_token_expires_in * 1000),
    'user_id': data.user_id,
    'subscription_info': subscriptionInfoStr
  };

  await setStorageItems(storageData);

  // 保存邮箱信息
  if (data.email) {
    await setStorageItem('user_email', data.email);
  }
}

/**
 * 清除所有认证数据
 */
function clearAuthData(): void {
  // 使用存储适配器清除数据
  removeStorageItem([
    'access_token',
    'refresh_token',
    'token_type',
    'access_token_expires_at',
    'user_id',
    'subscription_info',
    'user_email'
  ]);
}

/**
 * 获取当前访问令牌
 * @returns 当前访问令牌或null
 */
export async function getAccessToken(): Promise<string | null> {
  return await getStorageItem<string>('access_token');
}

/**
 * 获取当前刷新令牌
 * @returns 当前刷新令牌或null
 */
export async function getRefreshToken(): Promise<string | null> {
  return await getStorageItem<string>('refresh_token');
}

/**
 * 检查访问令牌是否已过期
 * @returns 如果令牌已过期则返回true，否则返回false
 */
export async function isAccessTokenExpired(): Promise<boolean> {
  try {
    // 首先检查存储中的过期时间
    const expiresAtStr = await getStorageItem<string>('access_token_expires_at');
    const token = await getAccessToken();
    
    // 如果没有过期时间记录或没有token，则认为已过期
    if (!expiresAtStr || !token) return true;
    
    // 从localStorage中获取过期时间
    const expiresAt = parseInt(expiresAtStr, 10);
    
    // 从token中解析过期时间（如果可能）
    let tokenExpTime = 0;
    try {
      // 尝试解析JWT token (格式: xxxxx.yyyyy.zzzzz)
      if (token.split('.').length === 3) {
        const tokenPayload = JSON.parse(atob(token.split('.')[1]));
        if (tokenPayload.exp) {
          // JWT中的exp是以秒为单位的时间戳
          tokenExpTime = tokenPayload.exp * 1000;
        }
      }
    } catch (e) {
      console.warn('无法从token中解析过期时间:', e);
    }
    
    const now = Date.now();
    // 提前5分钟刷新令牌，避免在临界点出现问题
    const buffer = 5 * 60 * 1000; 
    
    // 如果有两个过期时间，取较早的那个
    if (tokenExpTime > 0) {
      const earlierExpTime = Math.min(expiresAt, tokenExpTime);
      return now > earlierExpTime - buffer;
    }
    
    // 否则只使用localStorage中的过期时间
    return now > expiresAt - buffer;
  } catch (error) {
    console.error('检查令牌过期时出错:', error);
    return true; // 出错时假设令牌已过期
  }
}

/**
 * 设置访问令牌（已废弃，请使用存储适配器）
 * @deprecated 请使用 setStorageItem('access_token', token)
 */
export const setAccessToken = (token: string) => {
  localStorage.setItem('access_token', token);
};

/**
 * 设置刷新令牌（已废弃，请使用存储适配器）
 * @deprecated 请使用 setStorageItem('refresh_token', token)
 */
export const setRefreshToken = (token: string) => {
  localStorage.setItem('refresh_token', token);
};

/**
 * 移除访问令牌（通过存储适配器安全删除）
 */
export const removeAccessToken = async () => {
  const { removeStorageItem } = await import('./storageAdapter');
  await removeStorageItem('access_token');
};

/**
 * 移除刷新令牌（通过存储适配器安全删除）
 */
export const removeRefreshToken = async () => {
  const { removeStorageItem } = await import('./storageAdapter');
  await removeStorageItem('refresh_token');
};
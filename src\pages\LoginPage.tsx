import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { log } from '../utils/logger';
import { useAuth } from '../contexts/AuthContext';
import { requestVerificationCode, verifyCodeAndLogin } from '../services/authService';

interface LocationState {
  from?: string;
}

const LoginPage: React.FC = () => {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [codeSending, setCodeSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useAuth();
  
  // 如果用户已登录，重定向到之前尝试访问的页面或默认到个人中心
  useEffect(() => {
    if (currentUser) {
      const state = location.state as LocationState;
      const from = state?.from || '/profile';
      navigate(from, { replace: true });
    }
  }, [currentUser, location.state, navigate]);

  // 请求验证码
  const handleRequestCode = async (e: React.MouseEvent) => {
    e.preventDefault();
    
    if (!email) {
      setError(t('login.errors.emailRequired'));
      return;
    }

    if (!agreedToTerms) {
      setError(t('login.errors.agreementRequired'));
      return;
    }
    
    try {
      setCodeSending(true);
      setError(null);
      
      const status = await requestVerificationCode(email);

      // 根据状态显示不同的提示信息
      if (status === 1) {
        // 邮件发送成功
        setCodeSent(true);
        setError(null);
      } else if (status === 2) {
        // 已有未过期验证码
        setCodeSent(true);
        setError(t('login.codeAlreadySent'));
      } else if (status === 3 || status === 4) {
        // 发送失败或设备限制
        setCodeSent(false);
        setError('邮件发送失败，请稍后再试');
      } else {
        // 默认处理
        setCodeSent(true);
        setError(null);
      }
    } catch (err) {
      log.error('发送验证码失败:', err);
      setError(err instanceof Error ? err.message : t('login.errors.sendCodeFailed'));
      setCodeSent(false);
    } finally {
      setCodeSending(false);
    }
  };

  // 登录提交
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!email || !code) {
      setError(t('login.errors.emailAndCodeRequired'));
      return;
    }

    if (!agreedToTerms) {
      setError(t('login.errors.agreementRequired'));
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      await verifyCodeAndLogin(email, code);

      // 等待存储操作完成并确保用户状态更新
      await new Promise(resolve => setTimeout(resolve, 500));

      // 登录成功后跳转到个人中心
      const state = location.state as LocationState;
      const from = state?.from || '/profile';

      // 使用 React Router 进行跳转，避免页面刷新
      navigate(from, { replace: true });

      // 如果 React Router 跳转失败，使用强制跳转作为备用
      setTimeout(() => {
        if (window.location.pathname !== from) {
          window.location.href = from;
        }
      }, 1000);
    } catch (err) {
      log.error('登录失败:', err);
      setError(err instanceof Error ? err.message : t('login.errors.loginFailed'));
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 py-12 md:py-20 flex items-center justify-center">
      <div className="mx-auto max-w-md px-4 w-full">
        <div className="bg-white/80 dark:bg-gray-800/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 dark:border-gray-700/50 p-8 transform transition-all duration-300 hover:shadow-3xl">
          {/* Logo/Icon */}
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
          </div>

          <h2 className="text-3xl font-bold text-center bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent mb-2">
            {t('login.title')}
          </h2>
          <p className="text-center text-gray-600 dark:text-gray-400 mb-8">
            {t('login.subtitle')}
          </p>
          
          {error && (
            <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 p-4 rounded-xl mb-6 flex items-center space-x-3 animate-slide-in-from-top-2">
              <svg className="w-5 h-5 text-red-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium">{error}</span>
            </div>
          )}

          {codeSent && !error && (
            <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 p-4 rounded-xl mb-6 flex items-center space-x-3 animate-slide-in-from-top-2">
              <svg className="w-5 h-5 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium">{t('login.codeSent')}</span>
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <label
                htmlFor="email"
                className="block text-sm font-semibold text-gray-700 dark:text-gray-300"
              >
                {t('login.email')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                  </svg>
                </div>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={codeSent}
                  placeholder={t('login.emailPlaceholder')}
                  className="w-full pl-10 pr-3 py-3 border border-gray-200 dark:border-gray-600 rounded-xl shadow-sm bg-white/50 dark:bg-gray-700/50 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:shadow-md"
                  required
                />
              </div>
              {!codeSent && (
                <button
                  type="button"
                  onClick={handleRequestCode}
                  disabled={codeSending || !email}
                  className="w-full mt-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-4 rounded-xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transform transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]"
                >
                  {codeSending ? (
                    <span className="flex justify-center items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {t('login.sending')}
                    </span>
                  ) : (
                    <span className="flex justify-center items-center">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                      {t('login.getVerificationCode')}
                    </span>
                  )}
                </button>
              )}
            </div>
            
            {codeSent && (
              <div className="space-y-2 animate-slide-in-from-bottom-4">
                <label
                  htmlFor="code"
                  className="block text-sm font-semibold text-gray-700 dark:text-gray-300"
                >
                  {t('login.verificationCode')}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2a2 2 0 012 2M9 7a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2V9a2 2 0 00-2-2H9z" />
                    </svg>
                  </div>
                  <input
                    id="code"
                    type="text"
                    value={code}
                    onChange={(e) => setCode(e.target.value)}
                    placeholder={t('login.verificationCodePlaceholder')}
                    maxLength={6}
                    className="w-full pl-10 pr-3 py-3 border border-gray-200 dark:border-gray-600 rounded-xl shadow-sm bg-white/50 dark:bg-gray-700/50 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:text-white transition-all duration-200 hover:shadow-md text-center text-lg font-mono tracking-widest"
                    required
                  />
                </div>
              </div>
            )}
            
            {/* 协议同意复选框 */}
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="flex items-center h-5">
                  <input
                    id="agree-terms"
                    type="checkbox"
                    checked={agreedToTerms}
                    onChange={(e) => setAgreedToTerms(e.target.checked)}
                    className="w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </div>
                <label htmlFor="agree-terms" className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                  {t('login.agreement.text')}
                  <Link
                    to="/user-agreement"
                    target="_blank"
                    className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 underline mx-1"
                  >
                    {t('login.agreement.userAgreement')}
                  </Link>
                  {t('login.agreement.and')}
                  <Link
                    to="/privacy-policy"
                    target="_blank"
                    className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 underline mx-1"
                  >
                    {t('login.agreement.privacyPolicy')}
                  </Link>
                </label>
              </div>
            </div>

            {codeSent && (
              <button
                type="submit"
                disabled={loading || !code || !agreedToTerms}
                className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold py-3 px-4 rounded-xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transform transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] animate-slide-in-from-bottom-4"
              >
                {loading ? (
                  <span className="flex justify-center items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {t('login.loginInProgress')}
                  </span>
                ) : (
                  <span className="flex justify-center items-center">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    {t('login.loginButton')}
                  </span>
                )}
              </button>
            )}
          </form>

          <div className="mt-8 text-center space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <div className="h-px bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent flex-1"></div>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">{t('login.secureLogin')}</span>
              <div className="h-px bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent flex-1"></div>
            </div>

            <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
              {t('login.autoRegisterNote')}
            </p>

            <div className="flex items-center justify-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center space-x-1">
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <span>{t('login.secureEncryption')}</span>
              </div>
              <div className="flex items-center space-x-1">
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                <span>{t('login.privacyProtection')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage; 
import { getAccessToken, isAccessTokenExpired, refreshAccessToken, getRefreshToken, removeAccessToken, removeRefreshToken } from './authService';
import { API_URL, API_KEY } from '../config/api';

// 缓存令牌刷新Promise，避免多个请求同时刷新令牌
let refreshTokenPromise: Promise<any> | null = null;
// 标记是否因刷新令牌失败而需要重新登录
let needRelogin = false;

/**
 * 创建带有自动令牌刷新功能的请求函数
 * @returns 增强的fetch函数
 */
export const createApiClient = () => {
  /**
   * 增强的fetch函数，自动处理令牌刷新
   * @param url 请求URL
   * @param options 请求选项
   * @returns 响应Promise
   */
  return async (url: string, options: RequestInit = {}): Promise<Response> => {
    // 如果已经确定需要重新登录，直接拒绝请求
    if (needRelogin) {
      console.error('用户需要重新登录，请求被拒绝');
      // 触发重新登录事件
      window.dispatchEvent(new CustomEvent('auth-relogin-required'));
      return Promise.reject(new Error('需要重新登录'));
    }
    
    // 准备请求头
    options.headers = options.headers || {};
    const headers = options.headers as Record<string, string>;
    
    // 添加公共请求头
    headers['Content-Type'] = headers['Content-Type'] || 'application/json';
    
    // 检查API密钥是否存在
    if (API_KEY) {
      headers['x-api-key'] = API_KEY;
    } else {
      console.error('⚠️ API请求缺少API密钥，这可能导致请求被拒绝。请检查环境变量配置。');
    }
    
    // 检查是否需要认证
    const accessToken = getAccessToken();
    if (accessToken) {
      // 检查令牌是否过期
      if (await isAccessTokenExpired()) {
        // 如果已经有刷新请求在进行中，等待它完成
        if (refreshTokenPromise === null) {
          const refreshToken = await getRefreshToken();
          if (refreshToken) {
            refreshTokenPromise = refreshAccessToken(refreshToken)
              .catch(error => {
                console.error('刷新令牌失败:', error);
                // 刷新失败，标记需要重新登录
                needRelogin = true;

                // 🔒 保守处理：只在确认是认证错误时才删除token
                const isAuthError = error instanceof Error &&
                  (error.message.includes('INVALID_TOKEN') ||
                   error.message.includes('TOKEN_EXPIRED') ||
                   error.message.includes('认证失败'));

                if (isAuthError) {
                  removeAccessToken();
                  removeRefreshToken();
                }

                // 触发重新登录事件
                window.dispatchEvent(new CustomEvent('auth-relogin-required'));
                return null;
              })
              .finally(() => {
                // 完成后，清除Promise以允许下次刷新
                refreshTokenPromise = null;
              });
          } else {
            console.error('无法刷新令牌：刷新令牌不存在');
            needRelogin = true;
            window.dispatchEvent(new CustomEvent('auth-relogin-required'));
          }
        }
        
        // 等待令牌刷新完成
        if (refreshTokenPromise) {
          await refreshTokenPromise;
        }
        
        // 获取新的访问令牌
        const newAccessToken = getAccessToken();
        if (newAccessToken) {
          headers['Authorization'] = `Bearer ${newAccessToken}`;
        } else {
          console.error('刷新令牌后未能获取新的访问令牌');
          if (!needRelogin) {
            needRelogin = true;
            window.dispatchEvent(new CustomEvent('auth-relogin-required'));
          }
        }
      } else {
        // 令牌未过期，直接使用
        headers['Authorization'] = `Bearer ${accessToken}`;
      }
    }
    
    // 确保URL以API基础路径开头
    if (!url.startsWith('http')) {
      // 移除开头的斜杠（如果有）
      const endpoint = url.startsWith('/') ? url.substring(1) : url;
      // 始终使用完整的API URL，通过环境变量获取
      url = `${API_URL}/${endpoint}`;
    }
    
    // 设置CORS选项
    options.mode = options.mode || 'cors';
    options.credentials = options.credentials || 'omit';
    
    // 发送请求
    
    try {
      // 直接发送请求
      const response = await fetch(url, options);
      
      // 处理响应
      
      // 处理认证错误
      if (response.status === 401) {
        console.error('认证失败 (401)：可能是令牌无效或已过期');
        
        // 获取请求中使用的token和当前存储的token
        const requestToken = headers['Authorization']?.replace('Bearer ', '') || '';
        const currentToken = await getAccessToken();

        // 如果请求中的token和当前存储的token不一致，可能是token已经被刷新过了
        if (requestToken && currentToken && requestToken !== currentToken) {
          // 更新请求头中的令牌
          headers['Authorization'] = `Bearer ${currentToken}`;
          // 重新发送请求
          return fetch(url, options);
        }
        
        // 尝试读取错误详情
        try {
          const errorData = await response.clone().json();
          console.error('服务器错误详情:', errorData);
          
          // 检查是否是令牌过期或无效
          const errorMessage = errorData.error || errorData.message || errorData.detail || '';
          const isTokenError = typeof errorMessage === 'string' && 
            (errorMessage.includes('token') || 
              errorMessage.includes('Token') || 
              errorMessage.includes('认证') || 
              errorMessage.includes('auth'));
              
          if (isTokenError) {
            // 如果还没有刷新过令牌，尝试刷新
            if (!refreshTokenPromise && !needRelogin) {
              const refreshToken = await getRefreshToken();
              if (refreshToken) {
                try {
                  // 主动尝试刷新令牌
                  await refreshAccessToken(refreshToken);
                  
                  // 获取新的访问令牌
                  const newToken = await getAccessToken();
                  if (newToken) {
                    // 更新请求头中的令牌
                    headers['Authorization'] = `Bearer ${newToken}`;

                    // 重新发送请求
                    return fetch(url, options);
                  }
                } catch (refreshError) {
                  console.error('尝试刷新令牌失败:', refreshError);
                  needRelogin = true;

                  // 🔒 保守处理：只在确认是认证错误时才删除token
                  const isAuthError = refreshError instanceof Error &&
                    (refreshError.message.includes('INVALID_TOKEN') ||
                     refreshError.message.includes('TOKEN_EXPIRED') ||
                     refreshError.message.includes('认证失败'));

                  if (isAuthError) {
                    removeAccessToken();
                    removeRefreshToken();
                  }

                  // 设置更有意义的错误提示
                  const errorMsg = refreshError instanceof Error
                    ? refreshError.message
                    : '登录已过期，请重新登录';

                  // 触发重新登录事件，带上错误信息
                  window.dispatchEvent(new CustomEvent('auth-relogin-required', {
                    detail: { message: errorMsg }
                  }));
                  
                  // 返回带有明确提示的响应
                  return new Response(JSON.stringify({
                    success: false,
                    error: errorMsg,
                    code: 'SESSION_EXPIRED'
                  }), {
                    status: 401,
                    headers: { 'Content-Type': 'application/json' }
                  });
                }
              } else {
                console.error('无法刷新令牌：刷新令牌不存在');
                needRelogin = true;
                
                // 触发重新登录事件
                window.dispatchEvent(new CustomEvent('auth-relogin-required', {
                  detail: { message: '登录已过期，请重新登录' }
                }));
                
                // 返回带有明确提示的响应
                return new Response(JSON.stringify({
                  success: false,
                  error: '登录已过期，请重新登录',
                  code: 'SESSION_EXPIRED'
                }), {
                  status: 401,
                  headers: { 'Content-Type': 'application/json' }
                });
              }
            } else if (needRelogin) {
              // 如果已经知道需要重新登录，直接返回友好的错误信息
              return new Response(JSON.stringify({
                success: false,
                error: '登录已过期，请重新登录',
                code: 'SESSION_EXPIRED'
              }), {
                status: 401,
                headers: { 'Content-Type': 'application/json' }
              });
            }
          }
        } catch (e) {
          console.error('无法解析错误响应:', e);
        }
      }
      
      return response;
    } catch (error) {
      console.error(`请求失败: ${url}`, error);
      throw error;
    }
  };
};

// 重置重新登录状态的方法，用于用户成功登录后调用
export const resetNeedRelogin = () => {
  needRelogin = false;
};

// 创建全局API客户端
export const apiClient = createApiClient(); 
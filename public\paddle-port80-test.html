<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle端口80测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #efe; border-color: #cfc; }
        .error { background-color: #fee; border-color: #fcc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 15px 25px; margin: 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background: #005a87; }
        .log { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; }
        .solution { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .current-url { background: #e7f3ff; padding: 10px; border-radius: 3px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Paddle CSP问题解决</h1>
        
        <div class="section error">
            <h2>❌ 当前问题</h2>
            <p><strong>CSP错误:</strong> frame-ancestors http://localhost</p>
            <p><strong>403错误:</strong> 域名白名单或权限问题</p>
        </div>

        <div class="section info">
            <h2>🌐 当前访问信息</h2>
            <div class="current-url">
                <p><strong>当前URL:</strong> <span id="current-url"></span></p>
                <p><strong>协议:</strong> <span id="protocol"></span></p>
                <p><strong>主机:</strong> <span id="hostname"></span></p>
                <p><strong>端口:</strong> <span id="port"></span></p>
            </div>
        </div>

        <div class="section warning">
            <h2>🔧 解决方案</h2>
            <div class="solution">
                <h3>方案1: 更新Paddle域名白名单</h3>
                <p>在Paddle Dashboard中添加:</p>
                <ul>
                    <li><code>http://localhost:5173</code></li>
                    <li><code>https://localhost:5173</code></li>
                    <li><code>http://localhost</code></li>
                    <li><code>https://localhost</code></li>
                </ul>
                
                <h3>方案2: 使用端口80</h3>
                <p>修改vite配置使用端口80:</p>
                <pre>server: { port: 80 }</pre>
                
                <h3>方案3: 使用代理</h3>
                <p>通过本地代理移除CSP头（已在vite.config.ts中配置）</p>
            </div>
        </div>

        <div class="section">
            <h2>🧪 新Token测试</h2>
            <p><strong>当前Token:</strong> <code>test_5c1f7fd4f3fb13fcf3c46516a93</code></p>
            <button onclick="testNewToken()">🚀 测试新Token</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section success" id="success-section" style="display: none;">
            <h2>🎉 测试成功！</h2>
            <div id="success-info"></div>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'test_5c1f7fd4f3fb13fcf3c46516a93',
            priceId: 'pri_01k1aenmy30gyzrq9wayftn5z4'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '5px';
            logEntry.style.padding = '5px';
            logEntry.style.borderRadius = '3px';
            
            if (type === 'success') {
                logEntry.style.backgroundColor = '#d4edda';
                logEntry.style.color = '#155724';
            } else if (type === 'error') {
                logEntry.style.backgroundColor = '#f8d7da';
                logEntry.style.color = '#721c24';
            } else {
                logEntry.style.backgroundColor = '#d1ecf1';
                logEntry.style.color = '#0c5460';
            }
            
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('success-section').style.display = 'none';
        }

        function showSuccess() {
            const infoDiv = document.getElementById('success-info');
            infoDiv.innerHTML = `
                <h3>✅ 新Token和配置验证成功！</h3>
                <p>支付功能正常工作，可以继续使用</p>
            `;
            document.getElementById('success-section').style.display = 'block';
        }

        async function testNewToken() {
            log('🚀 测试新Token和CSP配置...', 'info');
            log(`Token: ${config.token}`, 'info');
            log(`当前URL: ${window.location.href}`, 'info');
            
            try {
                log('🔧 初始化Paddle...', 'info');
                
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            log(`❌ Checkout错误: ${data.detail}`, 'error');
                            
                            if (data.detail && data.detail.includes('JWT')) {
                                log('❌ 仍然是JWT错误', 'error');
                            } else if (data.detail && data.detail.includes('403')) {
                                log('❌ 403错误 - 检查域名白名单', 'error');
                            }
                        } else if (data.type === 'checkout.loaded') {
                            log('🎉 支付页面加载成功！', 'success');
                            showSuccess();
                        } else if (data.name === 'checkout.closed' && data.data) {
                            const item = data.data.items?.[0];
                            if (item?.price_id && item?.product?.name) {
                                log('🎉 产品数据完整！', 'success');
                                log(`产品: ${item.product.name}, 价格: ${item.price_id}`, 'success');
                                showSuccess();
                            }
                        }
                    }
                });
                
                log('✅ Paddle初始化成功', 'success');
                
                // 测试支付
                log('💳 打开支付页面...', 'info');
                
                const testConfig = {
                    items: [{
                        priceId: config.priceId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                window.Paddle.Checkout.open(testConfig);
                log('✅ 支付请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 监听CSP错误
        window.addEventListener('securitypolicyviolation', function(e) {
            log(`🚨 CSP违规: ${e.violatedDirective} - ${e.blockedURI}`, 'error');
        });

        // 页面加载时显示当前URL信息
        window.onload = function() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('protocol').textContent = window.location.protocol;
            document.getElementById('hostname').textContent = window.location.hostname;
            document.getElementById('port').textContent = window.location.port || '默认端口';
            
            log('🔧 CSP问题解决测试页面已加载', 'info');
            log(`当前访问: ${window.location.href}`, 'info');
            
            if (window.location.port === '5173') {
                log('⚠️ 当前使用端口5173，可能导致CSP错误', 'warning');
                log('💡 建议在Paddle Dashboard中添加 http://localhost:5173 到域名白名单', 'info');
            }
        };
    </script>
</body>
</html>

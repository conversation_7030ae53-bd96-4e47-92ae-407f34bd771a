import { USERAPI_URL, createApiRequestOptions } from '../config/api';
import { getAccessToken } from './authService';
import { setStorageItem } from './storageAdapter';

// API配置已加载

export interface SubscriptionInfo {
  // 原有字段（兼容性）
  type?: string;
  status?: string | null;
  created_at?: number;
  updated_at?: number;
  next_bill_date?: number | null;

  // 新API字段
  subscription_id?: string;
  plan_id?: string;
  plan_name?: string;             // 计划名称
  is_trial?: boolean;             // 是否是试用订阅
  cancel_at_period_end?: boolean;
  current_period_start?: string | null;
  current_period_end?: string | null;
  can_cancel?: boolean;
  cancel_reason?: string | null;
  days_remaining?: number | null;

  // 试用期相关字段
  canceled_at?: string;           // 试用到期时间 (ISO 8601 格式)
  trial_ends_at?: string | null;  // 试用期结束时间 (ISO 8601 格式)
}

export interface UserProfile {
  user_id: string;
  email: string;
  subscription_info: SubscriptionInfo | null;
}

/**
 * 从本地缓存获取用户配置
 * @returns 用户配置或null
 */
export function getCachedUserProfile(): UserProfile | null {
  const userId = localStorage.getItem('user_id');
  const email = localStorage.getItem('user_email');
  const subscriptionInfoStr = localStorage.getItem('subscription_info');
  
  if (!userId || !email) return null;
  
  let subscriptionInfo = null;
  
  // 安全解析订阅信息
  if (subscriptionInfoStr) {
    try {
      // 排除无效值
      if (subscriptionInfoStr !== "0" && subscriptionInfoStr !== "undefined" && subscriptionInfoStr !== "null") {
        subscriptionInfo = JSON.parse(subscriptionInfoStr);
      }
    } catch (e) {
      console.warn('解析缓存的订阅信息失败:', e);
      // 解析失败，使用null
      subscriptionInfo = null;
    }
  }
  
  return {
    user_id: userId,
    email: email,
    subscription_info: subscriptionInfo
  };
}

/**
 * 缓存用户配置到本地存储
 * @param profile 用户配置
 */
export function cacheUserProfile(profile: UserProfile): void {
  if (!profile.user_id || !profile.email) {
    return;
  }
  
  setStorageItem('user_id', profile.user_id);
  setStorageItem('user_email', profile.email);
  
  if (profile.subscription_info) {
    setStorageItem('subscription_info', JSON.stringify(profile.subscription_info));
  } else {
    setStorageItem('subscription_info', null);
  }
}

/**
 * 获取当前用户信息
 * @returns 用户信息
 */
export async function getCurrentUserProfile(): Promise<UserProfile> {
  try {
    // 首先尝试从缓存获取
    const cachedProfile = getCachedUserProfile();
    if (cachedProfile) {
      return cachedProfile;
    }

    // 缓存没有，则请求API
    const token = await getAccessToken();
    if (!token) {
      console.error('未找到访问令牌');
      throw new Error('未找到访问令牌');
    }
    
    // 创建请求选项 - 使用与 authService 中相同的方法
    const requestOptions = createApiRequestOptions({
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      mode: 'cors',
      credentials: 'omit'
    });
    
    // 构建URL - 使用正确的用户API URL
    const url = `${USERAPI_URL}/user/me`;

    // 发送请求 - 直接使用fetch，与 authService 保持一致
    const response = await fetch(url, requestOptions);
    
    if (!response.ok) {
      let errorMessage = '获取用户信息失败';
      let errorData = null;
      try {
        const responseText = await response.text();

        // 尝试解析为JSON
        if (responseText) {
          try {
            errorData = JSON.parse(responseText);
            errorMessage = errorData.message || errorData.detail || errorData.error || errorMessage;
          } catch (jsonError) {
            errorMessage = responseText || errorMessage;
          }
        }
      } catch (e) {
        console.error('读取错误响应失败:', e);
      }
      throw new Error(`${errorMessage} (状态码: ${response.status})`);
    }

    const responseText = await response.text();

    let data;
    try {
      data = JSON.parse(responseText);
    } catch (parseError) {
      console.error('JSON解析失败:', parseError);
      throw new Error(`响应不是有效的JSON: ${responseText}`);
    }
    
    // 验证响应数据 - 检查各种可能的响应格式
    let userId = '';
    let email = '';
    let subscriptionInfo = null;
    
    // 处理不同的响应格式
    if (data.user_id && data.email) {
      // 标准格式
      userId = data.user_id;
      email = data.email;
      subscriptionInfo = data.subscription_info;
    } else if (data.id && data.email) {
      // 替代格式 1
      userId = data.id;
      email = data.email;
      subscriptionInfo = data.subscription_info;
    } else if (data.user_id && data.subscription_id) {
      // 新格式：直接包含订阅信息的响应
      userId = data.user_id;
      email = data.email || ''; // 如果没有email字段，使用空字符串

      // 将整个响应作为订阅信息，但排除user_id和email字段
      subscriptionInfo = {
        subscription_id: data.subscription_id,
        status: data.status,
        plan_id: data.plan_id,
        cancel_at_period_end: data.cancel_at_period_end,
        current_period_start: data.current_period_start,
        current_period_end: data.current_period_end,
        can_cancel: data.can_cancel,
        cancel_reason: data.cancel_reason,
        days_remaining: data.days_remaining,
        // 为了兼容性，添加一些别名字段
        type: data.plan_id,
        created_at: data.current_period_start ? new Date(data.current_period_start).getTime() / 1000 : undefined,
        updated_at: Date.now() / 1000,
        next_bill_date: data.current_period_end ? new Date(data.current_period_end).getTime() / 1000 : null
      };
    } else if (data.data && data.data.user_id && data.data.email) {
      // 嵌套格式
      userId = data.data.user_id;
      email = data.data.email;
      subscriptionInfo = data.data.subscription_info;
    } else if (data.data && data.data.id && data.data.email) {
      // 嵌套替代格式
      userId = data.data.id;
      email = data.data.email;
      subscriptionInfo = data.data.subscription_info;
    } else if (data.success && data.data && data.data.user_id && data.data.email) {
      // 成功响应嵌套格式
      userId = data.data.user_id;
      email = data.data.email;
      subscriptionInfo = data.data.subscription_info;
    } else if (data.success && data.data && data.data.id && data.data.email) {
      // 成功响应嵌套替代格式
      userId = data.data.id;
      email = data.data.email;
      subscriptionInfo = data.data.subscription_info;
    } else {
      console.error('用户信息响应数据格式不符合预期:', data);
      console.error('期望的字段: user_id/id, email');
      console.error('实际收到的字段:', Object.keys(data));
      console.error('完整响应数据:', JSON.stringify(data, null, 2));
      throw new Error(`用户信息响应数据格式不符合预期。期望字段: user_id/id, email。实际字段: ${Object.keys(data).join(', ')}`);
    }
    
    const profile: UserProfile = {
      user_id: userId,
      email: email,
      subscription_info: subscriptionInfo
    };

    // 缓存用户信息
    cacheUserProfile(profile);
    
    return profile;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    throw error;
  }
}

/**
 * 清除用户缓存数据
 */
export function clearUserCache(): void {
  localStorage.removeItem('user_id');
  localStorage.removeItem('user_email');
  localStorage.removeItem('subscription_info');
  
  // 触发 storage 事件，以便其他组件可以更新
  window.dispatchEvent(new Event('storage'));
}

// 添加一个变量来跟踪上次API请求的时间
let lastApiCallTime = 0;
const API_CALL_COOLDOWN = 3000; // 3秒冷却时间

/**
 * 获取当前登录用户的资料
 * @returns 用户资料
 */
export async function getUserProfile(): Promise<UserProfile> {
  try {
    // 检查是否在冷却期内
    const now = Date.now();
    if (now - lastApiCallTime < API_CALL_COOLDOWN) {
      // 尝试返回缓存的用户资料
      const cachedProfile = getCachedUserProfile();
      if (cachedProfile) {
        return cachedProfile;
      }
      // 如果没有缓存，继续请求API
    }
    
    // 更新最后请求时间
    lastApiCallTime = now;
    
    const accessToken = await getAccessToken();
    if (!accessToken) {
      throw new Error('未登录，无法获取用户资料');
    }

    // 使用createApiRequestOptions构建请求选项
    const requestOptions = createApiRequestOptions({
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`
      },
      mode: 'cors',
      credentials: 'omit'
    });
    
    const fullUrl = `${USERAPI_URL}/user/me`;
    const response = await fetch(fullUrl, requestOptions);

    if (!response.ok) {
      let errorDetail = '';
      try {
        const responseText = await response.text();
        if (responseText) {
          try {
            const errorData = JSON.parse(responseText);
            errorDetail = JSON.stringify(errorData);
          } catch (jsonError) {
            errorDetail = responseText;
          }
        }
      } catch (e) {
        // 忽略读取错误
      }
      throw new Error(`获取用户资料失败: ${response.status} ${response.statusText} ${errorDetail}`);
    }

    const responseText = await response.text();

    let data;
    try {
      data = JSON.parse(responseText);
    } catch (parseError) {
      console.error('用户资料JSON解析失败:', parseError);
      throw new Error(`用户资料响应不是有效的JSON: ${responseText}`);
    }

    // 处理不同的响应格式，构建标准的UserProfile对象
    let profile: UserProfile;

    if (data.user_id && data.subscription_id) {
      // 新格式：直接包含订阅信息的响应
      profile = {
        user_id: data.user_id,
        email: data.email || '',
        subscription_info: {
          subscription_id: data.subscription_id,
          status: data.status,
          plan_id: data.plan_id,
          cancel_at_period_end: data.cancel_at_period_end,
          current_period_start: data.current_period_start,
          current_period_end: data.current_period_end,
          can_cancel: data.can_cancel,
          cancel_reason: data.cancel_reason,
          days_remaining: data.days_remaining,
          // 为了兼容性，添加一些别名字段
          type: data.plan_id,
          created_at: data.current_period_start ? new Date(data.current_period_start).getTime() / 1000 : undefined,
          updated_at: Date.now() / 1000,
          next_bill_date: data.current_period_end ? new Date(data.current_period_end).getTime() / 1000 : null
        }
      };
    } else if (data.user_id && data.email) {
      // 标准格式：包含嵌套subscription_info的响应
      profile = {
        user_id: data.user_id,
        email: data.email,
        subscription_info: data.subscription_info || null
      };
    } else {
      // 如果都不匹配，尝试直接使用原始数据
      console.warn('未知的用户资料响应格式，尝试直接使用:', data);
      profile = data as UserProfile;
    }

    // 用户资料构建完成

    // 缓存用户资料
    cacheUserProfile(profile);

    return profile;
  } catch (error) {
    console.error('获取用户资料失败:', error);
    throw error;
  }
}

/**
 * 计算试用期剩余天数
 * @param trialEndsAt 试用期结束时间 (ISO 8601 格式)
 * @returns 剩余天数，如果已过期返回 0
 */
function calculateTrialDaysRemaining(trialEndsAt: string): number {
  try {
    const trialEndDate = new Date(trialEndsAt);
    const currentDate = new Date();
    const diffTime = trialEndDate.getTime() - currentDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays); // 确保不返回负数
  } catch (error) {
    console.error('解析试用期结束时间失败:', error);
    return 0;
  }
}

/**
 * 检查是否在试用期内
 * @param subscriptionInfo 订阅信息
 * @returns 是否在试用期内
 */
export function isInTrialPeriod(subscriptionInfo: SubscriptionInfo | null): boolean {
  if (!subscriptionInfo) {
    return false;
  }
  // 方法1: 检查订阅状态
  if (subscriptionInfo.status === 'trialing') {
    return true;
  }

  // 方法2: 检查试用期结束时间
  const trialEndsAt = subscriptionInfo.trial_ends_at || subscriptionInfo.canceled_at;
  if (trialEndsAt) {
    try {
      const trialEndDate = new Date(trialEndsAt);
      const currentDate = new Date();
      return currentDate < trialEndDate;
    } catch (error) {
      console.error('解析试用期时间失败:', error);
      return false;
    }
  }

  return false;
}

/**
 * 检查是否可以退订（基于试用期判断）
 * @param subscriptionInfo 订阅信息
 * @returns 是否可以退订和相关信息
 */
export function canCancelSubscription(subscriptionInfo: SubscriptionInfo | null): {
  canCancel: boolean;
  reason?: string;
  daysRemaining?: number | null;
} {
  if (!subscriptionInfo) {
    return {
      canCancel: false,
      reason: '无有效订阅'
    };
  }

  // 检查退订资格

  // 优先使用后端返回的 can_cancel 字段（如果存在）
  if (subscriptionInfo.can_cancel !== undefined) {
    const canCancel = subscriptionInfo.can_cancel === true;
    const reason = subscriptionInfo.cancel_reason || undefined;
    const daysRemaining = subscriptionInfo.days_remaining;

    return {
      canCancel,
      reason,
      daysRemaining
    };
  }

  // 如果后端没有返回 can_cancel，则前端计算
  const inTrialPeriod = isInTrialPeriod(subscriptionInfo);

  if (inTrialPeriod) {
    // 在试用期内，可以退订
    const trialEndsAt = subscriptionInfo.trial_ends_at || subscriptionInfo.canceled_at;
    const daysRemaining = trialEndsAt ? calculateTrialDaysRemaining(trialEndsAt) : null;

    return {
      canCancel: true,
      reason: undefined,
      daysRemaining
    };
  } else {
    // 试用期外，不能退订
    return {
      canCancel: false,
      reason: '试用期已结束，无法退订',
      daysRemaining: 0
    };
  }
}

/**
 * 取消订阅 - 通过后端 /user/me/qx 接口
 * @returns 取消订阅的结果
 */
export async function cancelSubscription(): Promise<any> {
  try {
    const accessToken = await getAccessToken();
    if (!accessToken) {
      throw new Error('未登录，无法取消订阅');
    }

    // 构建请求选项 - 按照新接口规范
    const requestOptions = {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      mode: 'cors' as RequestMode,
      credentials: 'omit' as RequestCredentials,
      body: JSON.stringify({
        confirm: true  // 固定值，确认取消订阅
      })
    };

    // 使用新的后端退订接口 (直接访问，不带 /api 前缀)
    const fullUrl = `${USERAPI_URL}/user/me/qx`;
    const response = await fetch(fullUrl, requestOptions);

    const responseText = await response.text();

    let result;
    try {
      result = JSON.parse(responseText);
    } catch (parseError) {
      console.error('取消订阅响应JSON解析失败:', parseError);
      throw new Error(`取消订阅响应不是有效的JSON: ${responseText}`);
    }

    // 新接口总是返回 HTTP 200，通过 success 字段判断结果
    if (!response.ok) {
      throw new Error(`HTTP 错误: ${response.status} ${response.statusText}`);
    }

    // 检查业务逻辑结果
    if (result.success) {
      // 取消订阅成功后，清除本地缓存，强制重新获取用户信息
      clearUserCache();
    }

    return result;
  } catch (error) {
    console.error('❌ 后端退订失败:', error);
    throw error;
  }
}
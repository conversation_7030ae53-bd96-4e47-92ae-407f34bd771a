const translation = {
  // 通用
  common: {
    loading: "加载中...",
    refresh: "刷新",
    logout: "退出登录",
    login: "登录",
    processing: "处理中...",
    error: "错误",
    success: "成功"
  },
  
  // 导航
  nav: {
    home: "首页",
    features: "功能",
    pricing: "定价",
    about: "关于",
    profile: "个人中心"
  },
  
  // 页脚
  footer: {
    description: "智能自动填充浏览器扩展，提升您的网页操作效率，告别重复输入的烦恼。",
    services: "服务",
    contactUs: "联系我们",
    changelog: "更新日志",
    about: "关于",
    userAgreement: "用户协议",
    privacyPolicy: "隐私政策",
    refundPolicy: "退款政策",
    followUs: "关注我们",
    copyright: "版权所有 2025. AutoFill. 保留所有权利。"
  },
  
  // 首页
  home: {
    title: "安装自动填充助手",
    subtitle: "提升80%的输入效率",
    description: "告别重复输入，一键智能填充，网页效率革命者！精准快速的内容匹配输入，全网通用，复杂表单秒级完成，让您可以专注更有价值的工作。",
    getStarted: "获取自动填充助手",
    features: {
      fast: "快",
      fastDesc: "省时省力，效率飞跃",
      accurate: "准",
      accurateDesc: "智能识别，准确无误",
      flexible: "灵活",
      flexibleDesc: "随心定制，满足所需",
      safe: "安全",
      safeDesc: "安全无忧，简单上手",
    },
    faq: {
      title: "常见问题",
      questions: [
        {
          question: "如何安装和使用插件？",
          answer: "1) 在Chrome/Firefox/Edge商店搜索插件名；2) 点击'添加至浏览器'；3) 安装后点击浏览器工具栏图标打开管理后台；4) 开始添加预设内容或配置网站填充规则。链接到详细教程页面。"
        },
        {
          question: "支持哪些浏览器？",
          answer: "Chrome, Edge已经上架, Firefox,Safari正在支持中，如果您需要我们支持其他的浏览器，请联系我们（）。"
        },
        {
          question: "'网站填充管理'功能是如何识别输入框的？准确率高吗？",
          answer: "我们主要通过识别输入框前的固定标签文案（K）来定位目标输入框。对于结构良好、标签清晰的网页表单，准确率非常高。对于特殊或动态生成的表单，可能需要用户手动确认或调整规则。"
        },
        {
          question: "我的预设数据和网站配置安全吗？存储在哪里？",
          answer: "用户数据安全是我们的首要考虑。所有预设内容和网站配置默认优先存储在您的浏览器本地。另外我们提供的也有云端同步备份，云同步是可选功能；数据在传输和存储时均采用强加密，我们不会将您的数据用于任何其他目的，请链接到详细的隐私政策。"
        },
        {
          question: "这个插件适合哪些人群或场景？",
          answer: "非常适合有以下工作需求的人：经常需要填写表单的用户（注册、购物、报销、申请）。需要在不同网站重复输入相同信息的用户（如地址、联系方式）。客服、销售、数据录入等需要快速输入标准回复或信息的岗位。开发者、测试人员需要填充测试数据。任何希望提升网页操作效率、减少重复劳动的人。"
        },
        {
          question: "是免费的吗？有高级功能吗？",
          answer: "基础版免费，包含核心的快速填充管理和网站填充管理功能，满足大部分用户需求。提供高级版/专业版（可选），包含更多高级功能，如：云同步，去广告（广告功能尚未上线），适配更多的网站和分组等。"
        }
      ]
    }
  },
  
  // 关于页面
  about: {
    title: "关于我们",
    content: "这是关于我们页面的占位内容。实际的团队和公司介绍内容将在后期添加。"
  },
  
  // 功能页面
  features: {
    title: "功能特点",
    subtitle: "让填充表单变得轻松简单",
    description: "自动填充助手提供多种强大功能，帮助您在不同网站上快速准确地输入信息，大幅提升您的工作效率。",
    quickFill: {
      title: "快速填充",
      description: "预设常用内容，右键一键输入，再也不用重复输入相同的信息。",
      features: [
        "支持无限条目的预设内容",
        "分组管理，轻松找到所需内容",
        "右键菜单快速访问",
        "支持纯文本、格式化文本和富文本"
      ]
    },
    siteFill: {
      title: "网站填充",
      description: "智能识别网页表单结构，根据关键词匹配自动填充表单内容。",
      features: [
        "通过标签文案（K）自动识别输入框",
        "自动匹配并填充对应内容（V）",
        "支持复杂表单和多页表单",
        "高准确率的智能匹配算法"
      ]
    },
    management: {
      title: "便捷管理",
      description: "直观的用户界面，轻松管理您的所有预设内容和填充规则。",
      features: [
        "拖拽式分组和条目管理",
        "搜索和筛选功能",
        "一键导入/导出配置",
        "网站规则的创建和编辑"
      ]
    },
    security: {
      title: "安全保障",
      description: "您的数据安全是我们的首要考虑，所有数据默认存储在本地。",
      features: [
        "本地优先存储策略",
        "可选的加密云同步",
        "严格的数据隐私保护",
        "完全控制您的数据"
      ]
    },
    compatibility: {
      title: "广泛兼容",
      description: "支持主流浏览器和各类网站，确保您在任何地方都能使用。",
      features: [
        "支持Chrome和Edge浏览器",
        "Firefox和Safari即将支持",
        "适配各类表单结构",
        "持续更新以支持更多网站"
      ]
    },
    content: "自动填充助手是您提高网页输入效率的得力助手，告别重复输入，专注更有价值的工作。"
  },
  
  // 定价页面
  pricing: {
    chooseYour: "选择您的",
    subscriptionPlan: "订阅计划",
    description: "自动填充功能可帮助您快速填写表单，并通过在网站上自动输入信息来节省时间。",
    premium: {
      title: "高级版",
      price: "$9.99",
      period: "/年",
      trial: "30天试用",
      button: "获取高级版 (年度)"
    },
    free: {
      title: "免费版",
      price: "$0",
      period: "/年",
      forever: "永久免费",
      button: "继续使用免费版"
    },
    features: {
      cloudSync: "云同步",
      dataPrivacy: "数据隐私保护",
      unlimitedGroups: "无限制分组数量",
      unlimitedItems: "每个分组无限条预设",
      unlimitedWebsites: "无限制网站数量",
      unlimitedKeys: "每个网站无限制子页面",
      unlimitedRules: "每个子页面无限制规则",
      devices: "多设备同步",
      limitedGroups: "最多3个分组",
      limitedItems: "每个分组最多10条记录",
      limitedWebsites: "最多3个网站",
      limitedKeys: "每个网站最多3个子网站",
      limitedRules: "每个子网站最多10条规则"
    },
    notifications: {
      success: "订阅成功！感谢您的支持！",
      cancelled: "订阅已取消",
      error: "处理订阅时出错。请稍后再试。",
      alreadySubscribed: "您已订阅，无法重复支付，感谢您的支持！"
    },
    limits: {
      quickFillLimited: "此预设内容已被限制，请升级为订阅用户以使用完整功能",
      websiteParseLimited: "此网站解析功能已被限制，请升级为订阅用户",
      upgradePrompt: "升级为高级版用户，解锁所有功能",
      upgradeButton: "立即升级"
    }
  },
  
  // 登录页面
  login: {
    title: "欢迎回来",
    subtitle: "登录到您的账户",
    quickLogin: "快速登录",
    quickLoginSubtitle: "验证身份以继续使用",
    email: "电子邮箱",
    emailPlaceholder: "请输入您的邮箱地址",
    verificationCode: "验证码",
    verificationCodePlaceholder: "请输入6位验证码",
    getVerificationCode: "获取验证码",
    resendVerificationCode: "重新获取验证码",
    sending: "发送中...",
    verifying: "验证中...",
    loginButton: "立即登录",
    loginInProgress: "登录中...",
    codeSent: "验证码已发送，请查收邮件",
    codeAlreadySent: "已发送过验证码，请检查邮箱或等待一段时间后重试",
    backToEmail: "返回修改",
    secureLogin: "安全登录",
    autoRegisterNote: "使用验证码登录，如果账户不存在将自动注册",
    secureEncryption: "安全加密",
    privacyProtection: "隐私保护",
    countdownText: "秒后可重新获取",
    agreement: {
      text: "我已阅读并同意",
      userAgreement: "用户服务协议",
      and: "和",
      privacyPolicy: "隐私政策",
      mustAgree: "请先同意用户服务协议和隐私政策"
    },
    errors: {
      emailRequired: "请输入电子邮箱",
      codeRequired: "请输入验证码",
      emailAndCodeRequired: "请输入电子邮箱和验证码",
      sendCodeFailed: "发送验证码失败，请稍后重试",
      loginFailed: "登录失败，请检查您的验证码并重试",
      agreementRequired: "请先同意用户服务协议和隐私政策"
    }
  },

  // 个人中心页面
  profile: {
    title: "我的账户",
    welcome: "欢迎回来",
    memberSince: "注册时间",
    lastLogin: "最后登录",
    basic: {
      title: "基本信息",
      userId: "用户ID",
      email: "账号",
      notSet: "未设置",
      joinDate: "加入日期",
      accountType: "账户类型",
      status: "账户状态",
      active: "正常",
      verified: "已验证"
    },
    subscription: {
      title: "订阅信息",
      status: "订阅状态",
      subscribed: "已订阅",
      active: "已激活",
      abnormal: "状态异常",
      paused: "已暂停",
      cancelled: "已取消",
      past_due: "付款逾期",
      unknown: "状态未知",
      nextBilling: "下次计费日期",
      startDate: "订阅开始日期",
      updatePayment: "更新支付方式",
      cancel: "取消订阅",
      cancelSubscription: "退订",
      cancelling: "取消中...",
      daysRemaining: "剩余 {days} 天退订期限",
      trialCanCancel: "试用期可随时取消",
      notSubscribed: "未订阅",
      noActiveSubscription: "暂无有效订阅",
      viewPlans: "查看计划",
      subscribe: "立即订阅",
      subscribePlan: "订阅套餐",
      goSubscribe: "去订阅",
      plan: "当前套餐",
      subtitle: "选择适合您的计划，享受更好的使用体验",
      recommended: "推荐",
      premium: "高级版",
      free: "免费版",
      features: {
        unlimitedGroups: "无限制分组数量",
        unlimitedWebsites: "无限制网站数量",
        unlimitedSubpages: "每个网站无限制子页面",
        unlimitedRules: "每个子页面无限制规则",
        unlimitedPresets: "每个分组无限条预设"
      },
             freeFeatures: {
         limitedGroups: "最多3个分组",
         limitedPresets: "每个分组最多10条记录",
         limitedWebsites: "最多3个网站",
         limitedSubpages: "每个网站最多3个子网站",
         limitedRules: "每个子网站最多10条规则"
       },
      billing: {
        nextBillingDate: "下次计费日期",
        billingDescription: "您将在每次续费时被收取 $9.99，直到您通过提交帮助台请求取消订阅。如果您取消，之前的费用将不予退还，但您可以继续使用服务直到您付费期限结束。",
        yearSuffix: "/年",
        monthSuffix: "/月"
      },
      labels: {
        plan: "套餐",
        premium: "高级版",
        free: "免费版",
        freePlan: "免费计划"
      },
      freePlan: {
        price: "$0",
        forever: "永久免费",
        description: "适合个人用户的基础功能"
      },
      premiumPlan: {
        price: "$9.99",
        description: "专业用户的最佳选择"
      }
    },
    stats: {
      title: "使用统计",
      totalLogins: "总登录次数",
      daysActive: "活跃天数",
      lastActive: "最后活跃",
      dataUsage: "数据使用量",
      featuresUsed: "已使用功能"
    },
    quickActions: {
      title: "快捷操作",
      manageSubscription: "管理订阅",
      downloadData: "下载数据",
      contactSupport: "联系客服",
      viewDocumentation: "查看文档",
      reportIssue: "报告问题",
      shareApp: "分享应用"
    },
    security: {
      title: "安全设置",
      changePassword: "修改密码",
      twoFactor: "两步验证",
      loginDevices: "登录设备",
      privacySettings: "隐私设置",
      dataExport: "数据导出",
      deleteAccount: "删除账户"
    },
    loading: "正在加载用户信息...",
    reload: "重新加载",
    refresh: "刷新",
    refreshSuccess: "刷新成功",
    logout: "退出登录"
  },
  
  // 设置页面
  settings: {
    general: "常规设置",
    accountStatus: "账号状态",
    notLoggedIn: "未登录",
    loggedIn: "已登录",
    refresh: "刷新",
    logout: "退出登录"
  },
  
  // 错误显示
  error: {
    refreshPage: "刷新页面",
    apiConfig: {
      title: "API配置错误",
      checkEnv: "请检查您的环境变量，确保正确设置了API URL和API密钥。",
      createFile: "在项目根目录创建.env.local文件并添加：",
      reload: "重新加载"
    }
  }
};

export default translation; 
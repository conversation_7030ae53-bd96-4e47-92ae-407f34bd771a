/**
 * 刷新令牌 API 代理函数
 * 处理 /api/refresh-token 路径的请求
 */
export async function onRequest(context) {
  // 从环境变量获取基础 URL
  const apiBaseUrl = context.env.API_BASE_URL || "https://autoapi.utilityapp.site";
  
  // 获取请求信息
  const { request } = context;
  
  // 构建目标 URL
  const targetUrl = `${apiBaseUrl}/refresh-token`;
  console.log(`转发刷新令牌请求到: ${targetUrl}`);
  
  try {
    // 克隆原始请求
    const requestInit = {
      method: request.method,
      headers: new Headers(request.headers),
    };
    
    // 如果是 POST 请求，需要包含请求体
    if (request.method === "POST") {
      const contentType = request.headers.get("Content-Type");
      if (contentType && contentType.includes("application/json")) {
        // 克隆 JSON 请求体
        const body = await request.json();
        requestInit.body = JSON.stringify(body);
        requestInit.headers.set("Content-Type", "application/json");
      } else {
        // 其他类型请求体直接传递
        requestInit.body = await request.arrayBuffer();
      }
    }
    
    // 移除可能导致问题的头信息
    requestInit.headers.delete("host");
    
    // 发送请求到实际 API
    const response = await fetch(targetUrl, requestInit);
    
    // 构建响应
    const responseInit = {
      status: response.status,
      statusText: response.statusText,
      headers: new Headers(response.headers)
    };
    
    // 获取响应体
    const responseBody = await response.arrayBuffer();
    
    // 返回响应
    return new Response(responseBody, responseInit);
  } catch (error) {
    console.error(`处理刷新令牌请求出错: ${error.message}`);
    return new Response(JSON.stringify({ 
      error: "中间层处理请求失败", 
      message: error.message 
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json"
      }
    });
  }
} 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle域名测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .log { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Paddle域名配置测试</h1>
        
        <div class="section info">
            <h2>📋 当前环境信息</h2>
            <pre id="env-info"></pre>
        </div>

        <div class="section warning">
            <h2>⚠️ 域名白名单配置</h2>
            <p>请确保在Paddle Dashboard中配置了以下域名：</p>
            <ul>
                <li><code>autofill.utilityapp.site</code> - 生产环境</li>
                <li><code>localhost:5174</code> - 开发环境</li>
                <li><code>localhost:5173</code> - 备用端口</li>
                <li><code>localhost</code> - 本地开发</li>
                <li><code>127.0.0.1</code> - IP访问</li>
            </ul>
            <p><strong>配置路径：</strong> Paddle Dashboard > Developer tools > Checkout settings > Allowed domains</p>
        </div>

        <div class="section">
            <h2>🧪 测试步骤</h2>
            <button onclick="testPaddleSDK()">1. 测试Paddle SDK加载</button>
            <button onclick="testPaddleInit()">2. 测试Paddle初始化</button>
            <button onclick="testCheckoutOpen()">3. 测试支付页面打开</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'test_459728092d35d36bc173c60e52d',
            productId: 'pri_01jxa2azwznm06dqbptzagtm0z'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        function showEnvInfo() {
            const envInfo = {
                domain: window.location.hostname,
                port: window.location.port,
                protocol: window.location.protocol,
                fullUrl: window.location.href,
                userAgent: navigator.userAgent.substring(0, 100) + '...',
                token: config.token,
                productId: config.productId
            };
            document.getElementById('env-info').textContent = JSON.stringify(envInfo, null, 2);
        }

        function testPaddleSDK() {
            log('📦 测试Paddle SDK加载...', 'info');
            
            if (window.Paddle) {
                log('✅ Paddle SDK已加载', 'success');
                log(`Paddle对象: ${typeof window.Paddle}`, 'info');
                
                if (window.Paddle.Initialize) {
                    log('✅ Paddle.Initialize方法可用', 'success');
                } else {
                    log('❌ Paddle.Initialize方法不可用', 'error');
                }
                
                if (window.Paddle.Checkout) {
                    log('✅ Paddle.Checkout对象可用', 'success');
                } else {
                    log('❌ Paddle.Checkout对象不可用', 'error');
                }
            } else {
                log('❌ Paddle SDK未加载', 'error');
            }
        }

        function testPaddleInit() {
            log('🚀 测试Paddle初始化...', 'info');
            
            if (!window.Paddle) {
                log('❌ Paddle SDK未加载，请先运行测试1', 'error');
                return;
            }

            try {
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                    }
                });
                log('✅ Paddle初始化成功', 'success');
            } catch (error) {
                log(`❌ Paddle初始化失败: ${error.message}`, 'error');
                
                if (error.message.includes('403') || error.message.includes('Forbidden')) {
                    log('🔍 403错误通常是域名白名单问题', 'warning');
                    log('💡 请检查Paddle Dashboard中的域名配置', 'warning');
                }
            }
        }

        function testCheckoutOpen() {
            log('💳 测试支付页面打开...', 'info');
            
            if (!window.Paddle || !window.Paddle.Checkout) {
                log('❌ Paddle未正确初始化，请先运行前面的测试', 'error');
                return;
            }

            try {
                window.Paddle.Checkout.open({
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }],
                    customer: {
                        email: '<EMAIL>'
                    },
                    settings: {
                        displayMode: 'overlay',
                        theme: 'light',
                        locale: 'zh'
                    }
                });
                log('✅ 支付页面打开成功', 'success');
            } catch (error) {
                log(`❌ 支付页面打开失败: ${error.message}`, 'error');
                
                if (error.message.includes('403') || error.message.includes('Forbidden')) {
                    log('🔍 这确认了是域名白名单问题', 'error');
                    log('💡 请在Paddle Dashboard中添加当前域名到白名单', 'warning');
                }
            }
        }

        // 监听Paddle事件
        window.addEventListener('paddle-loaded', function() {
            log('🎯 Paddle SDK加载完成事件触发', 'success');
        });

        window.addEventListener('paddle-checkout-error', function(event) {
            log(`🚨 Paddle结账错误事件: ${JSON.stringify(event.detail)}`, 'error');
        });

        // 页面加载完成
        window.onload = function() {
            showEnvInfo();
            log('🎯 Paddle域名测试页面已加载', 'info');
            log('💡 请按顺序点击测试按钮', 'info');
        };
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle 调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .log { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Paddle 调试工具</h1>
        
        <div class="section">
            <h2>📋 当前配置</h2>
            <div id="config-info"></div>
        </div>

        <div class="section">
            <h2>🧪 测试步骤</h2>
            <button onclick="testToken()">1. 测试 Token 有效性</button>
            <button onclick="testSDKLoad()">2. 测试 SDK 加载</button>
            <button onclick="testInitialize()">3. 测试初始化</button>
            <button onclick="testCheckout()">4. 测试支付页面</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 调试日志</h2>
            <div id="debug-log" class="log"></div>
        </div>
    </div>

    <script>
        // 配置信息
        const config = {
            PADDLE_ENVIRONMENT: 'sandbox',
            PADDLE_CLIENT_TOKEN: 'test_8064b08184b64e6031ae8c7dd5a',
            PADDLE_PRODUCT_YEARLY: 'pri_01jxa2azwznm06dqbptzagtm0z',
            PADDLE_VENDOR_ID: '32634'
        };

        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        // 显示配置信息
        function showConfig() {
            const configDiv = document.getElementById('config-info');
            configDiv.innerHTML = `
                <pre>${JSON.stringify(config, null, 2)}</pre>
                <p><strong>Token 长度:</strong> ${config.PADDLE_CLIENT_TOKEN.length} 字符</p>
                <p><strong>Token 格式:</strong> ${config.PADDLE_CLIENT_TOKEN.startsWith('test_') ? '✅ 正确 (test_)' : '❌ 错误'}</p>
            `;
        }

        // 测试 Token 有效性
        async function testToken() {
            log('🔑 开始测试 Token 有效性...', 'info');
            
            if (!config.PADDLE_CLIENT_TOKEN) {
                log('❌ Token 未配置', 'error');
                return;
            }

            if (!config.PADDLE_CLIENT_TOKEN.startsWith('test_')) {
                log('❌ Token 格式错误，应该以 test_ 开头', 'error');
                return;
            }

            if (config.PADDLE_CLIENT_TOKEN.length < 30) {
                log('⚠️ Token 长度可能不正确，标准长度应该是 test_ + 27个字符', 'error');
                return;
            }

            log('✅ Token 格式看起来正确', 'success');
            
            // 尝试直接调用 Paddle API 验证 token
            try {
                const response = await fetch('https://sandbox-api.paddle.com/products', {
                    headers: {
                        'Authorization': `Bearer ${config.PADDLE_CLIENT_TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`API 响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.status === 401) {
                    log('❌ Token 无效或已过期', 'error');
                } else if (response.status === 403) {
                    log('❌ Token 权限不足或域名未配置', 'error');
                } else if (response.ok) {
                    log('✅ Token 验证成功', 'success');
                }
                
            } catch (error) {
                log(`❌ API 调用失败: ${error.message}`, 'error');
            }
        }

        // 测试 SDK 加载
        function testSDKLoad() {
            log('📦 开始测试 Paddle SDK 加载...', 'info');
            
            if (window.Paddle) {
                log('✅ Paddle SDK 已加载', 'success');
                log(`Paddle 版本: ${window.Paddle.version || '未知'}`, 'info');
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdn.paddle.com/paddle/v2/paddle.js';
            script.async = true;
            
            script.onload = () => {
                if (window.Paddle) {
                    log('✅ Paddle SDK 加载成功', 'success');
                    log(`Paddle 版本: ${window.Paddle.version || '未知'}`, 'info');
                } else {
                    log('❌ Paddle SDK 加载失败', 'error');
                }
            };
            
            script.onerror = () => {
                log('❌ Paddle SDK 脚本加载失败', 'error');
            };
            
            document.head.appendChild(script);
            log('🔄 正在加载 Paddle SDK...', 'info');
        }

        // 测试初始化
        function testInitialize() {
            log('🚀 开始测试 Paddle 初始化...', 'info');
            
            if (!window.Paddle) {
                log('❌ Paddle SDK 未加载，请先测试 SDK 加载', 'error');
                return;
            }

            try {
                window.Paddle.Initialize({
                    token: config.PADDLE_CLIENT_TOKEN
                });
                log('✅ Paddle 初始化成功', 'success');
            } catch (error) {
                log(`❌ Paddle 初始化失败: ${error.message}`, 'error');
            }
        }

        // 测试支付页面
        function testCheckout() {
            log('💳 开始测试支付页面...', 'info');
            
            if (!window.Paddle) {
                log('❌ Paddle SDK 未加载', 'error');
                return;
            }

            if (!window.Paddle.Checkout) {
                log('❌ Paddle.Checkout 不可用', 'error');
                return;
            }

            try {
                const checkoutOptions = {
                    items: [{
                        priceId: config.PADDLE_PRODUCT_YEARLY,
                        quantity: 1
                    }],
                    customer: {
                        email: '<EMAIL>'
                    }
                };

                log('📋 支付配置:', 'info');
                log(`<pre>${JSON.stringify(checkoutOptions, null, 2)}</pre>`, 'info');

                window.Paddle.Checkout.open(checkoutOptions);
                log('✅ 支付页面调用成功', 'success');
                
            } catch (error) {
                log(`❌ 支付页面测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示配置
        window.onload = function() {
            showConfig();
            log('🎯 Paddle 调试工具已加载', 'info');
            log('💡 请按顺序点击测试按钮进行诊断', 'info');
        };
    </script>
</body>
</html>

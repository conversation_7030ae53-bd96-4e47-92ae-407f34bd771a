/**
 * 订阅限制检查工具
 * 用于检查用户订阅状态并应用相应的功能限制
 */

// 免费版限制
const FREE_LIMITS = {
  maxGroups: 3,
  maxPresetsPerGroup: 10,
  maxWebsites: 3,
  maxSubpagesPerWebsite: 3,
  maxRulesPerSubpage: 10
};

/**
 * 检查用户是否为付费用户
 * @param {Function} callback 回调函数，参数为 (isPremium, subscriptionInfo)
 */
function checkSubscriptionStatus(callback) {
  chrome.storage.local.get(['subscription_info'], function(result) {
    if (chrome.runtime.lastError) {
      console.error('获取订阅信息失败:', chrome.runtime.lastError.message);
      callback(false, null);
      return;
    }

    const subscriptionInfo = result.subscription_info;
    
    if (!subscriptionInfo) {
      // 没有订阅信息，为免费用户
      callback(false, null);
      return;
    }

    try {
      const subscription = typeof subscriptionInfo === 'string' 
        ? JSON.parse(subscriptionInfo) 
        : subscriptionInfo;

      // 检查订阅状态
      const isActive = subscription.status === 'active';
      const isNotExpired = subscription.expiration_time && 
        new Date(subscription.expiration_time) > new Date();

      const isPremium = isActive && isNotExpired;
      
      console.log('订阅状态检查:', {
        status: subscription.status,
        expiration: subscription.expiration_time,
        isPremium: isPremium
      });

      callback(isPremium, subscription);
    } catch (error) {
      console.error('解析订阅信息失败:', error);
      callback(false, null);
    }
  });
}

/**
 * 获取用户数据统计
 * @param {Function} callback 回调函数，参数为数据统计对象
 */
function getUserDataStats(callback) {
  chrome.storage.local.get(['quickFillData', 'websiteRules'], function(result) {
    if (chrome.runtime.lastError) {
      console.error('获取用户数据失败:', chrome.runtime.lastError.message);
      callback({
        groupCount: 0,
        totalPresets: 0,
        websiteCount: 0,
        totalSubpages: 0,
        totalRules: 0
      });
      return;
    }

    const stats = {
      groupCount: 0,
      totalPresets: 0,
      websiteCount: 0,
      totalSubpages: 0,
      totalRules: 0
    };

    // 统计快速填充数据
    if (result.quickFillData) {
      try {
        const quickFillData = typeof result.quickFillData === 'string' 
          ? JSON.parse(result.quickFillData) 
          : result.quickFillData;
        
        if (quickFillData.groups && Array.isArray(quickFillData.groups)) {
          stats.groupCount = quickFillData.groups.length;
          stats.totalPresets = quickFillData.groups.reduce((total, group) => {
            return total + (group.presets ? group.presets.length : 0);
          }, 0);
        }
      } catch (error) {
        console.error('解析快速填充数据失败:', error);
      }
    }

    // 统计网站规则数据
    if (result.websiteRules) {
      try {
        const websiteRules = typeof result.websiteRules === 'string' 
          ? JSON.parse(result.websiteRules) 
          : result.websiteRules;
        
        if (Array.isArray(websiteRules)) {
          stats.websiteCount = websiteRules.length;
          stats.totalSubpages = websiteRules.reduce((total, website) => {
            return total + (website.subpages ? website.subpages.length : 0);
          }, 0);
          stats.totalRules = websiteRules.reduce((total, website) => {
            if (website.subpages && Array.isArray(website.subpages)) {
              return total + website.subpages.reduce((subTotal, subpage) => {
                return subTotal + (subpage.rules ? subpage.rules.length : 0);
              }, 0);
            }
            return total;
          }, 0);
        }
      } catch (error) {
        console.error('解析网站规则数据失败:', error);
      }
    }

    callback(stats);
  });
}

/**
 * 过滤快速填充数据，只保留免费版限制内的数据
 * @param {Object} quickFillData 原始快速填充数据
 * @returns {Object} 过滤后的数据
 */
function filterQuickFillData(quickFillData) {
  if (!quickFillData || !quickFillData.groups) {
    return quickFillData;
  }

  const filteredData = {
    ...quickFillData,
    groups: quickFillData.groups.slice(0, FREE_LIMITS.maxGroups).map(group => ({
      ...group,
      presets: group.presets ? group.presets.slice(0, FREE_LIMITS.maxPresetsPerGroup) : []
    }))
  };

  return filteredData;
}

/**
 * 过滤网站规则数据，只保留免费版限制内的数据
 * @param {Array} websiteRules 原始网站规则数据
 * @returns {Array} 过滤后的数据
 */
function filterWebsiteRules(websiteRules) {
  if (!Array.isArray(websiteRules)) {
    return websiteRules;
  }

  return websiteRules.slice(0, FREE_LIMITS.maxWebsites).map(website => ({
    ...website,
    subpages: website.subpages 
      ? website.subpages.slice(0, FREE_LIMITS.maxSubpagesPerWebsite).map(subpage => ({
          ...subpage,
          rules: subpage.rules ? subpage.rules.slice(0, FREE_LIMITS.maxRulesPerSubpage) : []
        }))
      : []
  }));
}

/**
 * 检查是否超出免费版限制
 * @param {Object} stats 数据统计
 * @returns {Object} 限制检查结果
 */
function checkFreeLimits(stats) {
  return {
    groupsExceeded: stats.groupCount > FREE_LIMITS.maxGroups,
    presetsExceeded: stats.totalPresets > (FREE_LIMITS.maxGroups * FREE_LIMITS.maxPresetsPerGroup),
    websitesExceeded: stats.websiteCount > FREE_LIMITS.maxWebsites,
    subpagesExceeded: stats.totalSubpages > (FREE_LIMITS.maxWebsites * FREE_LIMITS.maxSubpagesPerWebsite),
    rulesExceeded: stats.totalRules > (FREE_LIMITS.maxWebsites * FREE_LIMITS.maxSubpagesPerWebsite * FREE_LIMITS.maxRulesPerSubpage)
  };
}

/**
 * 获取升级提示消息
 * @param {string} language 语言代码
 * @returns {Object} 提示消息对象
 */
function getUpgradeMessages(language = 'zh') {
  const messages = {
    zh: {
      quickFillLimited: '此预设内容已被限制，请升级为订阅用户以使用完整功能',
      websiteParseLimited: '此网站解析功能已被限制，请升级为订阅用户',
      upgradePrompt: '升级为高级版用户，解锁所有功能',
      upgradeButton: '立即升级'
    },
    en: {
      quickFillLimited: 'This preset content is limited, please upgrade to subscription user for full functionality',
      websiteParseLimited: 'This website parsing feature is limited, please upgrade to subscription user',
      upgradePrompt: 'Upgrade to Premium to unlock all features',
      upgradeButton: 'Upgrade Now'
    }
  };

  return messages[language] || messages.zh;
}

// 导出函数供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    checkSubscriptionStatus,
    getUserDataStats,
    filterQuickFillData,
    filterWebsiteRules,
    checkFreeLimits,
    getUpgradeMessages,
    FREE_LIMITS
  };
} else {
  // 浏览器环境，将函数添加到全局对象
  window.SubscriptionLimits = {
    checkSubscriptionStatus,
    getUserDataStats,
    filterQuickFillData,
    filterWebsiteRules,
    checkFreeLimits,
    getUpgradeMessages,
    FREE_LIMITS
  };
}

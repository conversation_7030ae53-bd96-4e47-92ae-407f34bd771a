<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle Sandbox环境测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .log { max-height: 400px; overflow-y: auto; }
        .config-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Paddle Sandbox环境诊断</h1>
        
        <div class="section warning">
            <h2>⚠️ Sandbox环境特殊说明</h2>
            <p>Paddle测试环境可能有以下特殊要求：</p>
            <ul>
                <li><strong>环境标识：</strong> 确保使用sandbox环境的正确配置</li>
                <li><strong>产品状态：</strong> 测试产品必须是Active状态</li>
                <li><strong>账户验证：</strong> 即使是测试环境也可能需要基本验证</li>
                <li><strong>API端点：</strong> 确保使用正确的sandbox API端点</li>
            </ul>
        </div>

        <div class="config-grid">
            <div class="section info">
                <h3>📋 当前配置</h3>
                <pre id="current-config"></pre>
            </div>
            <div class="section info">
                <h3>🌐 环境信息</h3>
                <pre id="env-info"></pre>
            </div>
        </div>

        <div class="section">
            <h2>🔧 Sandbox专项测试</h2>
            <button onclick="testSandboxAPI()">1. 测试Sandbox API连接</button>
            <button onclick="testProductInSandbox()">2. 验证测试产品</button>
            <button onclick="testSandboxCheckout()">3. 测试Sandbox Checkout</button>
            <button onclick="testEnvironmentSwitch()">4. 测试环境切换</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 诊断日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section info">
            <h2>💡 Sandbox环境解决方案</h2>
            <div id="sandbox-solutions"></div>
        </div>
    </div>

    <script>
        const config = {
            // 使用您更新的产品ID
            token: 'test_459728092d35d36bc173c60e52d',
            productId: 'pri_01k0xttm1f9wekbg957y66xawj',
            environment: 'sandbox',
            apiBase: 'https://sandbox-api.paddle.com',
            cdnUrl: 'https://cdn.paddle.com/paddle/v2/paddle.js'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function addSolution(solution) {
            const solutionsDiv = document.getElementById('sandbox-solutions');
            const solutionDiv = document.createElement('div');
            solutionDiv.style.cssText = 'margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007cba;';
            solutionDiv.innerHTML = solution;
            solutionsDiv.appendChild(solutionDiv);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('sandbox-solutions').innerHTML = '';
        }

        function showConfig() {
            document.getElementById('current-config').textContent = JSON.stringify(config, null, 2);
            
            const envInfo = {
                domain: window.location.hostname,
                port: window.location.port,
                protocol: window.location.protocol,
                userAgent: navigator.userAgent.substring(0, 50) + '...',
                timestamp: new Date().toISOString()
            };
            document.getElementById('env-info').textContent = JSON.stringify(envInfo, null, 2);
        }

        // 1. 测试Sandbox API连接
        async function testSandboxAPI() {
            log('🌐 测试1: Sandbox API连接...', 'info');
            
            try {
                // 测试基本API连接
                const response = await fetch(`${config.apiBase}/products`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${config.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log(`Sandbox API响应: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ API连接成功，找到 ${data.data?.length || 0} 个产品`, 'success');
                    
                    if (data.data && data.data.length > 0) {
                        log('产品列表:', 'info');
                        data.data.forEach(product => {
                            log(`- ${product.name} (${product.id}) - 状态: ${product.status}`, 'info');
                        });
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ API连接失败: ${errorText}`, 'error');
                    
                    if (response.status === 401) {
                        addSolution('🔧 <strong>Token问题:</strong> 请检查token是否正确，或尝试重新生成');
                    }
                }
            } catch (error) {
                log(`❌ API请求异常: ${error.message}`, 'error');
                addSolution('🔧 <strong>网络问题:</strong> 请检查网络连接或防火墙设置');
            }
        }

        // 2. 验证测试产品
        async function testProductInSandbox() {
            log('🛍️ 测试2: 验证测试产品...', 'info');
            
            try {
                const response = await fetch(`${config.apiBase}/prices/${config.productId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${config.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log(`产品验证响应: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ 产品验证成功', 'success');
                    log(`产品详情: ${JSON.stringify(data.data, null, 2)}`, 'info');
                    
                    if (data.data.status !== 'active') {
                        log('⚠️ 产品状态不是active', 'warning');
                        addSolution('🔧 <strong>产品状态:</strong> 请在Paddle Dashboard中激活此产品');
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ 产品验证失败: ${errorText}`, 'error');
                    
                    if (response.status === 404) {
                        addSolution('🔧 <strong>产品不存在:</strong> 请检查产品ID是否正确');
                    }
                }
            } catch (error) {
                log(`❌ 产品验证异常: ${error.message}`, 'error');
            }
        }

        // 3. 测试Sandbox Checkout
        async function testSandboxCheckout() {
            log('💳 测试3: Sandbox Checkout...', 'info');
            
            if (!window.Paddle) {
                log('📦 加载Paddle SDK...', 'info');
                await loadPaddleSDK();
            }

            try {
                log('🚀 初始化Paddle (Sandbox模式)...', 'info');
                
                window.Paddle.Initialize({
                    token: config.token,
                    environment: 'sandbox', // 明确指定sandbox环境
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            if (data.detail && data.detail.includes('Failed to retrieve JWT')) {
                                log('❌ JWT获取失败 - 这是Sandbox环境的已知问题', 'error');
                                addSolution('🔧 <strong>Sandbox JWT问题:</strong> 这可能是Paddle测试环境的限制，建议联系Paddle支持或尝试生产环境token');
                            }
                        } else if (data.type === 'checkout.loaded') {
                            log('✅ Checkout加载成功', 'success');
                        }
                    }
                });

                log('✅ Paddle初始化完成', 'success');
                
                setTimeout(() => {
                    log('💳 尝试打开Sandbox支付页面...', 'info');
                    
                    window.Paddle.Checkout.open({
                        items: [{
                            priceId: config.productId,
                            quantity: 1
                        }],
                        customer: {
                            email: '<EMAIL>'
                        },
                        settings: {
                            displayMode: 'overlay',
                            theme: 'light'
                        }
                    });
                }, 1000);
                
            } catch (error) {
                log(`❌ Sandbox Checkout失败: ${error.message}`, 'error');
            }
        }

        // 4. 测试环境切换
        function testEnvironmentSwitch() {
            log('🔄 测试4: 环境配置检查...', 'info');
            
            // 检查当前环境配置
            log(`当前环境: ${config.environment}`, 'info');
            log(`API端点: ${config.apiBase}`, 'info');
            log(`Token前缀: ${config.token.substring(0, 5)}...`, 'info');
            
            if (config.token.startsWith('test_')) {
                log('✅ 使用测试环境token', 'success');
            } else {
                log('⚠️ Token可能不是测试环境token', 'warning');
                addSolution('🔧 <strong>Token环境:</strong> 请确保使用test_开头的sandbox token');
            }
            
            if (config.apiBase.includes('sandbox')) {
                log('✅ 使用sandbox API端点', 'success');
            } else {
                log('⚠️ API端点可能不正确', 'warning');
            }
        }

        function loadPaddleSDK() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = config.cdnUrl;
                script.onload = () => {
                    log('✅ Paddle SDK加载成功', 'success');
                    resolve();
                };
                script.onerror = () => {
                    log('❌ Paddle SDK加载失败', 'error');
                    reject(new Error('SDK加载失败'));
                };
                document.head.appendChild(script);
            });
        }

        // 页面加载时初始化
        window.onload = function() {
            showConfig();
            log('🧪 Paddle Sandbox环境诊断工具已加载', 'info');
            log('💡 请按顺序运行测试以诊断Sandbox环境问题', 'info');
        };
    </script>
</body>
</html>

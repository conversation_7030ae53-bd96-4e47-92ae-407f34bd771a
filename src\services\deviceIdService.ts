/**
 * 设备ID服务
 * 集成设备ID到登录流程中
 */

import { generateDeviceId } from '../utils/testDeviceFingerprint';

/**
 * 设备ID服务类
 */
class DeviceIdService {
  private deviceId: string | null = null;
  private isGenerating: boolean = false;
  private generatePromise: Promise<string> | null = null;

  /**
   * 获取设备ID（单例模式，确保整个应用使用同一个ID）
   */
  async getDeviceId(): Promise<string> {
    // 如果已经有设备ID，直接返回
    if (this.deviceId) {
      return this.deviceId;
    }

    // 如果正在生成中，等待生成完成
    if (this.isGenerating && this.generatePromise) {
      return await this.generatePromise;
    }

    // 开始生成设备ID
    this.isGenerating = true;
    this.generatePromise = this.generateDeviceIdInternal();

    try {
      this.deviceId = await this.generatePromise;
      return this.deviceId;
    } finally {
      this.isGenerating = false;
      this.generatePromise = null;
    }
  }

  /**
   * 内部设备ID生成方法
   */
  private async generateDeviceIdInternal(): Promise<string> {
    try {
      console.log('🔍 开始生成设备ID...');
      const deviceId = await generateDeviceId();
      console.log('✅ 设备ID生成成功:', deviceId);
      return deviceId;
    } catch (error) {
      console.error('❌ 设备ID生成失败:', error);
      throw error;
    }
  }

  /**
   * 重置设备ID（用于测试或特殊情况）
   */
  reset(): void {
    this.deviceId = null;
    this.isGenerating = false;
    this.generatePromise = null;
    console.log('🔄 设备ID服务已重置');
  }

  /**
   * 获取当前缓存的设备ID（不触发生成）
   */
  getCachedDeviceId(): string | null {
    return this.deviceId;
  }
}

// 创建单例实例
const deviceIdService = new DeviceIdService();

/**
 * 获取设备ID的便捷函数
 */
export async function getDeviceId(): Promise<string> {
  return await deviceIdService.getDeviceId();
}

/**
 * 重置设备ID服务
 */
export function resetDeviceIdService(): void {
  deviceIdService.reset();
}

/**
 * 获取缓存的设备ID
 */
export function getCachedDeviceId(): string | null {
  return deviceIdService.getCachedDeviceId();
}

/**
 * 预加载设备ID（在应用启动时调用）
 */
export async function preloadDeviceId(): Promise<void> {
  try {
    console.log('🚀 预加载设备ID...');
    await deviceIdService.getDeviceId();
    console.log('✅ 设备ID预加载完成');
  } catch (error) {
    console.warn('⚠️ 设备ID预加载失败:', error);
  }
}

export default deviceIdService;

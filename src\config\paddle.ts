/**
 * 简化的 Paddle 配置
 * 只包含必要的配置项，去掉复杂的验证和日志
 */

// 从环境变量获取配置
export const PADDLE_CLIENT_TOKEN = import.meta.env.VITE_PADDLE_CLIENT_TOKEN || '';
export const PADDLE_ENVIRONMENT = import.meta.env.VITE_PADDLE_ENVIRONMENT || 'sandbox';
export const PADDLE_PRODUCT_ID = import.meta.env.VITE_PADDLE_PRODUCT_YEARLY || '';

// 价格信息（仅用于显示）
export const PRODUCT_PRICE = {
  amount: 9.99,
  currency: 'USD',
  period: 'year'
};

// 获取当前域名
const getBaseUrl = () => {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  return 'https://autofill.utilityapp.site';
};

// 成功和取消页面URL (取消页面URL可能仍需用于某些场景)
export const PADDLE_CANCEL_URL = `${getBaseUrl()}/pricing?subscription_cancelled=true`;
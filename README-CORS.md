# CORS问题解决方案

本文档说明如何解决应用中的CORS（跨域资源共享）问题。

## 问题描述

在开发过程中，我们遇到了以下CORS错误：

1. `x-api-key` 请求头不被允许
2. `No 'Access-Control-Allow-Origin' header is present on the requested resource`

这些错误会导致API请求失败，使应用无法正常工作。

## 解决方案

我们实现了两种解决方案：

### 1. Vite开发服务器代理（推荐）

在开发环境中，我们使用Vite的代理功能解决CORS问题。这是当前默认配置。

**工作原理**：
- 开发环境中的API请求会发送到本地代理（`/api`）
- Vite代理服务器将请求转发到实际的API服务器
- 由于请求来自同一个域，不会触发CORS限制

**配置文件**：
- `vite.config.ts` - 包含代理配置
- `src/config/api.ts` - 根据环境选择API URL

### 2. CORS代理服务（备选方案）

如果Vite代理不可用，我们也实现了CORS代理服务。

**工作原理**：
- 使用第三方CORS代理服务转发请求
- 代理服务添加必要的CORS头
- 支持多个备选代理服务

**相关文件**：
- `src/utils/corsProxy.ts` - CORS代理实现
- `src/services/apiInterceptor.ts` - API请求拦截器

## 如何配置

### 开发环境

默认情况下，开发环境使用Vite代理。无需额外配置。

如果需要使用CORS代理而非Vite代理，可以在`.env.local`文件中添加：

```
VITE_FORCE_CORS_PROXY=true
```

### 生产环境

生产环境默认直接访问API，不使用代理。如果生产环境也遇到CORS问题，可以：

1. 配置API服务器允许跨域请求（推荐）
2. 在前端服务器配置反向代理（如Nginx、Cloudflare Workers等）
3. 在`.env`文件中设置`VITE_FORCE_CORS_PROXY=true`启用CORS代理（不推荐用于生产）

## 调试

如果遇到API请求问题，可以：

1. 访问`/api-test`页面测试API连接
2. 检查浏览器开发者工具中的网络请求和控制台错误
3. 查看应用日志中的API请求详情

## 注意事项

- CORS代理可能会降低性能，增加请求延迟
- 某些CORS代理服务可能有使用限制或不稳定
- 生产环境应尽量避免使用第三方CORS代理
- 确保API密钥等敏感信息不会通过代理泄露 
import React, { useState, useEffect } from 'react';
import { getDeviceId, resetDeviceIdService, getCachedDeviceId } from '../services/deviceIdService';
import { 
  collectDeviceFeatures, 
  testDeviceIdConsistency, 
  clearDeviceIdCache, 
  getDeviceIdDebugInfo 
} from '../utils/testDeviceFingerprint';

const DeviceIdTestPage: React.FC = () => {
  const [deviceId, setDeviceId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [features, setFeatures] = useState<any>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  // 生成设备ID
  const handleGenerateId = async () => {
    setIsLoading(true);
    try {
      const id = await getDeviceId();
      setDeviceId(id);
      console.log('设备ID生成成功:', id);
    } catch (error) {
      console.error('设备ID生成失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 显示设备特征
  const handleShowFeatures = () => {
    const deviceFeatures = collectDeviceFeatures();
    setFeatures(deviceFeatures);
    console.log('设备特征:', deviceFeatures);
  };

  // 显示调试信息
  const handleShowDebugInfo = () => {
    const info = getDeviceIdDebugInfo();
    setDebugInfo(info);
    console.log('调试信息:', info);
  };

  // 测试一致性
  const handleTestConsistency = async () => {
    setIsLoading(true);
    const results: string[] = [];
    
    try {
      // 重定向console.log到结果数组
      const originalLog = console.log;
      console.log = (...args) => {
        results.push(args.join(' '));
        originalLog(...args);
      };

      await testDeviceIdConsistency();
      
      // 恢复console.log
      console.log = originalLog;
      
      setTestResults(results);
    } catch (error) {
      console.error('一致性测试失败:', error);
      results.push(`❌ 测试失败: ${error}`);
      setTestResults(results);
    } finally {
      setIsLoading(false);
    }
  };

  // 清除缓存
  const handleClearCache = () => {
    clearDeviceIdCache();
    resetDeviceIdService();
    setDeviceId(null);
    setFeatures(null);
    setDebugInfo(null);
    setTestResults([]);
    console.log('缓存已清除');
  };

  // 页面加载时获取缓存的设备ID
  useEffect(() => {
    const cachedId = getCachedDeviceId();
    if (cachedId) {
      setDeviceId(cachedId);
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">🔍 设备ID测试工具</h1>
          
          {/* 说明部分 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">📋 功能说明</h2>
            <ul className="text-blue-800 space-y-1">
              <li>• 生成确定性设备ID，清除存储后保持一致</li>
              <li>• 避免浏览器更新导致的ID变化</li>
              <li>• 每个设备ID限制最多3个账号登录</li>
              <li>• 使用隐私友好的设备特征</li>
            </ul>
          </div>

          {/* 操作按钮 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <button
              onClick={handleGenerateId}
              disabled={isLoading}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? '生成中...' : '生成设备ID'}
            </button>
            
            <button
              onClick={handleShowFeatures}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
            >
              显示特征
            </button>
            
            <button
              onClick={handleTestConsistency}
              disabled={isLoading}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50"
            >
              测试一致性
            </button>
            
            <button
              onClick={handleClearCache}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
            >
              清除缓存
            </button>
          </div>

          <button
            onClick={handleShowDebugInfo}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 mb-6"
          >
            显示调试信息
          </button>

          {/* 当前设备ID */}
          {deviceId && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">🆔 当前设备ID</h3>
              <div className="font-mono text-sm bg-white p-3 rounded border break-all">
                {deviceId}
              </div>
            </div>
          )}

          {/* 设备特征 */}
          {features && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">📊 设备特征</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-300">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border border-gray-300 px-4 py-2 text-left">特征</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">值</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Object.entries(features).map(([key, value]) => (
                      <tr key={key}>
                        <td className="border border-gray-300 px-4 py-2 font-medium">{key}</td>
                        <td className="border border-gray-300 px-4 py-2 font-mono text-sm">{String(value)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* 调试信息 */}
          {debugInfo && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">🔍 调试信息</h3>
              <pre className="bg-white p-3 rounded border text-sm overflow-x-auto">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </div>
          )}

          {/* 测试结果 */}
          {testResults.length > 0 && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">🧪 测试结果</h3>
              <div className="bg-white p-3 rounded border">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono mb-1">
                    {result}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 使用说明 */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-yellow-900 mb-2">⚠️ 使用说明</h3>
            <ul className="text-yellow-800 space-y-1 text-sm">
              <li>• 设备ID基于浏览器和硬件特征生成，不涉及个人隐私</li>
              <li>• 正常情况下，同一设备的ID保持稳定</li>
              <li>• 硬件升级、系统重装可能导致ID变化</li>
              <li>• 清除localStorage后重新生成的ID应该保持一致</li>
              <li>• 在登录时会自动发送设备ID到服务器</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceIdTestPage;

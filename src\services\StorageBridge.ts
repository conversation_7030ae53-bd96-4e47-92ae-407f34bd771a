/**
 * 存储桥接服务
 * 用于处理浏览器扩展和网页之间的存储同步
 */

// 防止循环引用
import { getStorageItem, setStorageItem, hasExtensionStorage } from './storageAdapter';
import { extensionMonitor } from '../utils/ExtensionMonitor';

// 全局错误处理计数器，防止无限循环
let errorCount = 0;
const MAX_ERROR_COUNT = 10;
const ERROR_RESET_INTERVAL = 5000; // 5秒

// 定义支持的操作类型
type StorageOperation = 'get' | 'set' | 'remove' | 'clear' | 'sync' | 'listen' | 'storage_changed' | 'storage_changed_result';

/**
 * StorageBridge类，用于处理浏览器扩展和网页之间的存储同步
 */
class StorageBridge {
  private static instance: StorageBridge;
  private isInitialized = false;
  private isProcessing = false;
  private extensionAvailable = false;
  private supportedOperations: Set<StorageOperation> = new Set(['get', 'set', 'remove', 'clear', 'sync', 'listen', 'storage_changed', 'storage_changed_result']);
  private cspCompatible = false;
  private currentExtensionId: string | null = null;

  /**
   * 获取单例实例
   */
  public static getInstance(): StorageBridge {
    if (!StorageBridge.instance) {
      StorageBridge.instance = new StorageBridge();
    }
    return StorageBridge.instance;
  }

  /**
   * 私有构造函数，防止外部直接实例化
   */
  private constructor() {
    this.init();
  }

  /**
   * 检查当前是否有插件可用
   */
  private hasExtensionAvailable(): boolean {
    return this.extensionAvailable && this.currentExtensionId !== null;
  }

  /**
   * 检查CSP兼容性
   */
  private checkCspCompatibility(): boolean {
    try {
      // 检查是否有CSP meta标签
      const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
      if (cspMeta) {
        const cspValue = cspMeta.getAttribute('content') || '';
        // 检查是否允许nonce-website-storage-bridge
        return cspValue.includes('nonce-website-storage-bridge');
      }
      // 如果没有CSP meta标签，假设没有CSP限制
      return true;
    } catch (error) {
      console.error('[StorageBridge] 检查CSP兼容性时出错:', error);
      return false;
    }
  }

  /**
   * 初始化桥接服务
   */
  private init(): void {
    if (this.isInitialized) return;

    try {
      // 检查CSP兼容性
      this.cspCompatible = this.checkCspCompatibility();

      // 检查扩展是否可用
      this.extensionAvailable = hasExtensionStorage();

      // 监听扩展安装事件（允许所有插件）
      window.addEventListener('extension-installed', (event: any) => {
        console.log('[StorageBridge] 检测到浏览器扩展已安装');
        this.extensionAvailable = true;

        // 获取插件ID（仅用于记录，不进行验证）
        if (event.detail && event.detail.extensionId) {
          this.currentExtensionId = event.detail.extensionId;

          // 记录插件检测（所有插件都允许访问）
          extensionMonitor.logValidation(
            event.detail.extensionId,
            true,
            '插件已检测到，允许访问缓存数据'
          );

          console.log('[StorageBridge] 插件已连接，允许访问缓存数据');
        }
      });

      // 定期重置错误计数
      setInterval(() => {
        if (errorCount > 0) {
          errorCount = 0;
        }
      }, ERROR_RESET_INTERVAL);

      this.isInitialized = true;
    } catch (error) {
      console.error('[StorageBridge] 初始化错误:', error);
    }
  }

  /**
   * 检查操作是否支持
   * @param operation 操作名称
   */
  private isOperationSupported(operation: string): boolean {
    return this.supportedOperations.has(operation as StorageOperation);
  }

  /**
   * 安全地创建和分发自定义事件
   * @param eventName 事件名称
   * @param detail 事件详情
   */
  private safeDispatchEvent(eventName: string, detail: any): void {
    try {
      // 检查是否有window.dispatchEvent
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        // 创建自定义事件
        const event = new CustomEvent(eventName, { detail });
        // 分发事件
        window.dispatchEvent(event);
      } else {
        console.warn('[StorageBridge] 无法分发事件，window.dispatchEvent不可用');
      }
    } catch (error) {
      console.error(`[StorageBridge] 分发事件 ${eventName} 时出错:`, error);
      this.handleError(error as Error);
    }
  }

  /**
   * 执行存储操作（带插件ID验证）
   * @param operation 操作名称
   * @param callback 回调函数
   */
  public async executeOperation<T>(operation: string, callback: () => Promise<T>): Promise<T> {
    // 检查操作是否支持
    if (!this.isOperationSupported(operation)) {
      console.error(`[StorageBridge] 未知的存储操作: ${operation}`);
      throw new Error('未知的存储操作');
    }

    // 检查是否有插件可用（不验证ID，允许所有插件）
    if (!this.hasExtensionAvailable()) {
      // 如果没有插件，直接返回null（静默处理）
      return Promise.resolve(null as T);
    }

    // 记录存储访问（仅在开发环境）
    if (import.meta.env.DEV) {
      extensionMonitor.logStorageAccess(
        operation,
        { extensionId: this.currentExtensionId },
        true,
        `存储操作 ${operation} 已允许`
      );
    }

    // 防止循环调用和错误堆积
    if (this.isProcessing) {
      throw new Error('存储操作正在进行中');
    }

    if (errorCount >= MAX_ERROR_COUNT) {
      console.warn('[StorageBridge] 错误次数过多，暂停操作');
      throw new Error('错误次数过多，暂停操作');
    }

    this.isProcessing = true;

    try {
      const result = await callback();
      this.isProcessing = false;
      return result;
    } catch (error) {
      this.isProcessing = false;
      errorCount++;
      console.error(`[StorageBridge] 存储操作错误: ${error}`);
      throw error;
    }
  }

  /**
   * 同步数据到扩展
   * @param key 键名
   * @param value 值
   */
  public async syncToExtension(key: string, value: any): Promise<void> {
    return this.executeOperation('sync', async () => {
      if (!this.extensionAvailable) return;

      try {
        // 使用安全的事件分发
        this.safeDispatchEvent('sync-to-extension', { key, value });
      } catch (error) {
        console.error('[StorageBridge] 同步到扩展失败:', error);
      }
    });
  }

  /**
   * 从扩展同步数据
   * @param key 键名
   */
  public async syncFromExtension(key: string): Promise<any> {
    return this.executeOperation('sync', async () => {
      if (!this.extensionAvailable) return null;

      try {
        // 这里可以添加从扩展获取数据的代码
        return await getStorageItem(key);
      } catch (error) {
        console.error('[StorageBridge] 从扩展同步失败:', error);
        return null;
      }
    });
  }
  
  /**
   * 处理未捕获的错误
   * @param error 错误对象
   */
  public handleError(error: Error): void {
    console.error('[StorageBridge] 捕获到未处理错误:', error);
    errorCount++;
    
    // 如果有全局错误处理函数，也调用它
    if (typeof window !== 'undefined' && 'handleStorageError' in window) {
      try {
        (window as any).handleStorageError(error);
      } catch (e) {
        console.error('[StorageBridge] 调用全局错误处理函数失败:', e);
      }
    }
  }
}

// 导出单例实例
export default StorageBridge.getInstance(); 
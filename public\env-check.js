/**
 * 环境变量检查脚本
 * 在页面加载时检查环境变量问题
 */

(function() {
  // 在页面加载完成后执行
  window.addEventListener('DOMContentLoaded', function() {
    console.log('===== 环境变量检查脚本 =====');
    
    // 检查环境变量是否正确加载
    setTimeout(function() {
      try {
        console.log('注意: 此脚本无法直接访问Vite环境变量。');
        console.log('如果您看到API请求失败，请确认:');
        console.log('1. 您已在项目根目录创建了 .env.local 文件');
        console.log('2. 文件中包含正确的 VITE_API_URL 和 VITE_API_KEY 值');
        console.log('3. 您已重启开发服务器以加载新的环境变量');
        console.log('4. 后端服务器已正确配置CORS，允许来自 http://localhost:5173 的请求');
        
        // 检查根元素
        const rootElement = document.getElementById('root');
        console.log('Root元素:', rootElement ? '存在' : '不存在');
        
        if (rootElement) {
          console.log('Root元素子节点数量:', rootElement.childNodes.length);
        }
      } catch (error) {
        console.error('环境变量检查失败:', error);
      }
    }, 1000);
  });
  
  // 捕获全局错误
  window.addEventListener('error', function(event) {
    console.error('全局错误:', event.error);
  });
  
  // 捕获未处理的Promise错误
  window.addEventListener('unhandledrejection', function(event) {
    console.error('未处理的Promise错误:', event.reason);
  });
})(); 
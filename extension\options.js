/**
 * 扩展设置页面脚本
 */

// 语言配置
const translations = {
  zh: {
    // 页面标题
    title: "自动填充助手 - 设置",
    // 设置分类
    generalSettings: "常规设置",
    subscriptionStatus: "订阅状态",
    accountInfo: "账号信息",
    // 设置项
    enableAutoFill: "启用自动填充",
    notificationSetting: "通知设置",
    // 订阅相关
    noActiveSubscription: "暂无有效订阅",
    subscribePlan: "订阅套餐",
    activeSubscription: "当前订阅有效",
    expiresOn: "到期时间",
    subscriptionPaused: "已暂停",
    subscriptionCancelled: "已取消",
    subscriptionPastDue: "付款逾期",
    subscriptionUnknown: "状态未知",
    // 账号信息
    emailLabel: "邮箱",
    userIdLabel: "用户ID",
    logoutButton: "退出登录",
    // 通知选项
    notificationOptions: {
      all: "所有通知",
      important: "仅重要通知",
      none: "关闭通知"
    }
  },
  en: {
    // 页面标题
    title: "AutoFill Assistant - Settings",
    // 设置分类
    generalSettings: "General Settings",
    subscriptionStatus: "Subscription Status",
    accountInfo: "Account Information",
    // 设置项
    enableAutoFill: "Enable AutoFill",
    notificationSetting: "Notification Settings",
    // 订阅相关
    noActiveSubscription: "No Active Subscription",
    subscribePlan: "Subscribe Plan",
    activeSubscription: "Subscription Active",
    expiresOn: "Expires on",
    subscriptionPaused: "Paused",
    subscriptionCancelled: "Cancelled",
    subscriptionPastDue: "Payment Past Due",
    subscriptionUnknown: "Status Unknown",
    // 账号信息
    emailLabel: "Email",
    userIdLabel: "User ID",
    logoutButton: "Logout",
    // 通知选项
    notificationOptions: {
      all: "All Notifications",
      important: "Important Only",
      none: "No Notifications"
    }
  }
};

// 当前语言
let currentLanguage = 'zh';

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  // 获取语言设置
  chrome.storage.local.get('language', function(result) {
    if (result.language) {
      currentLanguage = result.language;
    }
    
    // 设置语言选择器的当前值
    const languageSelector = document.getElementById('language-selector');
    languageSelector.value = currentLanguage;
    
    // 应用翻译
    applyTranslation();
    
    // 加载用户信息和订阅状态
    loadUserInfo();
  });
  
  // 监听语言切换
  document.getElementById('language-selector').addEventListener('change', function(e) {
    currentLanguage = e.target.value;
    
    // 保存语言设置
    chrome.storage.local.set({language: currentLanguage}, function() {
      console.log('语言设置已保存:', currentLanguage);
    });
    
    // 应用翻译
    applyTranslation();
  });
  
  // 监听订阅按钮点击
  document.getElementById('subscribe-button').addEventListener('click', function() {
    // 打开订阅页面
    chrome.tabs.create({url: 'https://autofill.utilityapp.site/pricing'});
  });
  
  // 监听退出登录按钮
  document.getElementById('logout-button').addEventListener('click', function() {
    logoutUser();
  });
  
  // 自动填充开关
  document.getElementById('auto-fill-toggle').addEventListener('change', function(e) {
    chrome.storage.local.set({autoFillEnabled: e.target.checked}, function() {
      console.log('自动填充设置已更新:', e.target.checked);
    });
  });
  
  // 通知设置变更
  document.getElementById('notification-setting').addEventListener('change', function(e) {
    chrome.storage.local.set({notificationSetting: e.target.value}, function() {
      console.log('通知设置已更新:', e.target.value);
    });
  });
  
  // 加载设置
  loadSettings();
});

// 应用翻译
function applyTranslation() {
  const lang = translations[currentLanguage];
  
  // 更新页面标题
  document.getElementById('title').textContent = lang.title;
  document.title = lang.title;
  
  // 更新设置分类标题
  document.getElementById('generalSettings').textContent = lang.generalSettings;
  document.getElementById('subscriptionStatus').textContent = lang.subscriptionStatus;
  document.getElementById('accountInfo').textContent = lang.accountInfo;
  
  // 更新设置项
  document.getElementById('enableAutoFill').textContent = lang.enableAutoFill;
  document.getElementById('notificationSetting').textContent = lang.notificationSetting;
  
  // 更新订阅相关
  document.getElementById('noActiveSubscription').textContent = lang.noActiveSubscription;
  document.getElementById('subscribePlan').textContent = lang.subscribePlan;
  
  // 更新账号信息
  document.getElementById('emailLabel').textContent = lang.emailLabel;
  document.getElementById('userIdLabel').textContent = lang.userIdLabel;
  document.getElementById('logout-button').textContent = lang.logoutButton;
  
  // 更新通知选项
  const notificationSelect = document.getElementById('notification-setting');
  for (let i = 0; i < notificationSelect.options.length; i++) {
    const option = notificationSelect.options[i];
    option.textContent = lang.notificationOptions[option.value];
  }
}

// 加载用户信息和订阅状态
function loadUserInfo() {
  chrome.storage.local.get([
    'user_id',
    'user_email',
    'subscription_info'
  ], function(result) {
    // 更新用户ID和邮箱
    document.getElementById('user-id-value').textContent = result.user_id || '-';
    document.getElementById('email-value').textContent = result.user_email || '-';
    
    // 更新订阅状态
    const subscriptionContainer = document.getElementById('subscription-container');
    const noSubscriptionDiv = document.getElementById('no-subscription');
    
    // 尝试解析订阅信息
    let subscriptionInfo = null;
    try {
      if (result.subscription_info && 
          result.subscription_info !== "0" && 
          result.subscription_info !== "undefined" && 
          result.subscription_info !== "null") {
        subscriptionInfo = JSON.parse(result.subscription_info);
      }
    } catch (e) {
      console.warn('解析订阅信息失败:', e);
    }
    
    if (subscriptionInfo && subscriptionInfo.status) {
      // 有订阅信息
      let statusClass = 'subscription-inactive';
      let statusText = '';
      
      // 根据订阅状态设置不同的样式和文本
      switch(subscriptionInfo.status) {
        case 'active':
          statusClass = 'subscription-active';
          statusText = translations[currentLanguage].activeSubscription;
          break;
        case 'paused':
          statusClass = 'subscription-warning';
          statusText = `${translations[currentLanguage].subscriptionStatus}: ${translations[currentLanguage].subscriptionPaused}`;
          break;
        case 'cancelled':
          statusClass = 'subscription-inactive';
          statusText = `${translations[currentLanguage].subscriptionStatus}: ${translations[currentLanguage].subscriptionCancelled}`;
          break;
        case 'past_due':
          statusClass = 'subscription-warning';
          statusText = `${translations[currentLanguage].subscriptionStatus}: ${translations[currentLanguage].subscriptionPastDue}`;
          break;
        default:
          statusClass = 'subscription-inactive';
          statusText = `${translations[currentLanguage].subscriptionStatus}: ${translations[currentLanguage].subscriptionUnknown}`;
      }
      
      noSubscriptionDiv.className = `subscription-status ${statusClass}`;
      noSubscriptionDiv.innerHTML = `
        <span>${statusText}</span>
        ${subscriptionInfo.next_bill_date ? 
          `<div>${translations[currentLanguage].expiresOn}: ${new Date(subscriptionInfo.next_bill_date).toLocaleDateString()}</div>` : 
          ''}
      `;
      
      // 如果订阅不是活跃状态，也显示订阅按钮
      document.getElementById('subscribe-button').style.display = 
        subscriptionInfo.status === 'active' ? 'none' : 'block';
    } else {
      // 无有效订阅
      noSubscriptionDiv.className = 'subscription-status subscription-inactive';
      noSubscriptionDiv.innerHTML = `<span id="noActiveSubscription">${translations[currentLanguage].noActiveSubscription}</span>`;
      
      // 显示订阅按钮
      document.getElementById('subscribe-button').style.display = 'block';
    }
  });
}

// 加载设置
function loadSettings() {
  chrome.storage.local.get([
    'autoFillEnabled',
    'notificationSetting',
    'language'
  ], function(result) {
    // 自动填充开关
    document.getElementById('auto-fill-toggle').checked = 
      result.autoFillEnabled === undefined ? true : result.autoFillEnabled;
    
    // 通知设置
    if (result.notificationSetting) {
      document.getElementById('notification-setting').value = result.notificationSetting;
    }
  });
}

// 退出登录
function logoutUser() {
  chrome.storage.local.remove([
    'access_token',
    'refresh_token',
    'token_type',
    'access_token_expires_at',
    'user_id',
    'user_email',
    'subscription_info'
  ], function() {
    console.log('用户已退出登录');
    
    // 重新加载用户信息（显示为未登录状态）
    loadUserInfo();
    
    // 向内容脚本通知登出
    chrome.tabs.query({}, function(tabs) {
      for (let tab of tabs) {
        chrome.tabs.sendMessage(tab.id, {action: 'syncFromExtension'}).catch(() => {
          // 忽略任何错误（如无法连接的标签页）
        });
      }
    });
  });
} 
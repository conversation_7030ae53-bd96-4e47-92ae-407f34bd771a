<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle 400错误修复</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .log { max-height: 400px; overflow-y: auto; }
        .config-test { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .param-test { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Paddle 400错误专项修复</h1>
        
        <div class="section success">
            <h2>✅ 已解决的问题</h2>
            <p>✅ CSP/域名白名单问题已解决 - 不再出现403错误</p>
        </div>

        <div class="section error">
            <h2>❌ 当前问题: 400错误</h2>
            <p><strong>错误位置:</strong> checkout-service.paddle.com/transaction-checkout</p>
            <p><strong>可能原因:</strong></p>
            <ul>
                <li>产品ID格式或值错误</li>
                <li>必需参数缺失</li>
                <li>产品状态不是Active</li>
                <li>价格配置问题</li>
                <li>客户信息格式错误</li>
            </ul>
        </div>

        <div class="section info">
            <h2>📋 当前配置</h2>
            <div class="config-test">
                <h3>🔧 产品配置</h3>
                <p><strong>Token:</strong> <code id="token-display"></code></p>
                <p><strong>产品ID:</strong> <code id="product-display"></code></p>
                <p><strong>环境:</strong> <code>sandbox</code></p>
            </div>
        </div>

        <div class="section">
            <h2>🧪 400错误诊断测试</h2>
            <button onclick="testMinimalConfig()">1. 测试最简配置</button>
            <button onclick="testWithoutCustomer()">2. 测试无客户信息</button>
            <button onclick="testDifferentProductId()">3. 测试备用产品ID</button>
            <button onclick="testStepByStep()">4. 逐步参数测试</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 诊断日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section warning" id="solution-section" style="display: none;">
            <h2>💡 发现的解决方案</h2>
            <div id="solution-content"></div>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'test_459728092d35d36bc173c60e52d',
            primaryProduct: 'pri_01k0xttmf9wekbg957y66xawj',  // $9.99 - 测试中工作的
            secondaryProduct: 'pri_01jxa2azwznm06dqbptzagtm0z', // $6.66 - 有JWT问题的
            testProducts: [
                // 可能的有效产品ID格式
                'pri_01k0xttmf9wekbg957y66xawj',
                'pri_01jxa2azwznm06dqbptzagtm0z'
            ]
        };

        let paddleInitialized = false;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('solution-section').style.display = 'none';
        }

        function showSolution(solution) {
            document.getElementById('solution-content').innerHTML = solution;
            document.getElementById('solution-section').style.display = 'block';
        }

        function showConfig() {
            document.getElementById('token-display').textContent = config.token.substring(0, 15) + '...';
            document.getElementById('product-display').textContent = config.primaryProduct;
        }

        async function initializePaddle() {
            if (paddleInitialized) return;

            return new Promise((resolve, reject) => {
                try {
                    window.Paddle.Initialize({
                        token: config.token,
                        eventCallback: (data) => {
                            log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                            
                            if (data.type === 'checkout.error') {
                                log(`❌ Checkout错误: ${data.detail}`, 'error');
                                
                                if (data.detail && data.detail.includes('400')) {
                                    log('🔍 400错误确认 - 请求参数问题', 'error');
                                } else if (data.detail && data.detail.includes('validation')) {
                                    log('🔍 验证错误 - 参数格式或值问题', 'error');
                                }
                            } else if (data.type === 'checkout.loaded') {
                                log('✅ 支付页面加载成功！', 'success');
                                showSolution('🎉 <strong>成功！</strong> 找到了有效的配置组合。');
                            }
                        }
                    });

                    paddleInitialized = true;
                    log('✅ Paddle初始化成功', 'success');
                    resolve();
                    
                } catch (error) {
                    log(`❌ Paddle初始化失败: ${error.message}`, 'error');
                    reject(error);
                }
            });
        }

        // 1. 测试最简配置
        async function testMinimalConfig() {
            log('🧪 测试1: 最简配置 (只有必需参数)...', 'info');
            
            try {
                await initializePaddle();
                
                // 只包含绝对必需的参数
                const minimalConfig = {
                    items: [{
                        priceId: config.primaryProduct,
                        quantity: 1
                    }]
                };
                
                log(`配置: ${JSON.stringify(minimalConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(minimalConfig);
                log('✅ 最简配置请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 最简配置测试失败: ${error.message}`, 'error');
            }
        }

        // 2. 测试无客户信息
        async function testWithoutCustomer() {
            log('🧪 测试2: 无客户信息配置...', 'info');
            
            try {
                await initializePaddle();
                
                const configWithoutCustomer = {
                    items: [{
                        priceId: config.primaryProduct,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: 'overlay',
                        theme: 'light'
                    }
                };
                
                log(`配置: ${JSON.stringify(configWithoutCustomer, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(configWithoutCustomer);
                log('✅ 无客户信息配置请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 无客户信息测试失败: ${error.message}`, 'error');
            }
        }

        // 3. 测试备用产品ID
        async function testDifferentProductId() {
            log('🧪 测试3: 备用产品ID...', 'info');
            
            try {
                await initializePaddle();
                
                // 测试第二个产品ID
                const configWithSecondary = {
                    items: [{
                        priceId: config.secondaryProduct,
                        quantity: 1
                    }]
                };
                
                log(`使用备用产品ID: ${config.secondaryProduct}`, 'info');
                log(`配置: ${JSON.stringify(configWithSecondary, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(configWithSecondary);
                log('✅ 备用产品ID请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 备用产品ID测试失败: ${error.message}`, 'error');
            }
        }

        // 4. 逐步参数测试
        async function testStepByStep() {
            log('🧪 测试4: 逐步添加参数...', 'info');
            
            try {
                await initializePaddle();
                
                // 测试序列
                const testConfigs = [
                    {
                        name: '仅产品ID',
                        config: {
                            items: [{ priceId: config.primaryProduct, quantity: 1 }]
                        }
                    },
                    {
                        name: '添加设置',
                        config: {
                            items: [{ priceId: config.primaryProduct, quantity: 1 }],
                            settings: { displayMode: 'overlay' }
                        }
                    },
                    {
                        name: '添加客户邮箱',
                        config: {
                            items: [{ priceId: config.primaryProduct, quantity: 1 }],
                            customer: { email: '<EMAIL>' },
                            settings: { displayMode: 'overlay' }
                        }
                    },
                    {
                        name: '添加自定义数据',
                        config: {
                            items: [{ priceId: config.primaryProduct, quantity: 1 }],
                            customer: { email: '<EMAIL>' },
                            customData: { test: 'data' },
                            settings: { displayMode: 'overlay', theme: 'light' }
                        }
                    }
                ];

                for (let i = 0; i < testConfigs.length; i++) {
                    const test = testConfigs[i];
                    
                    setTimeout(() => {
                        log(`🔄 步骤 ${i + 1}: ${test.name}`, 'info');
                        log(`配置: ${JSON.stringify(test.config, null, 2)}`, 'info');
                        
                        try {
                            window.Paddle.Checkout.open(test.config);
                            log(`✅ 步骤 ${i + 1} 请求已发送`, 'success');
                        } catch (error) {
                            log(`❌ 步骤 ${i + 1} 失败: ${error.message}`, 'error');
                        }
                    }, i * 3000); // 每3秒测试一个配置
                }
                
            } catch (error) {
                log(`❌ 逐步测试失败: ${error.message}`, 'error');
            }
        }

        // 监听网络错误
        window.addEventListener('error', function(e) {
            if (e.message && e.message.includes('400')) {
                log('🚨 检测到400错误事件', 'error');
            }
        });

        // 页面加载时显示配置
        window.onload = function() {
            showConfig();
            log('🔧 Paddle 400错误诊断工具已加载', 'info');
            log('💡 开始逐步测试以找出400错误的具体原因', 'info');
        };
    </script>
</body>
</html>

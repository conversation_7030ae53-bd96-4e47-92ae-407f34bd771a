/**
 * 网站解析功能
 * 用于解析网页表单并提供自动填充功能
 */

// 网站解析器类
class WebsiteParser {
  constructor() {
    this.isInitialized = false;
    this.parseButton = null;
    this.resultPanel = null;
  }

  // 初始化解析器
  init() {
    if (this.isInitialized) return;
    
    this.createParseButton();
    this.createResultPanel();
    this.isInitialized = true;
  }

  // 创建解析按钮
  createParseButton() {
    this.parseButton = document.createElement('div');
    this.parseButton.id = 'autofill-parse-button';
    this.parseButton.style.cssText = `
      position: fixed;
      top: 50%;
      right: 20px;
      transform: translateY(-50%);
      background: #4CAF50;
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      cursor: pointer;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 9999;
      user-select: none;
      transition: all 0.2s ease;
    `;
    
    this.parseButton.innerHTML = `
      <div style="display: flex; align-items: center; gap: 8px;">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
        <span>解析表单</span>
      </div>
    `;

    // 添加悬停效果
    this.parseButton.addEventListener('mouseenter', () => {
      this.parseButton.style.background = '#45a049';
      this.parseButton.style.transform = 'translateY(-50%) scale(1.05)';
    });

    this.parseButton.addEventListener('mouseleave', () => {
      this.parseButton.style.background = '#4CAF50';
      this.parseButton.style.transform = 'translateY(-50%) scale(1)';
    });

    // 添加点击事件
    this.parseButton.addEventListener('click', () => {
      this.handleParseClick();
    });

    document.body.appendChild(this.parseButton);
  }

  // 创建结果面板
  createResultPanel() {
    this.resultPanel = document.createElement('div');
    this.resultPanel.id = 'autofill-result-panel';
    this.resultPanel.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      width: 300px;
      max-height: 400px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.12);
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: none;
      overflow: hidden;
    `;

    document.body.appendChild(this.resultPanel);
  }

  // 处理解析按钮点击
  handleParseClick() {
    // 首先检查解析权限
    chrome.runtime.sendMessage({
      action: 'checkWebsiteParsing',
      language: navigator.language.startsWith('zh') ? 'zh' : 'en'
    }, (response) => {
      if (response && response.allowed) {
        // 有权限，执行解析
        this.performParsing();
      } else if (response && !response.allowed) {
        // 没有权限，已经在content-script中显示了限制提示
        console.log('网站解析功能被限制');
      } else {
        // 检查失败，显示错误
        this.showError('检查权限失败，请稍后重试');
      }
    });
  }

  // 执行实际的解析
  performParsing() {
    const forms = document.querySelectorAll('form');
    const inputs = document.querySelectorAll('input, textarea, select');
    
    const parseResults = {
      forms: forms.length,
      inputs: [],
      timestamp: new Date().toISOString()
    };

    // 解析输入字段
    inputs.forEach((input, index) => {
      const label = this.findLabelForInput(input);
      const inputInfo = {
        index: index,
        type: input.type || input.tagName.toLowerCase(),
        name: input.name || '',
        id: input.id || '',
        placeholder: input.placeholder || '',
        label: label,
        required: input.required || false,
        value: input.value || ''
      };
      
      parseResults.inputs.push(inputInfo);
    });

    this.showResults(parseResults);
  }

  // 查找输入字段的标签
  findLabelForInput(input) {
    // 通过for属性查找label
    if (input.id) {
      const label = document.querySelector(`label[for="${input.id}"]`);
      if (label) return label.textContent.trim();
    }

    // 查找父级label
    const parentLabel = input.closest('label');
    if (parentLabel) return parentLabel.textContent.trim();

    // 查找前面的文本节点或元素
    let prev = input.previousElementSibling;
    while (prev) {
      if (prev.textContent && prev.textContent.trim()) {
        return prev.textContent.trim();
      }
      prev = prev.previousElementSibling;
    }

    return '';
  }

  // 显示解析结果
  showResults(results) {
    this.resultPanel.innerHTML = `
      <div style="padding: 16px; border-bottom: 1px solid #eee;">
        <h3 style="margin: 0; font-size: 16px; color: #333;">解析结果</h3>
        <p style="margin: 4px 0 0 0; font-size: 12px; color: #666;">
          找到 ${results.forms} 个表单，${results.inputs.length} 个输入字段
        </p>
      </div>
      <div style="max-height: 300px; overflow-y: auto; padding: 8px;">
        ${results.inputs.map((input, index) => `
          <div style="padding: 8px; margin: 4px 0; background: #f8f9fa; border-radius: 6px; font-size: 12px;">
            <div style="font-weight: 500; color: #333; margin-bottom: 4px;">
              ${input.label || input.placeholder || input.name || `字段 ${index + 1}`}
            </div>
            <div style="color: #666;">
              类型: ${input.type} | 名称: ${input.name || '无'} | ID: ${input.id || '无'}
            </div>
          </div>
        `).join('')}
      </div>
      <div style="padding: 12px; border-top: 1px solid #eee; text-align: right;">
        <button id="close-result-panel" style="
          background: #6c757d;
          color: white;
          border: none;
          padding: 6px 12px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        ">关闭</button>
      </div>
    `;

    // 添加关闭按钮事件
    const closeButton = this.resultPanel.querySelector('#close-result-panel');
    closeButton.addEventListener('click', () => {
      this.hideResults();
    });

    this.resultPanel.style.display = 'block';

    // 5秒后自动隐藏
    setTimeout(() => {
      this.hideResults();
    }, 10000);
  }

  // 隐藏结果面板
  hideResults() {
    if (this.resultPanel) {
      this.resultPanel.style.display = 'none';
    }
  }

  // 显示错误信息
  showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #dc3545;
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      font-size: 14px;
      z-index: 10001;
    `;
    errorDiv.textContent = message;
    
    document.body.appendChild(errorDiv);
    
    setTimeout(() => {
      if (document.body.contains(errorDiv)) {
        document.body.removeChild(errorDiv);
      }
    }, 3000);
  }

  // 销毁解析器
  destroy() {
    if (this.parseButton && document.body.contains(this.parseButton)) {
      document.body.removeChild(this.parseButton);
    }
    if (this.resultPanel && document.body.contains(this.resultPanel)) {
      document.body.removeChild(this.resultPanel);
    }
    this.isInitialized = false;
  }
}

// 创建全局解析器实例
window.websiteParser = new WebsiteParser();

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.websiteParser.init();
  });
} else {
  window.websiteParser.init();
}

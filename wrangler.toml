# Cloudflare Pages Functions 配置
name = "autofill-app"
compatibility_flags = ["nodejs_compat"]

# 生产环境配置
[env.production]
# 这些变量需要在 Cloudflare Pages 控制台中设置
# API_KEY = "your-production-api-key"
# API_BASE_URL = "https://autoapi.utilityapp.site"
# PADDLE_WEBHOOK_SECRET = "your-paddle-webhook-secret"

[vars]
# 默认变量
API_BASE_URL = "https://autoapi.utilityapp.site"

# 临时禁用CSP以解决Paddle支付问题
# [[headers]]
#   for = "/*"
#   [headers.values]
#     # 更宽松的CSP配置以支持Paddle支付
#     Content-Security-Policy = "default-src 'self' 'unsafe-inline' 'unsafe-eval'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://static.cloudflareinsights.com https://cdn.paddle.com https://cdnjs.cloudflare.com https://sandbox-checkout-service.paddle.com https://checkout-service.paddle.com; style-src 'self' 'unsafe-inline' https://cdn.paddle.com; connect-src 'self' http://localhost:* http://127.0.0.1:* https://*.utilityapp.site chrome-extension://* https://*.paddle.com https://sandbox-checkout-service.paddle.com https://checkout-service.paddle.com https://vitals.vercel-insights.com; img-src 'self' data: https://*.paddle.com; frame-src 'self' https://*.paddle.com https://buy.paddle.com https://sandbox-checkout-service.paddle.com https://checkout-service.paddle.com; frame-ancestors 'self' http://localhost:* https://*.utilityapp.site; object-src 'none';"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle Token权限调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #efe; border-color: #cfc; }
        .error { background-color: #fee; border-color: #fcc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 12px 20px; margin: 8px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        button:hover { background: #005a87; }
        input { padding: 10px; width: 400px; border: 1px solid #ddd; border-radius: 3px; font-family: monospace; margin: 5px 0; }
        .log { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; font-size: 12px; }
        .debug-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .token-status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .status-good { background: #d4edda; color: #155724; }
        .status-bad { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Paddle Token权限调试</h1>
        
        <div class="section error">
            <h2>❌ 问题确认</h2>
            <div class="debug-info">
                <p><strong>CSP问题:</strong> ✅ 已解决（支付页面能打开）</p>
                <p><strong>JWT错误:</strong> ❌ 持续出现（Token权限问题）</p>
                <p><strong>产品数据:</strong> ❌ 为空（无法获取产品信息）</p>
            </div>
        </div>

        <div class="section warning">
            <h2>🔧 Token权限检查清单</h2>
            <div class="debug-info">
                <h3>在Paddle Dashboard中检查:</h3>
                <ol>
                    <li><strong>Token状态:</strong> 确认为Active</li>
                    <li><strong>Token权限:</strong> 确认有Checkout权限</li>
                    <li><strong>产品访问:</strong> 确认能访问产品和价格</li>
                    <li><strong>环境匹配:</strong> 确认是Sandbox环境Token</li>
                    <li><strong>账户状态:</strong> 确认账户完全激活</li>
                </ol>
            </div>
        </div>

        <div class="section info">
            <h2>🧪 Token调试测试</h2>
            <div>
                <label><strong>当前Token:</strong></label><br>
                <input type="text" id="currentToken" value="test_5c1f7fd4f3fb13fcf3c46516a93" readonly>
                <br>
                <label><strong>测试新Token:</strong></label><br>
                <input type="text" id="newToken" placeholder="输入新生成的Token进行测试">
                <br>
                <button onclick="testCurrentToken()">测试当前Token</button>
                <button onclick="testNewToken()">测试新Token</button>
                <button onclick="debugTokenInfo()">调试Token信息</button>
                <button onclick="clearLog()">清空日志</button>
            </div>
        </div>

        <div class="section">
            <h2>📝 调试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section" id="token-analysis" style="display: none;">
            <h2>🔍 Token分析结果</h2>
            <div id="analysis-content"></div>
        </div>

        <div class="section success" id="solution-section" style="display: none;">
            <h2>✅ 解决方案</h2>
            <div id="solution-content"></div>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            currentToken: 'test_5c1f7fd4f3fb13fcf3c46516a93',
            priceId: 'pri_01k1aenmy30gyzrq9wayftn5z4'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '5px';
            logEntry.style.padding = '5px';
            logEntry.style.borderRadius = '3px';
            
            if (type === 'success') {
                logEntry.style.backgroundColor = '#d4edda';
                logEntry.style.color = '#155724';
            } else if (type === 'error') {
                logEntry.style.backgroundColor = '#f8d7da';
                logEntry.style.color = '#721c24';
            } else if (type === 'warning') {
                logEntry.style.backgroundColor = '#fff3cd';
                logEntry.style.color = '#856404';
            } else {
                logEntry.style.backgroundColor = '#d1ecf1';
                logEntry.style.color = '#0c5460';
            }
            
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('token-analysis').style.display = 'none';
            document.getElementById('solution-section').style.display = 'none';
        }

        function showTokenAnalysis(token, result) {
            const analysisDiv = document.getElementById('analysis-content');
            const status = result.success ? 'status-good' : 'status-bad';
            
            analysisDiv.innerHTML = `
                <div class="token-status ${status}">
                    <h3>${result.success ? '✅' : '❌'} Token: ${token}</h3>
                    <p><strong>状态:</strong> ${result.status}</p>
                    <p><strong>错误:</strong> ${result.error || '无'}</p>
                    <p><strong>产品数据:</strong> ${result.hasProductData ? '有效' : '无效'}</p>
                </div>
            `;
            document.getElementById('token-analysis').style.display = 'block';
            
            if (result.success) {
                showSolution(token);
            }
        }

        function showSolution(workingToken) {
            const solutionDiv = document.getElementById('solution-content');
            solutionDiv.innerHTML = `
                <h3>🎉 找到有效Token！</h3>
                <p><strong>有效Token:</strong> <code>${workingToken}</code></p>
                <h4>下一步操作:</h4>
                <ol>
                    <li>更新 .env.local 文件:</li>
                    <pre>VITE_PADDLE_CLIENT_TOKEN=${workingToken}</pre>
                    <li>重启开发服务器: <code>npm run dev</code></li>
                    <li>测试主应用支付功能: <code>/pricing</code></li>
                </ol>
            `;
            document.getElementById('solution-section').style.display = 'block';
        }

        async function testToken(token, tokenName) {
            log(`🔍 测试${tokenName}...`, 'info');
            log(`Token: ${token}`, 'info');
            
            const result = {
                success: false,
                status: '未知',
                error: null,
                hasProductData: false
            };
            
            try {
                log('🔧 初始化Paddle...', 'info');
                
                await new Promise((resolve) => {
                    window.Paddle.Initialize({
                        token: token,
                        eventCallback: (data) => {
                            log(`📊 事件: ${JSON.stringify(data)}`, 'info');
                            
                            if (data.type === 'checkout.error') {
                                result.error = data.detail;
                                result.status = '错误';
                                
                                if (data.detail && data.detail.includes('JWT')) {
                                    log('❌ JWT错误 - Token无效或权限不足', 'error');
                                } else if (data.detail && data.detail.includes('authentication')) {
                                    log('❌ 认证错误 - Token格式或环境问题', 'error');
                                } else {
                                    log(`❌ 其他错误: ${data.detail}`, 'error');
                                }
                            } else if (data.type === 'checkout.loaded') {
                                log('✅ 支付页面加载成功！', 'success');
                                result.success = true;
                                result.status = '有效';
                            } else if (data.name === 'checkout.closed' && data.data) {
                                const item = data.data.items?.[0];
                                if (item?.price_id && item?.product?.name) {
                                    log('🎉 产品数据完整！Token完全有效！', 'success');
                                    result.hasProductData = true;
                                    result.success = true;
                                    result.status = '完全有效';
                                } else {
                                    log('⚠️ 支付页面能打开，但产品数据为空', 'warning');
                                    result.status = '部分有效';
                                }
                            }
                        }
                    });
                    
                    setTimeout(resolve, 1000); // 等待初始化
                });
                
                log('✅ Paddle初始化完成', 'success');
                
                // 测试支付
                log('💳 测试支付功能...', 'info');
                
                const testConfig = {
                    items: [{
                        priceId: config.priceId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                window.Paddle.Checkout.open(testConfig);
                log('✅ 支付测试请求已发送', 'success');
                
                // 等待结果
                await new Promise(resolve => setTimeout(resolve, 3000));
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`, 'error');
                result.error = error.message;
                result.status = '异常';
            }
            
            showTokenAnalysis(token, result);
            return result;
        }

        async function testCurrentToken() {
            await testToken(config.currentToken, '当前Token');
        }

        async function testNewToken() {
            const newToken = document.getElementById('newToken').value.trim();
            if (!newToken) {
                log('❌ 请输入新Token', 'error');
                return;
            }
            
            if (!newToken.startsWith('test_')) {
                log('❌ Token格式错误：应该以 "test_" 开头', 'error');
                return;
            }
            
            await testToken(newToken, '新Token');
        }

        function debugTokenInfo() {
            log('🔍 Token调试信息...', 'info');
            log(`当前Token长度: ${config.currentToken.length}`, 'info');
            log(`Token格式: ${config.currentToken.startsWith('test_') ? '✅ 正确' : '❌ 错误'}`, 'info');
            log(`环境: Sandbox`, 'info');
            log(`产品ID: ${config.priceId}`, 'info');
            
            log('💡 如果JWT错误持续，请检查:', 'warning');
            log('1. Paddle Dashboard中Token状态是否为Active', 'warning');
            log('2. Token是否有Checkout权限', 'warning');
            log('3. 账户是否完全验证', 'warning');
            log('4. 产品是否在同一环境中', 'warning');
        }

        // 页面加载时显示信息
        window.onload = function() {
            log('🔍 Token权限调试工具已加载', 'info');
            log('🎯 目标：找出JWT错误的根本原因', 'info');
            log('💡 建议先在Paddle Dashboard中检查Token权限设置', 'warning');
        };
    </script>
</body>
</html>

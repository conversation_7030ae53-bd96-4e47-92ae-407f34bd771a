/**
 * 设备ID生成工具
 * 使用混合确定性方案，确保清除存储后ID保持一致
 * 避免浏览器更新导致的ID变化
 */

export interface DeviceFeatures {
  // 第一层：系统特征（最稳定）
  screen: string;           // 屏幕分辨率和色深
  timezone: string;         // 时区信息
  platform: string;         // 平台信息
  hardwareConcurrency: number; // CPU核心数

  // 第二层：浏览器特征（较稳定，过滤版本号）
  userAgentCore: string;    // User-Agent核心部分（无版本号）
  language: string;         // 语言设置
  maxTouchPoints: number;   // 触摸点数
  deviceMemory: number;     // 设备内存

  // 第三层：渲染特征（高唯一性）
  canvasFingerprint: string; // 简化Canvas指纹
}

/**
 * 提取User-Agent核心部分，移除版本号避免浏览器更新影响
 */
function extractUserAgentCore(userAgent: string): string {
  try {
    // 移除所有版本号 (如 Chrome/120.0.0.0 -> Chrome/X.X.X)
    let core = userAgent
      .replace(/\d+\.\d+\.\d+(\.\d+)?/g, 'X.X.X')
      // 移除构建号和详细版本信息
      .replace(/\s+\([^)]*\d+[^)]*\)/g, ' (X)')
      // 保留主要浏览器标识
      .replace(/(Chrome|Firefox|Safari|Edge|Opera)\/X\.X\.X/g, '$1')
      // 移除多余空格
      .replace(/\s+/g, ' ')
      .trim();

    // 提取关键标识符
    const browserMatch = core.match(/(Chrome|Firefox|Safari|Edge|Opera)/);
    const osMatch = core.match(/(Windows|Mac|Linux|Android|iOS)/i);

    return `${browserMatch?.[1] || 'Unknown'}_${osMatch?.[1] || 'Unknown'}`;
  } catch (error) {
    console.warn('User-Agent解析失败:', error);
    return 'Unknown_Unknown';
  }
}

/**
 * 生成简化Canvas指纹
 */
function generateCanvasFingerprint(): string {
  try {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return 'no_canvas';

    // 设置固定尺寸避免分辨率影响
    canvas.width = 200;
    canvas.height = 50;

    // 绘制简单图形，利用GPU渲染差异
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillStyle = '#f60';
    ctx.fillRect(125, 1, 62, 20);
    ctx.fillStyle = '#069';
    ctx.fillText('Device ID Test 🔒', 2, 15);
    ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
    ctx.fillText('Device ID Test 🔒', 4, 17);

    // 获取图像数据并生成简短哈希
    const imageData = canvas.toDataURL();
    let hash = 0;
    for (let i = 0; i < imageData.length; i++) {
      const char = imageData.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }

    return Math.abs(hash).toString(36);
  } catch (error) {
    console.warn('Canvas指纹生成失败:', error);
    return 'canvas_error';
  }
}

/**
 * 获取平台信息（兼容性处理）
 */
function getPlatformInfo(): string {
  try {
    // 优先使用新的API
    if ('userAgentData' in navigator && (navigator as any).userAgentData) {
      const uaData = (navigator as any).userAgentData;
      return uaData.platform || navigator.platform || 'Unknown';
    }
    // 降级使用传统API
    return navigator.platform || 'Unknown';
  } catch (error) {
    return 'Unknown';
  }
}

/**
 * 收集设备特征
 */
export function collectDeviceFeatures(): DeviceFeatures {
  return {
    // 第一层：系统特征
    screen: `${screen.width}x${screen.height}x${screen.colorDepth}`,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    platform: getPlatformInfo(),
    hardwareConcurrency: navigator.hardwareConcurrency || 0,

    // 第二层：浏览器特征（稳定化处理）
    userAgentCore: extractUserAgentCore(navigator.userAgent),
    language: navigator.language,
    maxTouchPoints: navigator.maxTouchPoints || 0,
    deviceMemory: (navigator as any).deviceMemory || 0,

    // 第三层：渲染特征
    canvasFingerprint: generateCanvasFingerprint()
  };
}

/**
 * 生成设备特征的确定性哈希
 */
async function generateFeaturesHash(features: DeviceFeatures): Promise<string> {
  try {
    // 按权重排序特征，确保一致性
    const orderedFeatures = {
      // 高权重特征
      screen: features.screen,
      timezone: features.timezone,
      platform: features.platform,

      // 中权重特征
      hardwareConcurrency: features.hardwareConcurrency,
      language: features.language,
      userAgentCore: features.userAgentCore,

      // 低权重特征
      maxTouchPoints: features.maxTouchPoints,
      deviceMemory: features.deviceMemory,
      canvasFingerprint: features.canvasFingerprint
    };

    const featuresString = JSON.stringify(orderedFeatures);
    const encoder = new TextEncoder();
    const data = encoder.encode(featuresString);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));

    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  } catch (error) {
    console.error('设备特征哈希生成失败:', error);
    // 降级方案：使用简单哈希
    const fallbackString = JSON.stringify(features);
    let hash = 0;
    for (let i = 0; i < fallbackString.length; i++) {
      const char = fallbackString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(16);
  }
}

/**
 * 将哈希转换为UUID格式
 */
function hashToUUID(hash: string): string {
  // 确保哈希长度足够
  const paddedHash = (hash + '0'.repeat(32)).substring(0, 32);

  // 转换为UUID格式: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
  const uuid = [
    paddedHash.substring(0, 8),
    paddedHash.substring(8, 12),
    '4' + paddedHash.substring(13, 16), // 版本号4
    ((parseInt(paddedHash.substring(16, 17), 16) & 0x3) | 0x8).toString(16) + paddedHash.substring(17, 20), // 变体位
    paddedHash.substring(20, 32)
  ].join('-');

  return uuid;
}

// 设备ID缓存配置
const DEVICE_ID_STORAGE_KEY = 'device_fingerprint_id';
const DEVICE_FEATURES_STORAGE_KEY = 'device_features_cache';
const CACHE_VERSION = '1.0';

/**
 * 从localStorage获取缓存的设备ID
 */
function getCachedDeviceId(): string | null {
  try {
    const cached = localStorage.getItem(DEVICE_ID_STORAGE_KEY);
    if (cached) {
      const data = JSON.parse(cached);
      // 检查缓存版本
      if (data.version === CACHE_VERSION && data.deviceId) {
        return data.deviceId;
      }
    }
  } catch (error) {
    console.warn('读取缓存设备ID失败:', error);
  }
  return null;
}

/**
 * 缓存设备ID到localStorage
 */
function cacheDeviceId(deviceId: string, features: DeviceFeatures): void {
  try {
    const cacheData = {
      version: CACHE_VERSION,
      deviceId,
      features,
      timestamp: Date.now(),
      userAgent: navigator.userAgent // 用于检测浏览器更新
    };
    localStorage.setItem(DEVICE_ID_STORAGE_KEY, JSON.stringify(cacheData));

    // 同时缓存特征数据用于调试
    localStorage.setItem(DEVICE_FEATURES_STORAGE_KEY, JSON.stringify(features));
  } catch (error) {
    console.warn('缓存设备ID失败:', error);
  }
}

/**
 * 检查缓存的设备ID是否仍然有效
 */
function isCachedDeviceIdValid(): boolean {
  try {
    const cached = localStorage.getItem(DEVICE_ID_STORAGE_KEY);
    if (!cached) return false;

    const data = JSON.parse(cached);

    // 检查版本
    if (data.version !== CACHE_VERSION) return false;

    // 检查时间戳（可选：设置过期时间）
    const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天
    if (Date.now() - data.timestamp > maxAge) return false;

    // 检查User-Agent核心部分是否变化（检测浏览器大版本更新）
    const currentUACore = extractUserAgentCore(navigator.userAgent);
    const cachedUACore = extractUserAgentCore(data.userAgent || '');
    if (currentUACore !== cachedUACore) {
      console.log('检测到浏览器更新，重新生成设备ID');
      return false;
    }

    return true;
  } catch (error) {
    console.warn('验证缓存设备ID失败:', error);
    return false;
  }
}

/**
 * 生成设备ID（主函数）
 * 确保清除存储后ID保持一致，避免浏览器更新影响
 */
export async function generateDeviceId(): Promise<string> {
  try {
    // 1. 尝试从缓存获取
    if (isCachedDeviceIdValid()) {
      const cachedId = getCachedDeviceId();
      if (cachedId) {
        console.log('🔄 使用缓存的设备ID:', cachedId);
        return cachedId;
      }
    }

    // 2. 收集设备特征
    console.log('🔍 收集设备特征...');
    const features = collectDeviceFeatures();

    // 3. 生成确定性哈希
    console.log('🔐 生成设备指纹...');
    const hash = await generateFeaturesHash(features);

    // 4. 转换为UUID格式
    const deviceId = hashToUUID(hash);

    // 5. 缓存结果
    cacheDeviceId(deviceId, features);

    console.log('✅ 设备ID生成完成:', deviceId);
    console.log('📊 设备特征:', features);

    return deviceId;
  } catch (error) {
    console.error('❌ 设备ID生成失败:', error);

    // 降级方案：生成随机UUID
    const fallbackId = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });

    console.warn('🔄 使用降级方案生成设备ID:', fallbackId);
    return fallbackId;
  }
}

/**
 * 清除设备ID缓存（用于测试）
 */
export function clearDeviceIdCache(): void {
  try {
    localStorage.removeItem(DEVICE_ID_STORAGE_KEY);
    localStorage.removeItem(DEVICE_FEATURES_STORAGE_KEY);
    console.log('🗑️ 设备ID缓存已清除');
  } catch (error) {
    console.warn('清除设备ID缓存失败:', error);
  }
}

/**
 * 获取设备ID调试信息
 */
export function getDeviceIdDebugInfo(): any {
  const features = collectDeviceFeatures();
  const cached = getCachedDeviceId();
  const isValid = isCachedDeviceIdValid();

  return {
    currentFeatures: features,
    cachedDeviceId: cached,
    isCacheValid: isValid,
    cacheVersion: CACHE_VERSION,
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    userAgentCore: extractUserAgentCore(navigator.userAgent)
  };
}

/**
 * 测试设备ID生成的一致性
 */
export async function testDeviceIdConsistency(): Promise<void> {
  console.log('=== 设备ID一致性测试 ===');

  // 第一次生成
  const id1 = await generateDeviceId();
  console.log('第一次生成:', id1);

  // 第二次生成（应该使用缓存）
  const id2 = await generateDeviceId();
  console.log('第二次生成:', id2);

  // 清除缓存后重新生成
  clearDeviceIdCache();
  const id3 = await generateDeviceId();
  console.log('清除缓存后生成:', id3);

  // 验证一致性
  const isConsistent = id1 === id2 && id2 === id3;
  console.log('一致性测试:', isConsistent ? '✅ 通过' : '❌ 失败');

  if (!isConsistent) {
    console.warn('设备ID不一致，可能的原因:');
    console.warn('- 设备特征在测试期间发生变化');
    console.warn('- 随机因素影响（Canvas指纹等）');
    console.warn('- 浏览器环境变化');
  }

  // 显示调试信息
  console.log('调试信息:', getDeviceIdDebugInfo());
}

/**
 * 导出主要函数供其他模块使用
 */
export { generateDeviceId as getDeviceId };
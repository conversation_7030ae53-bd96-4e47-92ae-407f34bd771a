# Paddle 配置指南

## 📋 需要配置的 Paddle ID

### 🔑 必需的 ID 配置

| 配置项 | 环境变量 | 说明 | 获取位置 | 示例格式 |
|--------|----------|------|----------|----------|
| **Vendor ID** | `VITE_PADDLE_VENDOR_ID` | Paddle 卖家 ID | Developer Tools → Authentication | `231761` |
| **Price ID** | `VITE_PADDLE_PRODUCT_YEARLY` | 年度订阅产品价格 ID | Catalog → Prices | `pri_01jxbxghewg29tbwy909vmanb9` |

### 🌐 环境设置

| 配置项 | 环境变量 | 可选值 | 说明 |
|--------|----------|--------|------|
| **环境** | `VITE_PADDLE_ENVIRONMENT` | `production` / `sandbox` | 生产环境使用 `production` |

## 🚀 配置步骤

### 1. 登录 Paddle 控制台
访问 [Paddle 控制台](https://vendors.paddle.com/) 并登录

### 2. 获取 Vendor ID (卖家 ID)
1. 进入 **Developer Tools** → **Authentication**
2. 复制 **Seller ID** (新版) 或 **Vendor ID** (旧版)
3. 这是一个数字，例如：`231761`

### 3. 创建产品和价格
1. 进入 **Catalog** → **Products**
2. 点击 **Create Product** 创建新产品
3. 填写产品信息：
   - Product Name: `AutoFill Premium`
   - Description: `AutoFill 高级版年度订阅`
4. 创建价格：
   - 点击 **Add Price**
   - Amount: `9.99`
   - Currency: `USD`
   - Billing Cycle: `Annual` (年度)
5. 复制生成的 **Price ID**，格式如：`pri_01jxbxghewg29tbwy909vmanb9`

### 4. 配置环境变量
编辑 `.env.local` 文件：

```bash
# Paddle 支付配置
VITE_PADDLE_ENVIRONMENT=production
VITE_PADDLE_VENDOR_ID=231761
VITE_PADDLE_PRODUCT_YEARLY=pri_01jxbxghewg29tbwy909vmanb9
```

### 5. 验证配置
运行配置检查命令：
```bash
npm run check-paddle
```

## 🔧 Paddle 控制台其他配置

### 域名白名单
1. 进入 **Settings** → **Checkout Settings**
2. 在 **Allowed Domains** 中添加你的域名：
   - `localhost:5173` (开发环境)
   - `your-domain.com` (生产环境)

### Webhook 配置 (可选)
1. 进入 **Developer Tools** → **Notifications**
2. 添加 Webhook URL：`https://your-domain.com/api/paddle/webhook`
3. 选择需要监听的事件：
   - `subscription.created`
   - `subscription.updated`
   - `subscription.canceled`

## ⚠️ 注意事项

### 🔐 安全提醒
- **不要提交** `.env.local` 文件到 Git
- **生产环境**使用 Cloudflare Pages 环境变量设置
- **定期轮换** API 密钥

### 🌍 环境区分
- **开发/测试**: 使用 `sandbox` 环境，不会实际收费
- **生产**: 使用 `production` 环境，会实际收费

### 💰 价格设置
- 确保价格设置正确，一旦发布较难修改
- 可以创建多个价格方案 (月度/年度)

## 🐛 常见问题

### 1. 支付页面无法打开
- ✅ 检查 Vendor ID 是否正确
- ✅ 确认 Price ID 格式正确 (以 `pri_` 开头)
- ✅ 验证域名是否在白名单中

### 2. 支付完成后跳转失败
- ✅ 检查域名配置
- ✅ 确认回调 URL 可访问

### 3. 配置验证失败
```bash
# 检查配置
npm run check-paddle

# 查看控制台日志
npm run dev
```

## 📞 获取帮助

如果遇到问题：
1. 查看 [Paddle 官方文档](https://developer.paddle.com/)
2. 检查浏览器控制台错误信息
3. 运行 `npm run check-paddle` 验证配置

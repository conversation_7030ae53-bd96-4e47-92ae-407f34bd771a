<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle域名配置修复</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .log { max-height: 400px; overflow-y: auto; }
        .domain-list { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .domain-item { padding: 5px 0; border-bottom: 1px solid #eee; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Paddle域名配置修复</h1>
        
        <div class="section error">
            <h2>❌ 检测到的问题</h2>
            <ol>
                <li><strong>CSP错误:</strong> "frame-ancestors http://localhost" - 域名白名单配置问题</li>
                <li><strong>400错误:</strong> checkout-service返回400 - 请求参数问题</li>
            </ol>
        </div>

        <div class="section warning">
            <h2>🎯 需要在Paddle Dashboard中配置的域名</h2>
            <p><strong>配置路径:</strong> Developer tools > Checkout settings > Allowed domains</p>
            
            <div class="domain-list">
                <h3>📋 请添加以下域名:</h3>
                <div class="domain-item">✅ <code>http://localhost:5174</code> - 当前开发端口</div>
                <div class="domain-item">✅ <code>http://localhost:5173</code> - 备用开发端口</div>
                <div class="domain-item">✅ <code>http://localhost</code> - 本地开发</div>
                <div class="domain-item">✅ <code>https://autofill.utilityapp.site</code> - 生产域名</div>
            </div>
            
            <p><strong>⚠️ 注意:</strong> 确保使用 <code>http://</code> 而不是 <code>https://</code> 对于localhost</p>
        </div>

        <div class="section info">
            <h2>📋 当前环境信息</h2>
            <pre id="env-info"></pre>
        </div>

        <div class="section">
            <h2>🧪 域名配置测试</h2>
            <button onclick="testDomainConfig()">1. 测试域名配置</button>
            <button onclick="testSimpleCheckout()">2. 测试简化支付</button>
            <button onclick="testWithDifferentSettings()">3. 测试不同设置</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section success" id="success-info" style="display: none;">
            <h2>🎉 配置成功！</h2>
            <p>域名配置正确，支付页面可以正常打开。</p>
        </div>

        <div class="section info">
            <h2>💡 故障排除步骤</h2>
            <ol>
                <li><strong>检查域名配置:</strong> 确保在Paddle Dashboard中添加了正确的域名</li>
                <li><strong>等待生效:</strong> 域名配置可能需要几分钟才能生效</li>
                <li><strong>清除缓存:</strong> 清除浏览器缓存并刷新页面</li>
                <li><strong>检查产品状态:</strong> 确认产品和价格都是Active状态</li>
                <li><strong>联系支持:</strong> 如果问题持续，联系Paddle技术支持</li>
            </ol>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'test_459728092d35d36bc173c60e52d',
            productId: 'pri_01k0xttmf9wekbg957y66xawj',
            currentDomain: window.location.origin
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('success-info').style.display = 'none';
        }

        function showEnvInfo() {
            const envInfo = {
                currentDomain: config.currentDomain,
                protocol: window.location.protocol,
                hostname: window.location.hostname,
                port: window.location.port,
                fullUrl: window.location.href,
                userAgent: navigator.userAgent.substring(0, 80) + '...'
            };
            document.getElementById('env-info').textContent = JSON.stringify(envInfo, null, 2);
        }

        function testDomainConfig() {
            log('🌐 测试1: 域名配置检查...', 'info');
            
            log(`当前域名: ${config.currentDomain}`, 'info');
            log(`协议: ${window.location.protocol}`, 'info');
            log(`主机: ${window.location.hostname}`, 'info');
            log(`端口: ${window.location.port}`, 'info');
            
            // 检查是否是localhost
            if (window.location.hostname === 'localhost') {
                log('✅ 检测到localhost环境', 'success');
                log('💡 请确保在Paddle Dashboard中添加了http://localhost:' + window.location.port, 'warning');
            } else {
                log('✅ 检测到生产环境', 'success');
                log('💡 请确保在Paddle Dashboard中添加了' + config.currentDomain, 'warning');
            }
        }

        function testSimpleCheckout() {
            log('💳 测试2: 简化支付配置...', 'info');
            
            try {
                // 初始化Paddle
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            log(`❌ Checkout错误: ${data.detail}`, 'error');
                            
                            if (data.detail && data.detail.includes('frame-ancestors')) {
                                log('🔍 CSP错误 - 域名白名单问题', 'error');
                                log('💡 请在Paddle Dashboard中添加当前域名到白名单', 'warning');
                            } else if (data.detail && data.detail.includes('400')) {
                                log('🔍 400错误 - 请求参数问题', 'error');
                                log('💡 可能是产品ID或配置问题', 'warning');
                            }
                        } else if (data.type === 'checkout.loaded') {
                            log('✅ 支付页面加载成功！', 'success');
                            document.getElementById('success-info').style.display = 'block';
                        }
                    }
                });

                log('✅ Paddle初始化成功', 'success');
                
                // 使用最简配置打开支付页面
                setTimeout(() => {
                    log('🚀 打开支付页面...', 'info');
                    
                    window.Paddle.Checkout.open({
                        items: [{
                            priceId: config.productId,
                            quantity: 1
                        }]
                    });
                }, 1000);
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        function testWithDifferentSettings() {
            log('🔧 测试3: 不同设置配置...', 'info');
            
            try {
                // 尝试不同的配置选项
                window.Paddle.Checkout.open({
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: 'overlay',
                        theme: 'light',
                        frameTarget: 'self', // 尝试在当前窗口打开
                        frameStyle: 'width: 100%; min-width: 312px; background-color: transparent; border: none;'
                    }
                });
                
                log('✅ 使用不同设置的支付请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 不同设置测试失败: ${error.message}`, 'error');
            }
        }

        // 监听CSP错误
        window.addEventListener('securitypolicyviolation', function(e) {
            log(`🚨 CSP违规检测: ${e.violatedDirective} - ${e.blockedURI}`, 'error');
            log('💡 这确认了域名白名单配置问题', 'warning');
        });

        // 页面加载时显示环境信息
        window.onload = function() {
            showEnvInfo();
            log('🔧 Paddle域名配置修复工具已加载', 'info');
            log('💡 请先在Paddle Dashboard中配置域名白名单', 'warning');
            log(`当前需要添加的域名: ${config.currentDomain}`, 'info');
        };
    </script>
</body>
</html>

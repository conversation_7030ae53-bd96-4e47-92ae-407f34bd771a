/**
 * CORS代理工具
 * 用于处理CORS问题的工具函数
 */

import { log } from './logger';

// 可用的CORS代理服务列表
const CORS_PROXIES = [
  'https://corsproxy.io/?',
  'https://cors-anywhere.herokuapp.com/',
  'https://api.allorigins.win/raw?url='
];

/**
 * 通过CORS代理发送请求
 * @param url 原始URL
 * @param options 请求选项
 * @returns 响应Promise
 */
export async function fetchWithCorsProxy(url: string, options: RequestInit = {}): Promise<Response> {
  // 首先尝试直接请求
  try {
    log.debug('尝试直接请求:', url);
    const directResponse = await fetch(url, options);
    if (directResponse.ok) {
      log.debug('直接请求成功');
      return directResponse;
    }
    log.debug('直接请求失败，状态码:', directResponse.status);
  } catch (error) {
    log.error('直接请求失败:', error);
  }

  // 如果直接请求失败，尝试使用CORS代理
  let lastError: Error | null = null;
  for (const proxy of CORS_PROXIES) {
    try {
      const proxyUrl = `${proxy}${encodeURIComponent(url)}`;
      log.debug('尝试通过代理请求:', proxyUrl);
      const response = await fetch(proxyUrl, options);
      if (response.ok) {
        log.debug('代理请求成功');
        return response;
      }
      log.debug('代理请求失败，状态码:', response.status);
    } catch (error) {
      log.error(`代理 ${proxy} 请求失败:`, error);
      lastError = error as Error;
    }
  }

  // 所有尝试都失败
  throw lastError || new Error('所有CORS代理请求均失败');
}

/**
 * 创建代理URL
 * @param url 原始URL
 * @returns 代理URL
 */
export function createProxyUrl(url: string): string {
  // 使用第一个代理
  return `${CORS_PROXIES[0]}${encodeURIComponent(url)}`;
}

/**
 * 检测是否需要使用CORS代理
 * @returns 是否需要使用CORS代理
 */
export function shouldUseCorsProxy(): boolean {
  // 检查是否有环境变量控制
  const forceCorsProxy = import.meta.env.VITE_FORCE_CORS_PROXY === 'true';
  const disableCorsProxy = import.meta.env.VITE_DISABLE_CORS_PROXY === 'true';
  
  if (forceCorsProxy) return true;
  if (disableCorsProxy) return false;
  
  // 默认不使用代理
  return false;
} 
{"name": "autofill-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:functions": "wrangler pages dev --compatibility-flag=nodejs_compat -- npm run dev", "build": "tsc && vite build", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "preview": "vite preview", "typecheck": "tsc", "check-env": "node scripts/check-env.js", "check-paddle": "node scripts/check-paddle-config.js"}, "dependencies": {"@paddle/paddle-js": "^1.4.2", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.2", "react-router-dom": "^6.23.1"}, "devDependencies": {"@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "terser": "^5.39.1", "typescript": "^5.1.6", "vite": "^5.3.5", "vite-tsconfig-paths": "^4.2.1", "wrangler": "^3.114.9"}, "engines": {"node": ">=20.0.0"}}
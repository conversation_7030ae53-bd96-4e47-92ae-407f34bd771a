# API 环境变量设置指南

## 问题诊断

如果您在使用应用时遇到以下问题：
- "环境变量未设置"的警告
- API 请求返回 401 Unauthorized 错误
- CORS 错误 (跨域资源共享问题)

请按照本指南设置正确的环境变量。

## 环境变量设置步骤

### 1. 创建 .env.local 文件

在项目根目录 (与 package.json 同级) 创建一个名为 `.env.local` 的文件。

### 2. 添加必要的环境变量

将以下内容复制到 `.env.local` 文件中，并替换为您的实际值：

```
# API基础URL，用于所有API请求 (必需)
VITE_API_URL=https://autoapi.utilityapp.site

# API密钥，用于访问后端服务 (必需)
VITE_API_KEY=your_api_key_here
```

请将 `your_api_key_here` 替换为您的实际 API 密钥。

### 3. 重启开发服务器

创建或修改 `.env.local` 文件后，您必须重启开发服务器才能使新的环境变量生效：

```bash
# 按 Ctrl+C 停止当前运行的开发服务器
# 然后重新启动
npm run dev
```

## 验证环境变量是否正确加载

重启后，打开浏览器控制台 (F12)，应该能看到类似以下的日志：

```
环境变量配置:
- VITE_API_URL (原始值): https://autoapi.utilityapp.site
- API_URL (最终使用): https://autoapi.utilityapp.site
- API_KEY: 已设置 (不显示具体值)
- 开发环境: 是
```

如果 `VITE_API_URL` 显示为 `undefined` 或 `API_KEY` 显示为 `未设置`，说明环境变量未正确加载。

## 解决 CORS 问题

即使环境变量设置正确，您仍可能遇到 CORS 错误：

```
Access to fetch at 'https://autoapi.utilityapp.site/user/me' from origin 'http://localhost:5173' has been blocked by CORS policy
```

这是因为后端服务器需要明确允许来自您开发环境的请求。解决方法：

1. **联系后端开发人员**：请他们在服务器配置中添加 `Access-Control-Allow-Origin: http://localhost:5173`。

2. **如果您无法修改后端**：可以考虑使用 CORS 代理服务。项目中已包含 `src/utils/corsProxy.ts`，但默认未启用。

## 注意事项

- `.env.local` 文件不应提交到版本控制系统，因为它可能包含敏感信息。
- 生产环境的环境变量应通过您的部署平台 (如 Cloudflare Pages) 的环境变量设置功能进行配置。
- 如果您使用的是 Docker 或其他容器化环境，请确保正确地将环境变量传递给容器。 
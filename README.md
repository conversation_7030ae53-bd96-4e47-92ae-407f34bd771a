# AutoFill React 项目说明

## 项目简介

本项目是一个基于 **React** + **Vite** 开发的现代化 Web 应用，旨在介绍和推广一款浏览器自动填表插件。前端通过 Cloudflare Pages 部署，支持多语言（中英文）、Paddle 支付、用户认证、数据加密存储等功能。

---

## 主要功能

- 多语言国际化（中英文自动切换）
- 邮箱验证码登录/注册
- 用户中心（账户信息、订阅状态、登出）
- Paddle 支付集成（订阅、试用期、自动弹窗，自动切换支付语言）
- 定价与功能对比
- 安全存储（本地数据加密，支持扩展与网页同步）
- API 环境变量管理（多环境灵活切换）
- 统一日志与调试（生产环境自动屏蔽调试日志）

---

## 目录结构

```
/src
  /components      # 共享UI组件
  /pages           # 页面组件
  /services        # 业务服务（API、存储、支付等）
  /config          # 配置文件
  /contexts        # React上下文
  /i18n            # 国际化配置
  /utils           # 工具函数
  index.css
  main.tsx
  App.tsx
/public             # 静态资源
```

---

## 环境变量配置

在项目根目录创建 `.env.local` 文件，示例：

```env
VITE_API_URL=https://autoapi.utilityapp.site/auth
VITE_API_KEY=your_api_key_here
VITE_USERAPI_URL=https://autoapi.utilityapp.site
VITE_PADDLE_CLIENT_TOKEN=your_paddle_client_token
VITE_PADDLE_PRODUCT_YEARLY=pri_xxxxxxxxxxxxxxxxxxxxxxx
```

---

## 核心接口说明

### 邮箱验证码相关

#### 1. 请求验证码
- **接口**: `POST ${VITE_API_URL}/auth/request-code`
- **描述**: 向指定邮箱发送登录验证码，包含设备指纹信息
- **请求体**:
  ```json
  {
    "email": "<EMAIL>",
    "device_id": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
    "device_info": {
      "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...",
      "platform": "Win32",
      "screen": "1920x1080",
      "timezone": "Asia/Shanghai"
    }
  }
  ```
- **设备指纹说明**:
  - `device_id`: 32位设备指纹哈希，由前端生成
  - `device_info`: 设备基础信息，用于后端验证和记录
- **响应**: 
  - `200`: 返回状态码数字
    - `1`: 成功发送验证码
    - `2`: 已有未过期验证码，请稍后再试
    - `3`: 发送失败
- **错误**: 
  - `400`: 邮箱格式无效
  - `500`: 服务器内部错误

#### 2. 验证码登录
- **接口**: `POST ${VITE_API_URL}/auth/verify-code`
- **描述**: 使用邮箱和验证码进行登录
- **请求体**:
  ```json
  {
    "email": "<EMAIL>",
    "code": "123456"
  }
  ```
- **响应**:
  ```json
  {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "refresh_token": "dGhpcyBpcyBhIHJlZnJlc2g...",
    "user_id": "uuid-string",
    "email": "<EMAIL>",
    "expires_in": 3600,
    "token_type": "Bearer"
  }
  ```
- **错误**:
  - `400`: 验证码无效或已过期
  - `401`: 认证失败

### 用户信息与令牌

#### 3. 刷新访问令牌
- **接口**: `POST ${VITE_API_URL}/auth/refresh-token`
- **描述**: 使用refresh_token获取新的access_token
- **请求体**:
  ```json
  {
    "refresh_token": "dGhpcyBpcyBhIHJlZnJlc2g..."
  }
  ```
- **响应**:
  ```json
  {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "refresh_token": "dGhpcyBpcyBhIHJlZnJlc2g...",
    "expires_in": 3600,
    "token_type": "Bearer"
  }
  ```
- **错误**:
  - `401`: refresh_token无效或已过期

#### 4. 获取用户信息
- **接口**: `GET ${VITE_USERAPI_URL}/user/me`
- **描述**: 获取当前用户的详细信息和订阅状态
- **请求头**: `Authorization: Bearer <access_token>`
- **响应**:
  ```json
  {
    "user_id": "uuid-string",
    "email": "<EMAIL>",
    "subscription_info": {
      "status": "trialing",
      "plan_name": "Premium Plan",
      "is_trial": true,
      "current_period_start": "2025-07-25T10:09:29.000Z",
      "current_period_end": "2025-08-24T10:09:29.000Z",
      "trial_ends_at": "2025-08-24T10:09:29.000Z",
      "cancel_at_period_end": false,
      "can_cancel": true,
      "cancel_reason": null,
      "days_remaining": 25,
      "has_trialed": false,
      "trial_started_at": "2025-07-25T10:09:29.000Z"
    }
  }
  ```
- **订阅状态说明**:
  - `status`: 订阅状态 (`null`, `trialing`, `active`, `canceled`, `paused`, `past_due`)
  - `is_trial`: 当前是否为试用期
  - `has_trialed`: **关键字段** - 用户是否已经使用过试用期（包括已取消的）
  - `trial_started_at`: 首次试用开始时间
  - `can_cancel`: 是否可以直接退订
  - `days_remaining`: 试用期或可退订期的剩余天数
  - `cancel_reason`: 不能取消的原因说明

##### 试用期规则说明
- **首次订阅**: 如果 `has_trialed: false`，则获得试用期
- **试用期内取消**: 取消后 `has_trialed` 变为 `true`，失去试用资格
- **重新订阅**: 如果 `has_trialed: true`，则直接付费，无试用期
- **永久限制**: 一旦 `has_trialed: true`，该账号永远失去试用资格

#### 5. 取消订阅
- **接口**: `POST ${VITE_USERAPI_URL}/user/me/qx`
- **描述**: 取消当前用户的订阅（仅限试用期用户）
- **请求头**: `Authorization: Bearer <access_token>`
- **请求体**:
  ```json
  {
    "confirm": true,
    "user_id": "uuid-string"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "type": "qx"
  }
  ```
- **错误响应**:
  ```json
  {
    "success": false,
    "type": "qx",
    "message": "试用期已结束，无法退订"
  }
  ```
- **业务规则**:
  - 只有试用期用户（`status: "trialing"` 或 `can_cancel: true`）可以退订
  - 付费用户在试用期结束后不能直接退订
  - **试用期取消后果**: 取消后 `has_trialed` 变为 `true`，永久失去试用资格
  - **重新订阅**: 如果 `has_trialed: true`，则直接付费，无试用期

#### 6. 用户登出
- **接口**: `POST ${VITE_API_URL}/auth/logout`
- **描述**: 登出当前用户，使令牌失效
- **请求头**: `Authorization: Bearer <access_token>`
- **请求体**:
  ```json
  {
    "refresh_token": "dGhpcyBpcyBhIHJlZnJlc2g..."
  }
  ```
- **响应**: `200 OK` (无响应体)
- **错误**:
  - `401`: 令牌无效

### Paddle 支付

- **弹窗支付**  
  使用 `@paddle/paddle-js` 新版 SDK，代码示例：

  ```js
  await paddle.Checkout.open({
    items: [{ priceId: PADDLE_PRODUCT_ID }],
    customer: { email: currentUser?.email || '' },
    settings: {
      displayMode: 'overlay',
      locale: i18n.language.startsWith('zh') ? 'zh-Hans' : 'en'
    }
  });
  ```

---

## 本地开发与调试

1. 安装依赖
   ```bash
   npm install
   ```
2. 启动开发服务器
   ```bash
   npm run dev
   ```
3. 代码检查
   ```bash
   npm run lint
   ```
4. 预览构建结果
   ```bash
   npm run build
   npm run preview
   ```

---

## 部署

- 构建生产版本：
  ```bash
  npm run build
  ```
- 将 `dist` 目录部署到 Cloudflare Pages 或其它静态托管平台。

---

## 其它说明

- 日志：全局统一使用 `logger`，生产环境自动屏蔽调试日志。
- 存储：所有本地和扩展存储操作统一通过 `storageAdapter.ts` 实现。
- 多语言：所有页面和弹窗均支持中英文自动切换。

---

## 设备指纹

项目使用设备指纹技术生成唯一设备ID，用于设备限制功能。

### 功能特性
- **稳定性高**: 使用最稳定的硬件和系统特征
- **唯一性强**: 结合多个特征生成32位哈希ID
- **兼容性好**: 支持降级方案，确保在各种环境下都能工作
- **隐私友好**: 不收集敏感信息，只使用公开的系统特征

### 核心特征
1. **屏幕分辨率** - 显示器硬件特征
2. **操作系统平台** - 系统基础信息
3. **时区设置** - 地理位置特征
4. **颜色深度** - 显示器特征
5. **CPU核心数** - 硬件特征
6. **设备像素比** - 显示器特征
7. **语言设置** - 系统配置
8. **触摸点数** - 移动设备特征
9. **设备内存** - 硬件特征（如果支持）

### 使用方法
```typescript
import { generateDeviceId, getDeviceFeatures } from '../utils/deviceFingerprint';

// 生成设备ID
const deviceId = await generateDeviceId();

// 获取设备特征
const features = await getDeviceFeatures();
```

### 测试
- 访问 `/test-device-fingerprint.html` 进行功能测试
- 使用 `window.testDeviceFingerprint()` 进行程序化测试

---

## 日志管理

项目使用统一的日志管理系统，位于 `src/utils/logger.ts`，**生产环境自动完全屏蔽所有日志输出**：

### 特性
- **🚫 生产环境零日志**：多重检查确保生产环境绝对不会输出任何日志
- **🔧 开发环境友好**：开发环境提供完整的调试信息
- **📊 多级日志**：支持 debug、info、warn、error 四个级别
- **🎨 统一格式**：所有日志都有统一的格式和时间戳
- **📋 分组支持**：支持日志分组和性能计时

### 使用方法

```typescript
import { log } from '../utils/logger';

// 基本日志
log.debug('调试信息', data);
log.info('一般信息', data);
log.warn('警告信息', data);
log.error('错误信息', error);

// 分组日志
log.group('功能模块');
log.info('执行步骤1');
log.info('执行步骤2');
log.groupEnd();

// 性能计时
log.time('操作耗时');
// ... 执行操作
log.timeEnd('操作耗时');
```

### 环境控制
- **开发环境**：显示所有日志（仅localhost环境）
- **生产环境**：**完全屏蔽所有日志输出**，零性能影响
- **多重保护**：环境变量 + 域名检测 + 强制禁用机制

### 部署安全
项目采用多重机制确保生产环境日志安全：

1. **环境变量检测**：`import.meta.env.PROD` 自动屏蔽
2. **域名检测**：非localhost域名自动屏蔽
3. **强制禁用**：`logger.forceDisable()` 方法
4. **配置保护**：生产环境下配置修改无效

### 迁移完成 ✅
项目中的所有 `console.log`、`console.error` 等调用已完全替换为统一日志管理器：
- ✅ **生产环境无日志泄露**
- ✅ **开发环境完整调试信息**  
- ✅ **统一日志格式**
- ✅ **性能零影响**

---

如需详细接口参数、错误码说明或有其它文档需求，请随时补充！
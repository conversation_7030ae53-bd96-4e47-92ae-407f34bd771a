<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>自动填充助手 - 设置</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      width: 100%;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      color: #333;
    }
    .container {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #eee;
      padding-bottom: 15px;
      margin-bottom: 15px;
    }
    .header h1 {
      font-size: 24px;
      margin: 0;
    }
    .section {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    .section-title {
      font-size: 18px;
      margin-top: 0;
      margin-bottom: 15px;
      color: #1a73e8;
    }
    .setting-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
    }
    .setting-row:last-child {
      border-bottom: none;
    }
    .setting-label {
      font-weight: 500;
    }
    .setting-control {
      display: flex;
      align-items: center;
    }
    .button {
      background-color: #1a73e8;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
    }
    .button:hover {
      background-color: #1558c9;
    }
    .button.secondary {
      background-color: #f8f9fa;
      color: #1a73e8;
      border: 1px solid #dadce0;
    }
    .button.secondary:hover {
      background-color: #f1f3f4;
    }
    .subscription-status {
      padding: 10px 15px;
      border-radius: 4px;
      margin-bottom: 10px;
      font-weight: 500;
    }
    .subscription-active {
      background-color: #e8f5e9;
      color: #2e7d32;
    }
    .subscription-inactive {
      background-color: #ffebee;
      color: #c62828;
    }
    .subscription-warning {
      background-color: #fff3e0;
      color: #e65100;
    }
    select {
      padding: 8px 12px;
      border-radius: 4px;
      border: 1px solid #dadce0;
      background-color: #fff;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1 id="title">自动填充助手 - 设置</h1>
      <div class="setting-control">
        <select id="language-selector">
          <option value="zh">中文</option>
          <option value="en">English</option>
        </select>
      </div>
    </div>
    
    <div class="section">
      <h2 class="section-title" id="generalSettings">常规设置</h2>
      <div class="setting-row">
        <div class="setting-label" id="enableAutoFill">启用自动填充</div>
        <div class="setting-control">
          <input type="checkbox" id="auto-fill-toggle" checked>
        </div>
      </div>
      <div class="setting-row">
        <div class="setting-label" id="notificationSetting">通知设置</div>
        <div class="setting-control">
          <select id="notification-setting">
            <option value="all">所有通知</option>
            <option value="important">仅重要通知</option>
            <option value="none">关闭通知</option>
          </select>
        </div>
      </div>
    </div>
    
    <div class="section">
      <h2 class="section-title" id="subscriptionStatus">订阅状态</h2>
      <div id="subscription-container">
        <div id="no-subscription" class="subscription-status subscription-inactive">
          <span id="noActiveSubscription">暂无有效订阅</span>
        </div>
        <button id="subscribe-button" class="button">
          <span id="subscribePlan">订阅套餐</span>
        </button>
      </div>
    </div>
    
    <div class="section">
      <h2 class="section-title" id="accountInfo">账号信息</h2>
      <div class="setting-row">
        <div class="setting-label" id="emailLabel">邮箱</div>
        <div class="setting-control" id="email-value">-</div>
      </div>
      <div class="setting-row">
        <div class="setting-label" id="userIdLabel">用户ID</div>
        <div class="setting-control" id="user-id-value">-</div>
      </div>
      <div class="setting-row">
        <button id="logout-button" class="button secondary" id="logoutButton">退出登录</button>
      </div>
    </div>
  </div>
  
  <script src="options.js"></script>
</body>
</html> 
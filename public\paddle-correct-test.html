<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle正确配置测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .log { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ Paddle正确配置测试</h1>
        
        <div class="section success">
            <h2>🎯 问题解决方案</h2>
            <p><strong>发现的问题：</strong></p>
            <ul>
                <li>❌ Client-side token不能直接调用API (CORS限制)</li>
                <li>❌ Paddle SDK初始化参数错误</li>
            </ul>
            <p><strong>正确的做法：</strong></p>
            <ul>
                <li>✅ Client-side token只用于前端SDK</li>
                <li>✅ 使用正确的SDK初始化方式</li>
                <li>✅ 不需要直接调用API来验证产品</li>
            </ul>
        </div>

        <div class="section info">
            <h2>📋 正确配置</h2>
            <pre id="config-display"></pre>
        </div>

        <div class="section">
            <h2>🧪 正确的测试方法</h2>
            <button onclick="testCorrectSDKInit()">1. 正确初始化Paddle SDK</button>
            <button onclick="testCheckoutWithCorrectConfig()">2. 测试支付页面</button>
            <button onclick="testDifferentProducts()">3. 测试不同产品ID</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section warning">
            <h2>💡 重要说明</h2>
            <p><strong>Client-side token的正确用法：</strong></p>
            <ul>
                <li>✅ 只用于前端Paddle SDK初始化</li>
                <li>✅ 用于打开支付页面</li>
                <li>❌ 不能用于直接API调用</li>
                <li>❌ 不能用于服务端验证</li>
            </ul>
            <p><strong>如果需要API访问：</strong> 需要使用API Key在服务端进行</p>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'test_459728092d35d36bc173c60e52d',
            productId: 'pri_01k0xttm1f9wekbg957y66xawj',
            // 备用产品ID进行测试
            alternativeProducts: [
                'pri_01jxa2azwznm06dqbptzagtm0z', // 原来的产品ID
                'pri_01k0xttm1f9wekbg957y66xawj'  // 新的产品ID
            ]
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        function showConfig() {
            document.getElementById('config-display').textContent = JSON.stringify(config, null, 2);
        }

        // 1. 正确初始化Paddle SDK
        function testCorrectSDKInit() {
            log('🚀 测试1: 正确初始化Paddle SDK...', 'info');
            
            try {
                // 正确的初始化方式 - 不使用environment参数
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            log(`❌ Checkout错误: ${data.detail}`, 'error');
                            
                            if (data.detail && data.detail.includes('Failed to retrieve JWT')) {
                                log('🔍 JWT错误分析: 可能是产品配置问题', 'warning');
                            }
                        } else if (data.type === 'checkout.loaded') {
                            log('✅ Checkout页面加载成功', 'success');
                        } else if (data.name === 'checkout.completed') {
                            log('🎉 支付完成', 'success');
                        } else if (data.name === 'checkout.closed') {
                            log('📝 支付页面关闭', 'info');
                        }
                    }
                });

                log('✅ Paddle SDK初始化成功', 'success');
                log('💡 SDK已准备就绪，可以测试支付功能', 'info');
                
            } catch (error) {
                log(`❌ SDK初始化失败: ${error.message}`, 'error');
            }
        }

        // 2. 测试支付页面
        function testCheckoutWithCorrectConfig() {
            log('💳 测试2: 使用正确配置测试支付...', 'info');
            
            if (!window.Paddle) {
                log('❌ Paddle SDK未加载，请先运行测试1', 'error');
                return;
            }

            try {
                log(`🛍️ 使用产品ID: ${config.productId}`, 'info');
                
                window.Paddle.Checkout.open({
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }],
                    customer: {
                        email: '<EMAIL>'
                    },
                    settings: {
                        displayMode: 'overlay',
                        theme: 'light',
                        locale: 'en'
                    }
                });
                
                log('✅ 支付页面打开请求已发送', 'success');
                log('💡 请观察是否出现支付弹窗', 'info');
                
            } catch (error) {
                log(`❌ 支付页面打开失败: ${error.message}`, 'error');
            }
        }

        // 3. 测试不同产品ID
        function testDifferentProducts() {
            log('🔄 测试3: 测试不同产品ID...', 'info');
            
            if (!window.Paddle) {
                log('❌ Paddle SDK未加载，请先运行测试1', 'error');
                return;
            }

            config.alternativeProducts.forEach((productId, index) => {
                setTimeout(() => {
                    log(`🛍️ 测试产品 ${index + 1}: ${productId}`, 'info');
                    
                    try {
                        window.Paddle.Checkout.open({
                            items: [{
                                priceId: productId,
                                quantity: 1
                            }],
                            customer: {
                                email: '<EMAIL>'
                            },
                            settings: {
                                displayMode: 'overlay',
                                theme: 'light'
                            }
                        });
                        
                        log(`✅ 产品 ${index + 1} 支付页面请求已发送`, 'success');
                        
                    } catch (error) {
                        log(`❌ 产品 ${index + 1} 测试失败: ${error.message}`, 'error');
                    }
                }, index * 3000); // 每3秒测试一个产品
            });
        }

        // 监听Paddle加载完成
        window.addEventListener('DOMContentLoaded', function() {
            // 等待Paddle SDK加载
            const checkPaddle = setInterval(() => {
                if (window.Paddle) {
                    clearInterval(checkPaddle);
                    log('📦 Paddle SDK已加载完成', 'success');
                }
            }, 100);
        });

        // 页面加载时显示配置
        window.onload = function() {
            showConfig();
            log('🎯 Paddle正确配置测试工具已加载', 'info');
            log('💡 这次使用正确的方法测试Paddle集成', 'info');
            log('🔧 不再尝试直接调用API，只测试前端SDK功能', 'info');
        };
    </script>
</body>
</html>

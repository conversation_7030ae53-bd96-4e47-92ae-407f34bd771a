/**
 * 统一的Token管理器
 * 处理token的获取、解密和验证
 */

import { getAccessToken as getAccessTokenFromAuth } from './authService';
import { log } from '../utils/logger';

/**
 * 安全获取并解密访问令牌
 * 确保返回的是解密后的token，可以直接用于API调用
 */
export async function getDecryptedAccessToken(): Promise<string | null> {
  try {
    log.debug('开始获取访问令牌...');
    
    // 通过authService获取token（应该已经解密）
    const token = await getAccessTokenFromAuth();
    
    if (!token) {
      log.warn('未获取到访问令牌');
      return null;
    }
    
    log.debug(`获取到token，长度: ${token.length}`);
    log.debug(`Token前10字符: ${token.substring(0, 10)}`);
    
    // 检查token是否仍然是加密状态
    if (token.startsWith('ENCRYPTED:')) {
      log.error('Token仍然是加密状态，解密失败！');
      log.error(`原始token: ${token.substring(0, 50)}...`);
      
      // 尝试手动解密（作为备用方案）
      try {
        const manuallyDecrypted = await attemptManualDecryption(token);
        if (manuallyDecrypted && !manuallyDecrypted.startsWith('ENCRYPTED:')) {
          log.info('手动解密成功');
          return manuallyDecrypted;
        }
      } catch (decryptError) {
        log.error('手动解密也失败', decryptError);
      }
      
      return null;
    }
    
    // 验证token格式
    if (isValidTokenFormat(token)) {
      log.debug('Token格式验证通过');
      return token;
    } else {
      log.error('Token格式无效');
      return null;
    }
    
  } catch (error) {
    log.error('获取访问令牌时出错', error);
    return null;
  }
}

/**
 * 尝试手动解密token（备用方案）
 */
async function attemptManualDecryption(encryptedToken: string): Promise<string | null> {
  try {
    log.debug('尝试手动解密token...');
    
    // 动态导入存储适配器
    const { getStorageItem } = await import('./storageAdapter');
    
    // 直接通过存储适配器获取（强制解密）
    const decryptedToken = await getStorageItem<string>('access_token');
    
    if (decryptedToken && !decryptedToken.startsWith('ENCRYPTED:')) {
      log.info('通过存储适配器成功解密');
      return decryptedToken;
    }
    
    // 如果存储适配器也失败了，尝试其他方法
    log.warn('存储适配器解密也失败，尝试其他方法...');
    
    // 可以在这里添加其他解密尝试
    // 比如直接调用解密函数等
    
    return null;
  } catch (error) {
    log.error('手动解密失败', error);
    return null;
  }
}

/**
 * 验证token格式是否有效
 */
function isValidTokenFormat(token: string): boolean {
  if (!token || typeof token !== 'string') {
    return false;
  }
  
  // 检查是否仍然是加密状态
  if (token.startsWith('ENCRYPTED:')) {
    return false;
  }
  
  // 检查是否是JWT格式（可选）
  if (token.includes('.')) {
    const parts = token.split('.');
    if (parts.length === 3) {
      try {
        // 尝试解析JWT header以验证格式
        atob(parts[0]);
        log.debug('Token看起来是有效的JWT格式');
        return true;
      } catch (e) {
        log.debug('Token不是标准JWT格式，但可能仍然有效');
        return true; // 即使不是JWT也可能是有效的token
      }
    }
  }
  
  // 其他格式的token也可能有效
  return token.length > 10; // 简单的长度检查
}

/**
 * 创建包含认证头的请求选项
 */
export async function createAuthenticatedRequestOptions(options: RequestInit = {}): Promise<RequestInit | null> {
  const token = await getDecryptedAccessToken();
  
  if (!token) {
    log.error('无法获取有效的访问令牌');
    return null;
  }
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    ...(options.headers as Record<string, string> || {})
  };
  
  return {
    mode: 'cors' as RequestMode,
    credentials: 'omit' as RequestCredentials,
    ...options,
    headers
  };
}

/**
 * 执行需要认证的API请求
 */
export async function authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
  const requestOptions = await createAuthenticatedRequestOptions(options);
  
  if (!requestOptions) {
    throw new Error('无法创建认证请求：缺少有效的访问令牌');
  }
  
  log.debug(`发送认证请求到: ${url}`);
  const headers = requestOptions.headers as Record<string, string>;
  log.debug(`请求头包含Authorization: ${!!headers?.['Authorization']}`);
  
  return fetch(url, requestOptions);
}
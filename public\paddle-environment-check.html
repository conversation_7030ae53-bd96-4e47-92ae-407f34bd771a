<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle环境诊断</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        .critical { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .checklist { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .env-info { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .token-analysis { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Paddle环境诊断</h1>
        
        <div class="section critical">
            <h2>🚨 发现的问题</h2>
            <p><strong>新产品也出现JWT错误！</strong></p>
            <ul>
                <li>❌ 旧产品: JWT错误</li>
                <li>❌ 新产品: JWT错误</li>
                <li>❌ 所有产品都无法获取数据</li>
            </ul>
            <p><strong>这说明问题不是产品配置，而是环境或Token问题！</strong></p>
        </div>

        <div class="section warning">
            <h2>🎯 需要检查的环境配置</h2>
            
            <div class="env-info">
                <h3>📋 当前配置</h3>
                <p><strong>Token:</strong> <code>test_459728092d35d36bc173c60e52d</code></p>
                <p><strong>环境:</strong> Sandbox</p>
                <p><strong>旧产品ID:</strong> <code>pri_01k0xttmf9wekbg957y66xawj</code></p>
                <p><strong>新产品ID:</strong> <code>pri_01k1ae8ecd1yj4njgwec3vvs13</code></p>
            </div>

            <div class="token-analysis">
                <h3>🔍 Token分析</h3>
                <p><strong>Token格式:</strong> test_459728092d35d36bc173c60e52d</p>
                <ul>
                    <li>✅ 以 "test_" 开头 - 正确的Sandbox格式</li>
                    <li>✅ 长度27字符 - 符合要求</li>
                    <li>❓ 但仍然出现JWT错误</li>
                </ul>
            </div>
        </div>

        <div class="section error">
            <h2>🔧 必须检查的配置</h2>
            
            <div class="checklist">
                <h3>1. 🌐 环境匹配检查</h3>
                <ul>
                    <li><strong>Token环境:</strong> 确认Token是从Sandbox Dashboard获取的</li>
                    <li><strong>产品环境:</strong> 确认两个产品都是在Sandbox环境创建的</li>
                    <li><strong>Dashboard地址:</strong> 确认使用的是 <a href="https://sandbox-vendors.paddle.com/" target="_blank">https://sandbox-vendors.paddle.com/</a></li>
                </ul>
            </div>

            <div class="checklist">
                <h3>2. 🔑 Token权限检查</h3>
                <ul>
                    <li><strong>Token类型:</strong> 确认是Client-side Token（不是Server-side）</li>
                    <li><strong>Token状态:</strong> 确认Token是Active状态</li>
                    <li><strong>Token权限:</strong> 确认Token有Checkout权限</li>
                </ul>
            </div>

            <div class="checklist">
                <h3>3. 👤 账户验证检查</h3>
                <ul>
                    <li><strong>账户状态:</strong> 确认Paddle账户完全验证</li>
                    <li><strong>支付方式:</strong> 确认账户绑定了有效的支付方式</li>
                    <li><strong>KYC验证:</strong> 确认完成了身份验证</li>
                </ul>
            </div>
        </div>

        <div class="section info">
            <h2>🔍 诊断步骤</h2>
            <ol>
                <li><strong>重新生成Token:</strong>
                    <ul>
                        <li>登录 <a href="https://sandbox-vendors.paddle.com/" target="_blank">Paddle Sandbox Dashboard</a></li>
                        <li>进入 Developer tools > Authentication</li>
                        <li>删除现有的Client-side Token</li>
                        <li>创建新的Client-side Token</li>
                        <li>复制新Token并更新配置</li>
                    </ul>
                </li>
                
                <li><strong>验证产品环境:</strong>
                    <ul>
                        <li>在Sandbox Dashboard中查看产品列表</li>
                        <li>确认两个产品ID都存在</li>
                        <li>确认产品状态都是Active</li>
                    </ul>
                </li>
                
                <li><strong>测试最简配置:</strong>
                    <ul>
                        <li>使用新Token测试</li>
                        <li>如果仍有问题，联系Paddle支持</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="section warning">
            <h2>💡 可能的解决方案</h2>
            
            <h3>方案1: 重新生成Token</h3>
            <p>最可能的问题是Token权限或状态问题。重新生成一个新的Client-side Token。</p>
            
            <h3>方案2: 检查账户状态</h3>
            <p>确认Paddle账户完全验证，包括身份验证和支付方式。</p>
            
            <h3>方案3: 联系Paddle支持</h3>
            <p>如果以上都正确，可能是Paddle系统问题，需要联系技术支持。</p>
        </div>

        <div class="section success">
            <h2>✅ 下一步行动</h2>
            <ol>
                <li><strong>立即重新生成Token</strong> - 这是最可能的解决方案</li>
                <li><strong>更新 .env.local 中的Token</strong></li>
                <li><strong>重新测试</strong></li>
                <li><strong>如果仍有问题，提供详细信息给Paddle支持</strong></li>
            </ol>
        </div>

        <div class="section info">
            <h2>📞 Paddle支持信息</h2>
            <p>如果需要联系Paddle支持，请提供：</p>
            <ul>
                <li>账户ID/Vendor ID</li>
                <li>Token前缀: test_459728092d35d36bc173c60e52d</li>
                <li>产品ID: pri_01k1ae8ecd1yj4njgwec3vvs13</li>
                <li>错误信息: "Failed to retrieve JWT"</li>
                <li>环境: Sandbox</li>
            </ul>
        </div>
    </div>

    <script>
        // 页面加载时显示重要信息
        window.onload = function() {
            console.log('🔧 Paddle环境诊断工具已加载');
            console.log('🚨 发现JWT错误影响所有产品');
            console.log('💡 最可能的解决方案：重新生成Client-side Token');
            
            // 高亮显示关键信息
            const criticalSection = document.querySelector('.critical');
            if (criticalSection) {
                criticalSection.style.animation = 'pulse 2s infinite';
            }
        };

        // 添加脉冲动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.8; }
                100% { opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

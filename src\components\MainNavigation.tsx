import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from './LanguageSwitcher';

const MainNavigation: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { currentUser } = useAuth();
  const location = useLocation();
  const { t } = useTranslation();

  // 关闭导航菜单（在移动设备上）
  const closeMenu = () => setIsMenuOpen(false);

  // 检查当前路径是否是活跃的导航链接
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  // 当路由变化时关闭菜单
  useEffect(() => {
    closeMenu();
  }, [location.pathname]);

  return (
    <header className="bg-white dark:bg-gray-900 shadow-sm sticky top-0 z-30">
      <div className="mx-auto max-w-5xl px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo on the left */}
          <Link to="/" className="flex items-center space-x-2">
            <span className="text-xl font-bold text-teal-500">AF</span>
            <span className="text-xl font-bold text-gray-800 dark:text-white">AutoFill</span>
          </Link>

          {/* Group navigation and language switcher together on the right */}
          <div className="hidden md:flex md:items-center space-x-8">
            <NavLink to="/" isActive={isActive('/')}>{t('nav.home')}</NavLink>
            <NavLink to="/features" isActive={isActive('/features')}>{t('nav.features')}</NavLink>
            <NavLink to="/pricing" isActive={isActive('/pricing')}>{t('nav.pricing')}</NavLink>

            {/* User Authentication Links */}
            {currentUser ? (
              <NavLink to="/profile" isActive={isActive('/profile')}>{t('nav.profile')}</NavLink>
            ) : (
              <NavLink to="/login" isActive={isActive('/login')}>{t('common.login')}</NavLink>
            )}

            {/* Language Switcher immediately after navigation */}
            <div className="pl-2">
              <LanguageSwitcher />
            </div>
          </div>

          {/* Mobile menu button */}
          <button
            type="button"
            className="inline-flex items-center justify-center rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-teal-500 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-white md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <span className="sr-only">Open main menu</span>
            {isMenuOpen ? (
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
          <div className="space-y-1 px-4 py-3">
            <MobileNavLink to="/" isActive={isActive('/')}>{t('nav.home')}</MobileNavLink>
            <MobileNavLink to="/features" isActive={isActive('/features')}>{t('nav.features')}</MobileNavLink>
            <MobileNavLink to="/pricing" isActive={isActive('/pricing')}>{t('nav.pricing')}</MobileNavLink>

            {/* User Authentication Links */}
            {currentUser ? (
              <MobileNavLink to="/profile" isActive={isActive('/profile')}>{t('nav.profile')}</MobileNavLink>
            ) : (
              <MobileNavLink to="/login" isActive={isActive('/login')}>{t('common.login')}</MobileNavLink>
            )}

            {/* Language Switcher in mobile menu */}
            <div className="pt-3 pb-1">
              <div className="px-3 text-sm font-medium text-gray-500 dark:text-gray-400">
                Language / 语言
              </div>
              <div className="mt-2 px-3">
                <LanguageSwitcher />
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

// Desktop navigation link component
const NavLink: React.FC<{
  to: string;
  isActive: boolean;
  children: React.ReactNode;
}> = ({ to, isActive, children }) => (
  <Link
    to={to}
    className={`text-sm font-medium ${
      isActive
        ? 'text-teal-600 dark:text-teal-400'
        : 'text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white'
    }`}
  >
    {children}
  </Link>
);

// Mobile navigation link component
const MobileNavLink: React.FC<{
  to: string;
  isActive: boolean;
  children: React.ReactNode;
}> = ({ to, isActive, children }) => (
  <Link
    to={to}
    className={`block px-3 py-2 rounded-md text-base font-medium ${
      isActive
        ? 'bg-teal-50 text-teal-600 dark:bg-teal-900/30 dark:text-teal-400'
        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-white'
    }`}
  >
    {children}
  </Link>
);

export default MainNavigation; 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付成功 - AutoFill</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .success-container {
            background: white;
            border-radius: 16px;
            padding: 48px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
            position: relative;
            overflow: hidden;
        }
        
        .success-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4CAF50, #45a049);
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 24px;
            background: #4CAF50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: successPulse 2s ease-in-out infinite;
        }
        
        .success-icon svg {
            width: 40px;
            height: 40px;
            stroke: white;
            stroke-width: 3;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
        }
        
        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .success-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 16px;
        }
        
        .success-message {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 32px;
            line-height: 1.6;
        }
        
        .countdown-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            border: 1px solid #e9ecef;
        }
        
        .countdown-text {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 12px;
        }
        
        .progress-container {
            background: #e9ecef;
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 12px;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 1s linear;
        }
        
        .countdown-timer {
            font-size: 24px;
            font-weight: 600;
            color: #4CAF50;
            font-family: 'Courier New', monospace;
        }
        
        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 16px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 140px;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        .loading-dots {
            display: inline-block;
        }
        
        .loading-dots::after {
            content: '';
            animation: loadingDots 1.5s steps(4, end) infinite;
        }
        
        @keyframes loadingDots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
        
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            font-family: monospace;
            max-width: 300px;
            opacity: 0.7;
            z-index: 1000;
        }
        
        @media (max-width: 480px) {
            .success-container {
                padding: 32px 24px;
                margin: 16px;
            }
            
            .success-title {
                font-size: 24px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
            
            .debug-info {
                position: static;
                margin-top: 20px;
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">
            <svg viewBox="0 0 24 24">
                <polyline points="20,6 9,17 4,12"></polyline>
            </svg>
        </div>
        
        <h1 class="success-title">🎉 支付成功！</h1>
        <p class="success-message">
            感谢您的订阅！您的AutoFill Premium账户已激活。<br>
            我们正在为您准备个人中心页面<span class="loading-dots"></span>
        </p>
        
        <div class="countdown-container">
            <div class="countdown-text" id="countdownText">
                支付成功！<span id="countdown">5</span>秒后自动跳转到个人中心...
            </div>
            <div class="progress-container">
                <div class="progress-bar" id="progressBar"></div>
            </div>
        </div>
        
        <div class="action-buttons">
            <button class="btn btn-primary" id="redirectBtn">
                立即前往个人中心
            </button>
            <button class="btn btn-secondary" id="closeBtn">
                关闭窗口
            </button>
        </div>
    </div>

    <!-- Debug信息 -->
    <div class="debug-info" id="debugInfo" style="display: none;">
        <div>环境: <span id="envInfo">检测中...</span></div>
        <div>状态: <span id="statusInfo">初始化中...</span></div>
        <div>消息: <span id="messageInfo">-</span></div>
    </div>

    <script>
        // 启用调试模式
        const DEBUG = true;
        
        // 倒计时变量
        let countdown = 5;
        let countdownInterval;
        let redirected = false;
        let notificationSent = false;
        
        // DOM元素
        const countdownEl = document.getElementById('countdown');
        const countdownTextEl = document.getElementById('countdownText');
        const progressBarEl = document.getElementById('progressBar');
        const redirectBtn = document.getElementById('redirectBtn');
        const closeBtn = document.getElementById('closeBtn');
        const debugInfo = document.getElementById('debugInfo');
        const envInfo = document.getElementById('envInfo');
        const statusInfo = document.getElementById('statusInfo');
        const messageInfo = document.getElementById('messageInfo');
        
        // 调试日志函数
        function debugLog(message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] 🎉 ${message}`, data || '');
            
            if (DEBUG && messageInfo) {
                messageInfo.textContent = `${timestamp}: ${message}`;
            }
        }
        
        // 更新状态
        function updateStatus(status) {
            debugLog(`状态更新: ${status}`);
            if (DEBUG && statusInfo) {
                statusInfo.textContent = status;
            }
        }
        
        // 检测环境
        function detectEnvironment() {
            const env = {
                isInOverlay: window.parent !== window,
                isInFrame: window !== window.top,
                hasParent: window.parent !== window,
                origin: window.location.origin,
                userAgent: navigator.userAgent.substring(0, 50) + '...',
                url: window.location.href
            };
            
            debugLog('环境检测完成', env);
            
            if (DEBUG && envInfo) {
                envInfo.textContent = env.isInOverlay ? 'Overlay/Frame' : 'Direct';
                debugInfo.style.display = 'block';
            }
            
            return env;
        }
        
        // 发送支付成功通知
        function sendPaymentSuccessNotification(source = 'unknown') {
            if (notificationSent) {
                debugLog('通知已发送，跳过重复发送');
                return;
            }
            
            const message = {
                type: 'paddle_payment_success',
                source: source,
                timestamp: Date.now(),
                url: window.location.href
            };
            
            try {
                // 发送到父窗口
                if (window.parent && window.parent !== window) {
                    debugLog('📤 发送消息到父窗口', message);
                    window.parent.postMessage(message, '*');
                    
                    // 也发送到顶层窗口（防止多层嵌套）
                    if (window.top && window.top !== window.parent) {
                        window.top.postMessage(message, '*');
                    }
                }
                
                // 发送到当前窗口
                window.postMessage(message, '*');
                
                // 发送自定义事件
                const customEvent = new CustomEvent('paddle_payment_success', {
                    detail: message
                });
                window.dispatchEvent(customEvent);
                
                if (window.parent && window.parent !== window) {
                    window.parent.dispatchEvent(customEvent);
                }
                
                notificationSent = true;
                debugLog('✅ 支付成功通知发送完成');
                
            } catch (error) {
                debugLog('❌ 发送通知失败', error);
            }
        }
        
        // 开始倒计时
        function startCountdown() {
            const totalTime = countdown;
            
            function updateCountdown() {
                countdownEl.textContent = countdown;
                const progressPercent = ((totalTime - countdown) / totalTime) * 100;
                progressBarEl.style.width = progressPercent + '%';
                
                updateStatus(`倒计时: ${countdown}秒`);
                
                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    redirectToProfile();
                } else {
                    countdown--;
                }
            }
            
            // 立即执行一次
            updateCountdown();
            
            // 每秒更新
            countdownInterval = setInterval(updateCountdown, 1000);
            
            debugLog('倒计时开始', { totalTime, countdown });
        }
        
        // 跳转到个人中心
        function redirectToProfile() {
            if (redirected) {
                debugLog('已经跳转过，跳过');
                return;
            }
            
            redirected = true;
            updateStatus('正在跳转...');
            
            // 清除倒计时
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
            
            // 发送跳转前通知
            sendPaymentSuccessNotification('redirect_to_profile');
            
            const profileUrl = `${window.location.origin}/profile?paddle_payment_success=true&t=${Date.now()}`;
            debugLog('🚀 开始跳转', { url: profileUrl });
            
            // 延迟确保消息发送完成
            setTimeout(() => {
                try {
                    if (window.parent && window.parent !== window) {
                        debugLog('📱 通过父窗口跳转');
                        window.parent.location.href = profileUrl;
                    } else {
                        debugLog('🖥️ 当前窗口直接跳转');
                        window.location.href = profileUrl;
                    }
                } catch (error) {
                    debugLog('❌ 跳转失败，使用备用方案', error);
                    window.location.href = `${window.location.origin}/profile?paddle_payment_success=true&fallback=true`;
                }
            }, 800);
        }
        
        // 关闭窗口
        function closeWindow() {
            updateStatus('正在关闭...');
            debugLog('🔒 尝试关闭窗口');
            
            // 发送关闭前通知
            sendPaymentSuccessNotification('close_window');
            
            setTimeout(() => {
                try {
                    // 通知父窗口关闭overlay
                    if (window.parent && window.parent !== window) {
                        window.parent.postMessage({ type: 'close_success_window' }, '*');
                    }
                    
                    // 尝试关闭当前窗口
                    window.close();
                    
                    // 如果无法关闭，跳转到个人中心
                    setTimeout(() => {
                        if (!document.hidden) {
                            debugLog('🏠 无法关闭窗口，跳转到个人中心');
                            redirectToProfile();
                        }
                    }, 500);
                    
                } catch (error) {
                    debugLog('❌ 关闭窗口失败', error);
                    redirectToProfile();
                }
            }, 500);
        }
        
        // 页面初始化
        function initializePage() {
            debugLog('🎉 Paddle支付成功页面开始初始化');
            updateStatus('初始化中...');
            
            // 检测环境
            const env = detectEnvironment();
            
            // 根据环境调整倒计时
            if (env.isInOverlay || env.isInFrame) {
                countdown = 3;
                countdownTextEl.innerHTML = '支付成功！<span id="countdown">3</span>秒后自动跳转到个人中心...';
                debugLog('🔍 检测到在overlay/frame中，缩短倒计时');
            }
            
            // 立即发送页面加载通知
            sendPaymentSuccessNotification('page_loaded');
            
            // 开始倒计时
            updateStatus('倒计时开始');
            startCountdown();
            
            debugLog('✅ 页面初始化完成');
        }
        
        // 绑定事件
        redirectBtn.addEventListener('click', redirectToProfile);
        closeBtn.addEventListener('click', closeWindow);
        
        // 键盘事件
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                redirectToProfile();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                closeWindow();
            }
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
        
        // 窗口加载完成后再次通知
        window.addEventListener('load', function() {
            sendPaymentSuccessNotification('window_loaded');
        });
        
        // 监听来自父窗口的消息
        window.addEventListener('message', function(event) {
            debugLog('收到消息', event.data);
            
            if (event.data && event.data.type === 'close_success_window') {
                debugLog('收到关闭窗口指令');
                closeWindow();
            }
        });
        
        // 页面即将卸载时的最后通知
        window.addEventListener('beforeunload', function() {
            sendPaymentSuccessNotification('page_unload');
        });
    </script>
</body>
</html>
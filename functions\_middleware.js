// 全局中间件处理 CORS 和请求处理
export async function onRequest(context) {
  const { request, next, env } = context;
  
  // 处理 OPTIONS 请求 (CORS 预检)
  if (request.method === "OPTIONS") {
    return new Response(null, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization, x-api-key",
        "Access-Control-Max-Age": "86400",
      }
    });
  }
  
  // 处理实际请求
  const response = await next();
  
  // 添加 CORS 头到响应
  const newResponse = new Response(response.body, response);
  newResponse.headers.set("Access-Control-Allow-Origin", "*");
  
  return newResponse;
} 
/**
 * 认证 API 代理函数
 * 处理所有 /api/auth/* 路径的请求，并转发到实际的认证 API
 */
export async function onRequest(context) {
  // 从环境变量获取 API Key 和基础 URL
  const apiKey = context.env.API_KEY || "";
  const apiBaseUrl = context.env.API_BASE_URL || "https://autoapi.utilityapp.site";
  
  // 获取请求信息
  const { request } = context;
  const url = new URL(request.url);
  
  // 提取请求路径，移除 /api/auth 前缀
  const path = url.pathname.replace('/api/auth', '');
  
  // 构建目标 URL
  const targetUrl = `${apiBaseUrl}/auth${path}`;
  console.log(`转发认证请求到: ${targetUrl}`);
  
  try {
    // 克隆原始请求
    const requestInit = {
      method: request.method,
      headers: new Headers(request.headers),
    };
    
    // 如果是 POST/PUT 请求，需要包含请求体
    if (["POST", "PUT", "PATCH"].includes(request.method)) {
      const contentType = request.headers.get("Content-Type");
      if (contentType && contentType.includes("application/json")) {
        // 克隆 JSON 请求体
        const body = await request.json();
        requestInit.body = JSON.stringify(body);
        requestInit.headers.set("Content-Type", "application/json");
      } else {
        // 其他类型请求体直接传递
        requestInit.body = await request.arrayBuffer();
      }
    }
    
    // 设置 API Key
    if (apiKey) {
      requestInit.headers.set("x-api-key", apiKey);
    }
    
    // 移除可能导致问题的头信息
    requestInit.headers.delete("host");
    
    // 发送请求到实际 API
    const response = await fetch(targetUrl, requestInit);
    
    // 构建响应
    const responseInit = {
      status: response.status,
      statusText: response.statusText,
      headers: new Headers(response.headers)
    };
    
    // 获取响应体
    const responseBody = await response.arrayBuffer();
    
    // 返回响应
    return new Response(responseBody, responseInit);
  } catch (error) {
    console.error(`处理认证请求出错: ${error.message}`);
    return new Response(JSON.stringify({ 
      error: "中间层处理请求失败", 
      message: error.message 
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json"
      }
    });
  }
} 
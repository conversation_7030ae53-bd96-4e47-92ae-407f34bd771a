<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全存储测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .warning {
            color: #ffc107;
        }
        .info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 安全存储测试页面</h1>
        <p>此页面用于测试新的分层加密存储功能是否正常工作。</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>1. 存储测试数据</h3>
            <button onclick="storeTestData()">存储测试数据</button>
            <button onclick="clearTestData()">清除测试数据</button>
            <div id="storeResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 查看原始存储内容</h3>
            <button onclick="viewRawStorage()">查看localStorage原始内容</button>
            <div id="rawResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 读取解密后的数据</h3>
            <button onclick="readDecryptedData()">读取解密数据</button>
            <div id="decryptedResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 插件存储同步测试</h3>
            <button onclick="testExtensionSync()">测试插件同步</button>
            <div id="syncResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. 数据迁移测试</h3>
            <button onclick="testMigration()">测试数据迁移</button>
            <div id="migrationResult" class="result"></div>
        </div>
    </div>

    <script>
        // 测试数据
        const testData = {
            access_token: 'test_access_token_12345',
            refresh_token: 'test_refresh_token_67890',
            user_id: 'user_123456',
            user_email: '<EMAIL>',
            subscription_info: JSON.stringify({
                plan: 'premium',
                expires_at: '2024-12-31'
            })
        };

        // 存储测试数据
        async function storeTestData() {
            const resultDiv = document.getElementById('storeResult');
            resultDiv.innerHTML = '正在存储测试数据...';
            
            try {
                // 模拟使用存储适配器
                for (const [key, value] of Object.entries(testData)) {
                    localStorage.setItem(key, value);
                }
                
                resultDiv.innerHTML = '<span class="success">✅ 测试数据存储成功！</span>\n' + 
                    JSON.stringify(testData, null, 2);
            } catch (error) {
                resultDiv.innerHTML = '<span class="error">❌ 存储失败: ' + error.message + '</span>';
            }
        }

        // 清除测试数据
        function clearTestData() {
            const resultDiv = document.getElementById('storeResult');
            
            try {
                Object.keys(testData).forEach(key => {
                    localStorage.removeItem(key);
                });
                
                resultDiv.innerHTML = '<span class="success">✅ 测试数据已清除</span>';
            } catch (error) {
                resultDiv.innerHTML = '<span class="error">❌ 清除失败: ' + error.message + '</span>';
            }
        }

        // 查看原始存储内容
        function viewRawStorage() {
            const resultDiv = document.getElementById('rawResult');
            const rawData = {};
            
            Object.keys(testData).forEach(key => {
                const value = localStorage.getItem(key);
                rawData[key] = value;
            });
            
            resultDiv.innerHTML = '<span class="info">📋 localStorage原始内容:</span>\n' + 
                JSON.stringify(rawData, null, 2);
        }

        // 读取解密后的数据
        function readDecryptedData() {
            const resultDiv = document.getElementById('decryptedResult');
            resultDiv.innerHTML = '<span class="warning">⚠️ 此功能需要在实际应用中测试，因为需要导入存储适配器模块</span>';
        }

        // 测试插件同步
        function testExtensionSync() {
            const resultDiv = document.getElementById('syncResult');
            
            if (typeof chrome !== 'undefined' && chrome.storage) {
                resultDiv.innerHTML = '<span class="success">✅ 检测到浏览器插件环境</span>\n' +
                    '可以进行插件存储同步测试';
            } else {
                resultDiv.innerHTML = '<span class="warning">⚠️ 未检测到浏览器插件环境</span>\n' +
                    '请在安装了插件的环境中测试';
            }
        }

        // 测试数据迁移
        function testMigration() {
            const resultDiv = document.getElementById('migrationResult');
            
            // 先存储一些明文数据（模拟旧版本数据）
            const oldData = {
                access_token: 'old_plain_token_123',
                user_id: 'old_user_456'
            };
            
            Object.entries(oldData).forEach(([key, value]) => {
                localStorage.setItem(key, value);
            });
            
            resultDiv.innerHTML = '<span class="info">📝 已创建模拟的旧版本明文数据:</span>\n' +
                JSON.stringify(oldData, null, 2) + '\n\n' +
                '<span class="warning">⚠️ 实际迁移功能需要在应用启动时自动执行</span>';
        }

        // 页面加载时显示当前状态
        window.addEventListener('load', function() {
            console.log('🔐 安全存储测试页面已加载');
            console.log('当前localStorage内容:', localStorage);
        });
    </script>
</body>
</html>

import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectPath?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  redirectPath = '/login'
}) => {
  const { currentUser, loading } = useAuth();
  const location = useLocation();

  // 如果正在加载认证状态，显示加载状态
  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500"></div>
      </div>
    );
  }

  // 如果用户未登录，重定向到登录页面并保存当前路径
  if (!currentUser) {
    return <Navigate to={redirectPath} state={{ from: location.pathname }} replace />;
  }

  // 如果用户已登录，渲染子元素
  return <>{children}</>;
};

export default ProtectedRoute; 
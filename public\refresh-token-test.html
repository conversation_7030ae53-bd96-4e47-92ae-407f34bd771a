<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>刷新令牌测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #efe; border-color: #cfc; }
        .error { background-color: #fee; border-color: #fcc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 12px 20px; margin: 8px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        button:hover { background: #005a87; }
        input, textarea { padding: 10px; width: 400px; border: 1px solid #ddd; border-radius: 3px; margin: 5px; }
        textarea { height: 100px; font-family: monospace; }
        .log { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; font-size: 12px; }
        .test-result { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .highlight { background: #ffeb3b; padding: 2px 4px; border-radius: 2px; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #007cba; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 刷新令牌接口测试</h1>
        
        <div class="section info">
            <h2>📋 测试目标</h2>
            <p>验证 <code>POST /auth/refresh-token</code> 接口的完整功能：</p>
            <ul>
                <li>✅ 请求体包含 <span class="highlight">device_id</span> 参数</li>
                <li>✅ 设备ID与登录时保持一致</li>
                <li>❌ 失败时清理本地缓存</li>
                <li>🔄 失败时触发重新登录事件</li>
            </ul>
        </div>

        <div class="section">
            <h2>🧪 测试步骤</h2>
            
            <div class="step">
                <h3>步骤1: 输入测试数据</h3>
                <label><strong>Refresh Token:</strong></label><br>
                <textarea id="refreshToken" placeholder="输入refresh_token（JWT格式）">eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...</textarea>
                <br>
                <label><strong>设备ID:</strong></label><br>
                <input type="text" id="deviceId" placeholder="自动生成设备ID" readonly>
                <br>
                <button onclick="generateDeviceId()">🔍 生成设备ID</button>
            </div>

            <div class="step">
                <h3>步骤2: 测试刷新令牌</h3>
                <button onclick="testRefreshToken()">🔄 测试正常刷新</button>
                <button onclick="testInvalidToken()">❌ 测试无效Token</button>
                <button onclick="testInvalidDeviceId()">🚫 测试错误设备ID</button>
            </div>

            <div class="step">
                <h3>步骤3: 监听事件</h3>
                <button onclick="startEventListener()">👂 开始监听重新登录事件</button>
                <button onclick="clearLog()">🗑️ 清空日志</button>
            </div>
        </div>

        <div class="section" id="request-details" style="display: none;">
            <h2>📊 请求详情</h2>
            <div id="request-content"></div>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section warning">
            <h2>⚠️ 预期结果</h2>
            <div class="test-result">
                <h3>✅ 成功情况</h3>
                <ul>
                    <li><strong>请求体包含:</strong> refresh_token, device_id</li>
                    <li><strong>响应格式:</strong> access_token, token_type, access_token_expires_in</li>
                    <li><strong>设备ID一致:</strong> 与登录时相同</li>
                </ul>
                
                <h3>❌ 失败情况</h3>
                <ul>
                    <li><strong>清理缓存:</strong> 立即清除所有本地存储的认证信息</li>
                    <li><strong>触发事件:</strong> 发送 auth-relogin-required 事件</li>
                    <li><strong>统一提示:</strong> "登录已过期，请重新登录"</li>
                    <li><strong>错误码处理:</strong> 400, 401, 500 都执行相同处理</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let isListening = false;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '5px';
            logEntry.style.padding = '5px';
            logEntry.style.borderRadius = '3px';
            
            if (type === 'success') {
                logEntry.style.backgroundColor = '#d4edda';
                logEntry.style.color = '#155724';
            } else if (type === 'error') {
                logEntry.style.backgroundColor = '#f8d7da';
                logEntry.style.color = '#721c24';
            } else if (type === 'warning') {
                logEntry.style.backgroundColor = '#fff3cd';
                logEntry.style.color = '#856404';
            } else if (type === 'event') {
                logEntry.style.backgroundColor = '#e7f3ff';
                logEntry.style.color = '#0066cc';
            } else {
                logEntry.style.backgroundColor = '#d1ecf1';
                logEntry.style.color = '#0c5460';
            }
            
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('request-details').style.display = 'none';
        }

        function showRequestDetails(title, requestData, responseData) {
            const contentDiv = document.getElementById('request-content');
            
            const hasDeviceId = requestData.device_id ? true : false;
            const hasRefreshToken = requestData.refresh_token ? true : false;
            
            contentDiv.innerHTML = `
                <div class="test-result">
                    <h3>📤 ${title}</h3>
                    <p><strong>refresh_token 状态:</strong> 
                        <span style="color: ${hasRefreshToken ? '#28a745' : '#dc3545'};">
                            ${hasRefreshToken ? '✅ 存在' : '❌ 缺失'}
                        </span>
                    </p>
                    <p><strong>device_id 状态:</strong> 
                        <span style="color: ${hasDeviceId ? '#28a745' : '#dc3545'};">
                            ${hasDeviceId ? '✅ 存在' : '❌ 缺失'}
                        </span>
                    </p>
                    
                    <h4>完整请求体:</h4>
                    <pre>${JSON.stringify(requestData, null, 2)}</pre>
                    
                    <h4>响应数据:</h4>
                    <pre>${JSON.stringify(responseData, null, 2)}</pre>
                </div>
            `;
            
            document.getElementById('request-details').style.display = 'block';
        }

        // 简化的设备ID生成
        async function generateDeviceIdInternal() {
            const features = {
                screen: `${screen.width}x${screen.height}x${screen.colorDepth}`,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                platform: navigator.platform || 'Unknown',
                language: navigator.language,
                userAgent: navigator.userAgent.replace(/\d+\.\d+\.\d+(\.\d+)?/g, 'X.X.X')
            };
            
            const featuresString = JSON.stringify(features);
            const encoder = new TextEncoder();
            const data = encoder.encode(featuresString);
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            const hash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
            
            const paddedHash = (hash + '0'.repeat(32)).substring(0, 32);
            return [
                paddedHash.substring(0, 8),
                paddedHash.substring(8, 12),
                '4' + paddedHash.substring(13, 16),
                ((parseInt(paddedHash.substring(16, 17), 16) & 0x3) | 0x8).toString(16) + paddedHash.substring(17, 20),
                paddedHash.substring(20, 32)
            ].join('-');
        }

        async function generateDeviceId() {
            try {
                const deviceId = await generateDeviceIdInternal();
                document.getElementById('deviceId').value = deviceId;
                log(`🔍 设备ID生成成功: ${deviceId}`, 'success');
            } catch (error) {
                log(`❌ 设备ID生成失败: ${error.message}`, 'error');
            }
        }

        async function testRefreshToken() {
            const refreshToken = document.getElementById('refreshToken').value.trim();
            const deviceId = document.getElementById('deviceId').value.trim();
            
            if (!refreshToken || !deviceId) {
                log('❌ 请输入refresh_token和设备ID', 'error');
                return;
            }
            
            log('🔄 开始测试正常刷新令牌...', 'info');
            
            try {
                const requestData = {
                    refresh_token: refreshToken,
                    device_id: deviceId
                };
                
                const response = await fetch('http://localhost:5173/api/auth/refresh-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                let responseData;
                try {
                    const responseText = await response.text();
                    responseData = responseText ? JSON.parse(responseText) : {};
                } catch (parseError) {
                    responseData = { error: '响应解析失败' };
                }
                
                showRequestDetails('正常刷新令牌', requestData, responseData);
                
                if (response.ok) {
                    log('🎉 刷新令牌成功！', 'success');
                    log(`✅ 获得新访问令牌: ${responseData.access_token ? '是' : '否'}`, 'success');
                } else {
                    log(`❌ 刷新令牌失败，状态码: ${response.status}`, 'error');
                    log('💡 前端应清理缓存并触发重新登录事件', 'warning');
                }
                
            } catch (error) {
                log(`❌ 刷新失败: ${error.message}`, 'error');
                log('💡 前端应清理缓存并触发重新登录事件', 'warning');
            }
        }

        async function testInvalidToken() {
            const deviceId = document.getElementById('deviceId').value.trim();
            
            if (!deviceId) {
                log('❌ 请先生成设备ID', 'error');
                return;
            }
            
            log('🧪 测试无效refresh_token...', 'warning');
            
            try {
                const requestData = {
                    refresh_token: 'invalid_token_12345',
                    device_id: deviceId
                };
                
                const response = await fetch('http://localhost:5173/api/auth/refresh-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                let responseData;
                try {
                    const responseText = await response.text();
                    responseData = responseText ? JSON.parse(responseText) : {};
                } catch (parseError) {
                    responseData = { error: '响应解析失败' };
                }
                
                showRequestDetails('无效Token测试', requestData, responseData);
                
                if (response.ok) {
                    log('⚠️ 意外成功！无效token不应该通过验证', 'warning');
                } else {
                    log(`✅ 正确拒绝无效token，状态码: ${response.status}`, 'success');
                    log('💡 前端应清理缓存并触发重新登录事件', 'info');
                }
                
            } catch (error) {
                log(`✅ 正确处理错误: ${error.message}`, 'success');
            }
        }

        async function testInvalidDeviceId() {
            const refreshToken = document.getElementById('refreshToken').value.trim();
            
            if (!refreshToken) {
                log('❌ 请输入refresh_token', 'error');
                return;
            }
            
            log('🧪 测试错误设备ID...', 'warning');
            
            try {
                const requestData = {
                    refresh_token: refreshToken,
                    device_id: '00000000-0000-4000-8000-000000000000' // 错误的设备ID
                };
                
                const response = await fetch('http://localhost:5173/api/auth/refresh-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                let responseData;
                try {
                    const responseText = await response.text();
                    responseData = responseText ? JSON.parse(responseText) : {};
                } catch (parseError) {
                    responseData = { error: '响应解析失败' };
                }
                
                showRequestDetails('错误设备ID测试', requestData, responseData);
                
                if (response.ok) {
                    log('⚠️ 意外成功！错误设备ID不应该通过验证', 'warning');
                } else {
                    log(`✅ 正确拒绝错误设备ID，状态码: ${response.status}`, 'success');
                    log('💡 前端应清理缓存并触发重新登录事件', 'info');
                }
                
            } catch (error) {
                log(`✅ 正确处理错误: ${error.message}`, 'success');
            }
        }

        function startEventListener() {
            if (isListening) {
                log('👂 已经在监听重新登录事件', 'info');
                return;
            }
            
            isListening = true;
            log('👂 开始监听 auth-relogin-required 事件...', 'info');
            
            window.addEventListener('auth-relogin-required', function(event) {
                log('🔔 收到重新登录事件！', 'event');
                log(`📝 事件详情: ${JSON.stringify(event.detail)}`, 'event');
                log('✅ 前端应该引导用户重新登录', 'success');
            });
            
            // 也监听storage事件
            window.addEventListener('storage', function(event) {
                if (event.key === 'access_token' || event.key === 'refresh_token') {
                    log(`💾 存储变化: ${event.key} = ${event.newValue ? '有值' : '已清除'}`, 'event');
                }
            });
        }

        // 页面加载时显示信息
        window.onload = function() {
            log('🔄 刷新令牌测试工具已加载', 'info');
            log('📋 测试目标: 验证device_id参数和错误处理', 'info');
            log('🎯 请先生成设备ID，然后进行测试', 'info');
            
            // 自动生成设备ID
            generateDeviceId();
        };
    </script>
</body>
</html>

import React, { createContext, useState, useContext, useEffect } from 'react';
import { 
  getAccessToken, 
  isAccessTokenExpired, 
  logout as authLogout 
} from '../services/authService';
import { addStorageListener, getStorageItem } from '../services/storageAdapter';
import { resetNeedRelogin } from '../services/apiInterceptor';

// 定义用户类型
export interface User {
  id: string;
  email: string;
}

// 定义上下文类型
interface AuthContextType {
  currentUser: User | null;
  logout: () => Promise<void>;
  loading: boolean;
  needRelogin: boolean;
}

// 创建上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 自定义钩子，用于在组件中获取上下文
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth 必须在 AuthProvider 中使用');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [needRelogin, setNeedRelogin] = useState(false);

  // 登出函数，使用 authService 中的 logout
  async function logout() {
    try {
      // 先清除本地用户状态
      setCurrentUser(null);
      
      // 尝试调用 authService 的 logout
      try {
        await authLogout();
      } catch (error) {
        // 忽略后端登出错误，本地状态已清除
      }
      
      // 重置需要重新登录的状态
      setNeedRelogin(false);
      resetNeedRelogin();
      
      return Promise.resolve();
    } catch (error) {
      console.error('登出过程中出错:', error);
      return Promise.reject(error);
    }
  }

  // 更新用户状态
  const updateUserState = async () => {
    try {
      const userId = await getStorageItem<string>('user_id');
      const userEmail = await getStorageItem<string>('user_email');
      
      if (userId && userEmail) {
        setCurrentUser({
          id: userId,
          email: userEmail
        });

        // 用户登录后重置重新登录状态
        if (needRelogin) {
          setNeedRelogin(false);
          resetNeedRelogin();
        }
      } else {
        // 检查是否有访问令牌
        const accessToken = await getAccessToken();
        if (accessToken && !(await isAccessTokenExpired())) {
          // 有效令牌但用户信息不完整
        } else {
          setCurrentUser(null);

          // 检测异常情况：有部分用户信息但没有令牌
          if ((userId || userEmail) && !accessToken) {
            // 触发自动修复
            if ((window as any).debugSecureStorage?.emergencyFixTokens) {
              try {
                (window as any).debugSecureStorage.emergencyFixTokens();
              } catch (error) {
                console.error('自动修复失败:', error);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('更新用户状态时出错:', error);
      setCurrentUser(null);
    }
  };

  // 初始化时检查用户登录状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        await updateUserState();
      } catch (error) {
        console.error('检查用户登录状态时出错:', error);
      } finally {
        setLoading(false);
      }
    };
    
    initAuth();
  }, []);

  // 添加存储变化监听器
  useEffect(() => {
    // 使用存储适配器的监听器
    const removeListener = addStorageListener((changes) => {
      // 检查是否有认证相关的数据变更
      if (
        changes.access_token ||
        changes.user_id ||
        changes.user_email ||
        changes.cleared
      ) {
        updateUserState();
      }
    });

    // 监听认证数据恢复事件
    const handleAuthRestored = (event: CustomEvent) => {
      updateUserState();
    };

    window.addEventListener('auth-restored', handleAuthRestored as EventListener);

    return () => {
      removeListener();
      window.removeEventListener('auth-restored', handleAuthRestored as EventListener);
    };
  }, []);
  
  // 监听需要重新登录的事件
  useEffect(() => {
    const handleReloginRequired = (event: Event) => {
      setNeedRelogin(true);

      // 尝试获取详细的错误消息
      let errorMessage = '登录已过期，请重新登录';
      if (event instanceof CustomEvent && event.detail && event.detail.message) {
        errorMessage = event.detail.message;
      }

      // 如果当前已登录，执行登出操作
      if (currentUser) {
        logout().then(() => {
          // 显示通知或弹窗提醒用户重新登录
          if (typeof window !== 'undefined') {
            // 创建一个临时消息元素
            const messageEl = document.createElement('div');
            messageEl.style.position = 'fixed';
            messageEl.style.top = '20px';
            messageEl.style.left = '50%';
            messageEl.style.transform = 'translateX(-50%)';
            messageEl.style.padding = '10px 20px';
            messageEl.style.backgroundColor = '#f8d7da';
            messageEl.style.color = '#721c24';
            messageEl.style.borderRadius = '4px';
            messageEl.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
            messageEl.style.zIndex = '9999';
            messageEl.textContent = errorMessage;
            
            document.body.appendChild(messageEl);
            
            // 3秒后自动移除
            setTimeout(() => {
              if (document.body.contains(messageEl)) {
                document.body.removeChild(messageEl);
              }
            }, 3000);
          }
        });
      }
    };
    
    window.addEventListener('auth-relogin-required', handleReloginRequired);
    
    return () => {
      window.removeEventListener('auth-relogin-required', handleReloginRequired);
    };
  }, [currentUser]);

  const value = {
    currentUser,
    logout,
    loading,
    needRelogin
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export default AuthContext; 
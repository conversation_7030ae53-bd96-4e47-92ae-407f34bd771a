import { useState } from 'react';
import { requestVerificationCode } from '../services/authService';
import { log } from '../utils/logger';

/**
 * API测试组件
 * 用于测试API连接是否正常工作
 */
const ApiTestComponent = () => {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      setMessage('请输入邮箱地址');
      return;
    }
    
    setStatus('loading');
    setMessage('正在发送请求...');
    
    try {
      const result = await requestVerificationCode(email);
      setStatus('success');
      
      if (result === 1) {
        setMessage('验证码发送成功！');
      } else if (result === 2) {
        setMessage('已有未过期的验证码，请稍后再试。');
      } else if (result === 3) {
        setMessage('验证码发送失败，请稍后再试。');
      } else {
        setMessage(`请求成功，但返回了意外的结果: ${result}`);
      }
    } catch (error) {
      setStatus('error');
      setMessage(`请求失败: ${error instanceof Error ? error.message : String(error)}`);
      log.error('API测试失败:', error);
    }
  };

  return (
    <div className="api-test-container" style={{ maxWidth: '500px', margin: '0 auto', padding: '20px' }}>
      <h2>API连接测试</h2>
      <p>此组件用于测试API连接是否正常工作。输入邮箱地址并点击"发送验证码"按钮。</p>
      
      <form onSubmit={handleSubmit} style={{ marginTop: '20px' }}>
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="email" style={{ display: 'block', marginBottom: '5px' }}>邮箱地址:</label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            style={{ 
              width: '100%', 
              padding: '8px', 
              borderRadius: '4px',
              border: '1px solid #ccc' 
            }}
            placeholder="请输入邮箱地址"
          />
        </div>
        
        <button 
          type="submit" 
          disabled={status === 'loading'}
          style={{
            padding: '10px 15px',
            backgroundColor: status === 'loading' ? '#cccccc' : '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: status === 'loading' ? 'not-allowed' : 'pointer'
          }}
        >
          {status === 'loading' ? '发送中...' : '发送验证码'}
        </button>
      </form>
      
      {message && (
        <div 
          style={{ 
            marginTop: '20px', 
            padding: '10px', 
            borderRadius: '4px',
            backgroundColor: status === 'error' ? '#ffebee' : 
                           status === 'success' ? '#e8f5e9' : '#e3f2fd',
            color: status === 'error' ? '#c62828' : 
                  status === 'success' ? '#2e7d32' : '#1565c0'
          }}
        >
          {message}
        </div>
      )}
      
      <div style={{ marginTop: '30px', fontSize: '14px', color: '#666' }}>
        <h3>调试信息</h3>
        <p>API URL: {import.meta.env.VITE_API_URL || '/api (使用代理)'}</p>
        <p>API Key: {import.meta.env.VITE_API_KEY ? '已设置' : '未设置'}</p>
        <p>环境: {import.meta.env.DEV ? '开发环境' : '生产环境'}</p>
      </div>
    </div>
  );
};

export default ApiTestComponent; 
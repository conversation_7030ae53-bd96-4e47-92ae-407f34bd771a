<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>强制加密工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .warning {
            color: #ffc107;
        }
        .info {
            color: #17a2b8;
        }
        .status {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .encrypted {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .plain {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 强制加密工具</h1>
        <p>此工具可以立即加密当前存储中的敏感数据。</p>
        
        <button onclick="checkStatus()">检查当前状态</button>
        <button onclick="forceEncrypt()">立即强制加密</button>
        <button onclick="testLogin()">模拟登录数据</button>
        <button onclick="clearAll()">清除所有数据</button>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        // 加密类（与实际代码保持一致）
        class SecureStorageEncryption {
            static SENSITIVE_KEYS = ['access_token', 'refresh_token', 'user_id'];
            static encryptionKey = null;

            static generateDeviceKey() {
                if (this.encryptionKey) {
                    return this.encryptionKey;
                }

                try {
                    const fingerprint = [
                        navigator.userAgent,
                        navigator.language,
                        `${screen.width}x${screen.height}`,
                        new Date().getTimezoneOffset().toString(),
                        'autofill-secure-key-v1'
                    ].join('|');
                    
                    this.encryptionKey = btoa(fingerprint).replace(/[^a-zA-Z0-9]/g, '').slice(0, 32).padEnd(32, 'A');
                    return this.encryptionKey;
                } catch (error) {
                    console.warn('生成设备密钥失败，使用默认密钥:', error);
                    this.encryptionKey = 'AutoFillSecureStorageDefaultKey32';
                    return this.encryptionKey;
                }
            }

            static isSensitive(key) {
                return this.SENSITIVE_KEYS.includes(key);
            }

            static encrypt(data) {
                if (!data) return data;
                
                try {
                    const key = this.generateDeviceKey();
                    
                    const encrypted = data.split('').map((char, i) => {
                        const keyChar = key.charCodeAt(i % key.length);
                        const dataChar = char.charCodeAt(0);
                        return String.fromCharCode(dataChar ^ keyChar);
                    }).join('');
                    
                    return 'ENCRYPTED:' + btoa(encrypted);
                } catch (error) {
                    console.error('加密失败:', error);
                    return data;
                }
            }

            static decrypt(encryptedData) {
                if (!encryptedData || !encryptedData.startsWith('ENCRYPTED:')) {
                    return encryptedData;
                }
                
                try {
                    const key = this.generateDeviceKey();
                    
                    const base64Data = encryptedData.replace('ENCRYPTED:', '');
                    const encrypted = atob(base64Data);
                    
                    const decrypted = encrypted.split('').map((char, i) => {
                        const keyChar = key.charCodeAt(i % key.length);
                        const encryptedChar = char.charCodeAt(0);
                        return String.fromCharCode(encryptedChar ^ keyChar);
                    }).join('');
                    
                    return decrypted;
                } catch (error) {
                    console.error('解密失败:', error);
                    return encryptedData;
                }
            }
        }

        function checkStatus() {
            const resultDiv = document.getElementById('result');
            const sensitiveKeys = ['access_token', 'refresh_token', 'user_id'];
            const otherKeys = ['user_email', 'subscription_info', 'token_type', 'access_token_expires_at'];
            
            let result = '<span class="info">📊 当前存储状态检查:</span>\n\n';
            
            // 检查敏感数据
            result += '<span class="info">敏感数据状态:</span>\n';
            sensitiveKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    const isEncrypted = value.startsWith('ENCRYPTED:');
                    const status = isEncrypted ? '🔒 已加密' : '⚠️ 明文';
                    const className = isEncrypted ? 'success' : 'error';
                    result += `<span class="${className}">${key}: ${status}</span>\n`;
                    
                    // 显示部分内容
                    const displayValue = value.length > 50 ? value.substring(0, 50) + '...' : value;
                    result += `  内容: ${displayValue}\n`;
                    
                    // 如果是加密数据，尝试解密验证
                    if (isEncrypted) {
                        try {
                            const decrypted = SecureStorageEncryption.decrypt(value);
                            result += `  解密验证: <span class="success">✅ 成功</span>\n`;
                        } catch (e) {
                            result += `  解密验证: <span class="error">❌ 失败</span>\n`;
                        }
                    }
                    result += '\n';
                } else {
                    result += `<span class="warning">${key}: 无数据</span>\n`;
                }
            });
            
            // 检查其他数据
            result += '\n<span class="info">其他数据:</span>\n';
            otherKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    result += `<span class="info">${key}: 存在</span>\n`;
                    const displayValue = value.length > 30 ? value.substring(0, 30) + '...' : value;
                    result += `  内容: ${displayValue}\n`;
                } else {
                    result += `<span class="warning">${key}: 无数据</span>\n`;
                }
            });
            
            resultDiv.innerHTML = result;
        }

        function forceEncrypt() {
            const resultDiv = document.getElementById('result');
            const sensitiveKeys = ['access_token', 'refresh_token', 'user_id'];
            
            let result = '<span class="info">🔄 强制加密执行中...</span>\n\n';
            
            sensitiveKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    // 如果已经加密，先解密
                    let plainValue = value;
                    if (value.startsWith('ENCRYPTED:')) {
                        plainValue = SecureStorageEncryption.decrypt(value);
                        result += `<span class="info">${key}: 检测到已加密数据，先解密</span>\n`;
                    }
                    
                    // 重新加密
                    const encrypted = SecureStorageEncryption.encrypt(plainValue);
                    localStorage.setItem(key, encrypted);
                    
                    result += `<span class="success">✅ ${key}: 已重新加密</span>\n`;
                    result += `  新值: ${encrypted.substring(0, 50)}...\n\n`;
                } else {
                    result += `<span class="warning">⚠️ ${key}: 无数据可加密</span>\n`;
                }
            });
            
            result += '<span class="success">🎉 强制加密完成！请刷新主应用查看效果。</span>';
            resultDiv.innerHTML = result;
        }

        function testLogin() {
            const resultDiv = document.getElementById('result');
            
            // 模拟登录数据
            const testData = {
                access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0LXVzZXIiLCJpYXQiOjE2MzQ1Njc4OTAsImV4cCI6MTYzNDU3MTQ5MH0.test-signature',
                refresh_token: 'test-refresh-token-' + Date.now(),
                user_id: 'test-user-' + Date.now(),
                user_email: '<EMAIL>',
                subscription_info: JSON.stringify({plan: 'premium', expires_at: '2024-12-31'})
            };
            
            let result = '<span class="info">🧪 创建模拟登录数据...</span>\n\n';
            
            Object.entries(testData).forEach(([key, value]) => {
                if (SecureStorageEncryption.isSensitive(key)) {
                    const encrypted = SecureStorageEncryption.encrypt(value);
                    localStorage.setItem(key, encrypted);
                    result += `<span class="success">✅ ${key}: 已加密存储</span>\n`;
                } else {
                    localStorage.setItem(key, value);
                    result += `<span class="info">📝 ${key}: 已明文存储</span>\n`;
                }
            });
            
            result += '\n<span class="success">🎉 模拟登录数据创建完成！</span>';
            resultDiv.innerHTML = result;
        }

        function clearAll() {
            const resultDiv = document.getElementById('result');
            const keys = ['access_token', 'refresh_token', 'user_id', 'user_email', 'subscription_info', 'token_type', 'access_token_expires_at'];
            
            keys.forEach(key => {
                localStorage.removeItem(key);
            });
            
            resultDiv.innerHTML = '<span class="success">🗑️ 所有数据已清除</span>';
        }

        // 页面加载时自动检查状态
        window.addEventListener('load', function() {
            console.log('🔐 强制加密工具已加载');
            checkStatus();
        });
    </script>
</body>
</html>

/**
 * 浏览器插件活动监控工具
 * 用于记录和查看插件与网站的交互记录
 */

import { log } from './logger';

export interface ExtensionActivity {
  timestamp: number;
  type: 'extension_detected' | 'storage_access' | 'data_sync' | 'validation' | 'error';
  extensionId?: string;
  operation?: string;
  data?: any;
  success?: boolean;
  message?: string;
}

export class ExtensionMonitor {
  private static instance: ExtensionMonitor;
  private activities: ExtensionActivity[] = [];
  private maxActivities = 1000; // 最多保存1000条记录
  private listeners: ((activity: ExtensionActivity) => void)[] = [];

  private constructor() {
    this.initializeMonitoring();
  }

  public static getInstance(): ExtensionMonitor {
    if (!ExtensionMonitor.instance) {
      ExtensionMonitor.instance = new ExtensionMonitor();
    }
    return ExtensionMonitor.instance;
  }

  /**
   * 初始化监控
   */
  private initializeMonitoring(): void {
    // 监控插件安装事件
    window.addEventListener('extension-installed', (event: any) => {
      this.logActivity({
        timestamp: Date.now(),
        type: 'extension_detected',
        extensionId: event.detail?.extensionId,
        data: event.detail,
        success: true,
        message: '检测到浏览器插件安装'
      });
    });

    // 监控存储变化事件
    window.addEventListener('storage', (event) => {
      this.logActivity({
        timestamp: Date.now(),
        type: 'storage_access',
        operation: 'storage_change',
        data: {
          key: event.key,
          oldValue: event.oldValue,
          newValue: event.newValue,
          url: event.url
        },
        success: true,
        message: `存储变化: ${event.key}`
      });
    });

    // 监控自定义同步事件
    window.addEventListener('sync-to-extension', (event: any) => {
      this.logActivity({
        timestamp: Date.now(),
        type: 'data_sync',
        operation: 'sync_to_extension',
        data: event.detail,
        success: true,
        message: '数据同步到插件'
      });
    });

    // 监控认证恢复事件
    window.addEventListener('auth-restored', (event: any) => {
      this.logActivity({
        timestamp: Date.now(),
        type: 'data_sync',
        operation: 'auth_restored',
        data: event.detail,
        success: true,
        message: '从插件恢复认证数据'
      });
    });

    log.debug('[ExtensionMonitor] 插件活动监控已启动');
  }

  /**
   * 记录活动
   */
  public logActivity(activity: ExtensionActivity): void {
    // 添加到活动列表
    this.activities.unshift(activity);
    
    // 限制记录数量
    if (this.activities.length > this.maxActivities) {
      this.activities = this.activities.slice(0, this.maxActivities);
    }

    // 通知监听器
    this.listeners.forEach(listener => {
      try {
        listener(activity);
      } catch (error) {
        log.error('[ExtensionMonitor] 监听器执行失败:', error);
      }
    });

    // 在开发环境中输出到控制台
    if (import.meta.env.DEV) {
      log.debug(`[ExtensionMonitor] ${activity.type}:`, activity);
    }
  }

  /**
   * 获取所有活动记录
   */
  public getActivities(): ExtensionActivity[] {
    return [...this.activities];
  }

  /**
   * 获取特定类型的活动记录
   */
  public getActivitiesByType(type: ExtensionActivity['type']): ExtensionActivity[] {
    return this.activities.filter(activity => activity.type === type);
  }

  /**
   * 获取特定插件的活动记录
   */
  public getActivitiesByExtension(extensionId: string): ExtensionActivity[] {
    return this.activities.filter(activity => activity.extensionId === extensionId);
  }

  /**
   * 获取最近的活动记录
   */
  public getRecentActivities(count: number = 10): ExtensionActivity[] {
    return this.activities.slice(0, count);
  }

  /**
   * 清除所有活动记录
   */
  public clearActivities(): void {
    this.activities = [];
    log.debug('[ExtensionMonitor] 活动记录已清除');
  }

  /**
   * 添加活动监听器
   */
  public addListener(listener: (activity: ExtensionActivity) => void): void {
    this.listeners.push(listener);
  }

  /**
   * 移除活动监听器
   */
  public removeListener(listener: (activity: ExtensionActivity) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 导出活动记录为JSON
   */
  public exportActivities(): string {
    return JSON.stringify(this.activities, null, 2);
  }

  /**
   * 获取活动统计信息
   */
  public getStatistics(): {
    total: number;
    byType: Record<string, number>;
    byExtension: Record<string, number>;
    successRate: number;
    recentActivity: string;
  } {
    const total = this.activities.length;
    const byType: Record<string, number> = {};
    const byExtension: Record<string, number> = {};
    let successCount = 0;

    this.activities.forEach(activity => {
      // 按类型统计
      byType[activity.type] = (byType[activity.type] || 0) + 1;
      
      // 按插件统计
      if (activity.extensionId) {
        byExtension[activity.extensionId] = (byExtension[activity.extensionId] || 0) + 1;
      }
      
      // 成功率统计
      if (activity.success) {
        successCount++;
      }
    });

    const successRate = total > 0 ? (successCount / total) * 100 : 0;
    const recentActivity = this.activities.length > 0 
      ? new Date(this.activities[0].timestamp).toLocaleString()
      : '无活动记录';

    return {
      total,
      byType,
      byExtension,
      successRate,
      recentActivity
    };
  }

  /**
   * 手动记录插件验证活动
   */
  public logValidation(extensionId: string, success: boolean, message: string): void {
    this.logActivity({
      timestamp: Date.now(),
      type: 'validation',
      extensionId,
      success,
      message
    });
  }

  /**
   * 手动记录存储访问活动
   */
  public logStorageAccess(operation: string, data: any, success: boolean, message: string): void {
    this.logActivity({
      timestamp: Date.now(),
      type: 'storage_access',
      operation,
      data,
      success,
      message
    });
  }

  /**
   * 手动记录错误活动
   */
  public logError(message: string, data?: any): void {
    this.logActivity({
      timestamp: Date.now(),
      type: 'error',
      data,
      success: false,
      message
    });
  }
}

// 导出单例实例
export const extensionMonitor = ExtensionMonitor.getInstance();

// 在全局对象上暴露监控器（仅开发环境）
if (import.meta.env.DEV) {
  (window as any).extensionMonitor = extensionMonitor;
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle产品配置诊断</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        .critical { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .checklist { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .checklist-item { padding: 5px 0; border-bottom: 1px solid #eee; }
        .product-info { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Paddle产品配置诊断</h1>
        
        <div class="section critical">
            <h2>🚨 确认的问题</h2>
            <p><strong>产品数据获取失败 - 这不是参数格式问题！</strong></p>
            <ul>
                <li>✅ API调用成功（无400/403错误）</li>
                <li>✅ 支付页面能打开</li>
                <li>✅ 参数格式正确（测试了所有官方格式）</li>
                <li>❌ <strong>产品数据始终为空</strong> - price_id: null, product.id: 0</li>
            </ul>
        </div>

        <div class="section warning">
            <h2>🎯 需要在Paddle Dashboard中检查的配置</h2>
            
            <div class="product-info">
                <h3>📋 当前产品信息</h3>
                <p><strong>产品ID:</strong> <code>pri_01k0xttmf9wekbg957y66xawj</code></p>
                <p><strong>价格:</strong> $9.99/year (30天试用)</p>
                <p><strong>环境:</strong> Sandbox</p>
                <p><strong>Token:</strong> <code>test_459728092d35d36bc173c60e52d</code></p>
            </div>

            <div class="checklist">
                <h3>🔍 必须检查的配置项：</h3>
                
                <div class="checklist-item">
                    <strong>1. 产品状态</strong>
                    <ul>
                        <li>进入 Paddle Dashboard > Catalog > Products</li>
                        <li>找到 "autoinput" 产品</li>
                        <li>确认状态为 <strong>"Active"</strong></li>
                        <li>如果是 "Draft" 或其他状态，需要激活</li>
                    </ul>
                </div>
                
                <div class="checklist-item">
                    <strong>2. 价格状态</strong>
                    <ul>
                        <li>点击产品进入详情页</li>
                        <li>查看价格列表中的 $9.99/year</li>
                        <li>确认价格状态为 <strong>"Active"</strong></li>
                        <li>确认价格ID正确: <code>pri_01k0xttmf9wekbg957y66xawj</code></li>
                    </ul>
                </div>
                
                <div class="checklist-item">
                    <strong>3. 试用期配置</strong>
                    <ul>
                        <li>检查30天试用期设置是否正确</li>
                        <li>确认试用期没有阻止产品显示</li>
                        <li>考虑暂时禁用试用期进行测试</li>
                    </ul>
                </div>
                
                <div class="checklist-item">
                    <strong>4. 环境匹配</strong>
                    <ul>
                        <li>确认使用的是 <strong>Sandbox</strong> 环境</li>
                        <li>确认Token是Sandbox环境的Token</li>
                        <li>确认产品是在Sandbox环境中创建的</li>
                    </ul>
                </div>
                
                <div class="checklist-item">
                    <strong>5. 产品可见性</strong>
                    <ul>
                        <li>确认产品对客户可见</li>
                        <li>检查是否有地理限制</li>
                        <li>检查是否有其他访问限制</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section error">
            <h2>🔧 立即行动步骤</h2>
            <ol>
                <li><strong>登录Paddle Sandbox Dashboard</strong>
                    <br>🔗 <a href="https://sandbox-vendors.paddle.com/" target="_blank">https://sandbox-vendors.paddle.com/</a>
                </li>
                
                <li><strong>检查产品状态</strong>
                    <br>Catalog > Products > autoinput > 确认状态为Active
                </li>
                
                <li><strong>检查价格状态</strong>
                    <br>点击产品 > 查看价格列表 > 确认$9.99/year为Active
                </li>
                
                <li><strong>如果状态都正确，尝试创建新的简单产品</strong>
                    <ul>
                        <li>创建一个新产品（无试用期）</li>
                        <li>设置简单的一次性价格（如$1.00）</li>
                        <li>确保状态为Active</li>
                        <li>使用新的价格ID进行测试</li>
                    </ul>
                </li>
                
                <li><strong>联系Paddle技术支持</strong>
                    <br>如果以上都正确但仍有问题，说明可能是Paddle系统问题
                </li>
            </ol>
        </div>

        <div class="section info">
            <h2>💡 可能的原因分析</h2>
            <ul>
                <li><strong>产品未完全激活:</strong> 产品或价格状态不是Active</li>
                <li><strong>试用期配置问题:</strong> 30天试用期配置导致产品数据获取失败</li>
                <li><strong>环境不匹配:</strong> Token和产品不在同一环境</li>
                <li><strong>产品配置不完整:</strong> 缺少必要的产品信息</li>
                <li><strong>Paddle系统问题:</strong> Sandbox环境的临时问题</li>
            </ul>
        </div>

        <div class="section success">
            <h2>✅ 确认工作的配置</h2>
            <p>一旦产品配置正确，您应该看到：</p>
            <ul>
                <li>✅ price_id: "pri_01k0xttmf9wekbg957y66xawj"</li>
                <li>✅ product.id: 非零数字</li>
                <li>✅ product.name: "autoinput" 或您设置的产品名称</li>
                <li>✅ 正确的价格显示: $9.99</li>
            </ul>
        </div>

        <div class="section warning">
            <h2>🚀 临时解决方案</h2>
            <p>如果需要快速测试，建议：</p>
            <ol>
                <li><strong>创建一个新的简单产品</strong>（无试用期，一次性付费）</li>
                <li><strong>使用新产品ID进行测试</strong></li>
                <li><strong>确认基本功能正常后</strong>，再回来解决原产品的问题</li>
            </ol>
        </div>
    </div>

    <script>
        // 页面加载时显示重要信息
        window.onload = function() {
            console.log('🔧 Paddle产品配置诊断工具已加载');
            console.log('💡 问题确认：不是参数格式问题，而是产品配置问题');
            console.log('🎯 需要检查Paddle Dashboard中的产品和价格状态');
            
            // 高亮显示关键信息
            const productInfo = document.querySelector('.product-info');
            if (productInfo) {
                productInfo.style.animation = 'pulse 2s infinite';
            }
        };

        // 添加脉冲动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.7; }
                100% { opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

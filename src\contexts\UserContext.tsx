import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { getUserProfile } from '../services/userService';
import { useAuth } from './AuthContext';
import { addStorageListener, getStorageItem } from '../services/storageAdapter';

// 用户资料类型
export interface UserProfile {
  user_id: string;
  email: string;
  subscription_info: any | null;
}

// 上下文类型
interface UserContextType {
  userProfile: UserProfile | null;
  loading: boolean;
  error: string | null;
  refreshUserProfile: () => Promise<void>;
}

// 创建上下文
const UserContext = createContext<UserContextType | undefined>(undefined);

// 自定义钩子
export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser 必须在 UserProvider 中使用');
  }
  return context;
}

export function UserProvider({ children }: { children: React.ReactNode }) {
  const { currentUser } = useAuth();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState(0);

  // 刷新用户资料 - 使用防抖功能避免频繁请求
  const refreshUserProfile = useCallback(async () => {
    if (!currentUser) {
      setUserProfile(null);
      setLoading(false);
      return;
    }

    // 防抖：如果距离上次刷新不足3秒，则跳过
    const now = Date.now();
    if (isRefreshing || (now - lastRefreshTime < 3000)) {
      console.log('刷新请求被忽略：正在刷新或距离上次刷新时间不足3秒');
      return;
    }

    setIsRefreshing(true);
    setLoading(true);
    setError(null);
    setLastRefreshTime(now);

    try {
      // 尝试从 API 获取用户资料
      const profile = await getUserProfile();
      setUserProfile(profile);
      // 用户资料已更新
    } catch (error) {
      console.error('获取用户资料失败:', error);
      
      // 如果 API 请求失败，尝试从存储中获取缓存的用户资料
      try {
        // 首先检查错误是否与认证相关
        const isAuthError = error instanceof Error && 
          (error.message.includes('认证失败') || 
           error.message.includes('无效') || 
           error.message.includes('过期') || 
           error.message.includes('token') || 
           error.message.includes('Token') || 
           error.message.includes('INVALID_TOKEN'));
        
        if (isAuthError) {
          console.log('检测到认证错误，需要重新登录');
          setUserProfile(null);
          setError('登录已过期，请重新登录');
          // 触发需要重新登录的事件
          window.dispatchEvent(new CustomEvent('auth-relogin-required'));
          return;
        }
        
        // 如果不是认证错误，尝试从本地存储获取
        const userId = await getStorageItem<string>('user_id');
        const userEmail = await getStorageItem<string>('user_email');
        const subscriptionInfoStr = await getStorageItem<string>('subscription_info');
        
        if (userId && userEmail) {
          let subscriptionInfo = null;
          
          // 安全解析订阅信息
          if (subscriptionInfoStr) {
            try {
              // 排除无效值
              if (subscriptionInfoStr !== "0" && subscriptionInfoStr !== "undefined" && subscriptionInfoStr !== "null") {
                subscriptionInfo = JSON.parse(subscriptionInfoStr);
              }
            } catch (e) {
              console.warn('解析缓存的订阅信息字符串失败:', e);
              // 解析失败，使用null
              subscriptionInfo = null;
            }
          }
          
          const cachedProfile: UserProfile = {
            user_id: userId,
            email: userEmail,
            subscription_info: subscriptionInfo
          };
          
          setUserProfile(cachedProfile);
          console.log('从缓存加载用户资料:', cachedProfile);
        } else {
          setUserProfile(null);
          setError('请登录后查看个人资料');
        }
      } catch (cacheError) {
        console.error('从缓存加载用户资料失败:', cacheError);
        setUserProfile(null);
        setError('请登录后查看个人资料');
      }
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  }, [currentUser, isRefreshing, lastRefreshTime]);

  // 当用户状态变化时刷新资料
  useEffect(() => {
    refreshUserProfile();
  }, [currentUser]);
  
  // 监听存储变化，可能是其他标签页登录/登出
  useEffect(() => {
    const removeListener = addStorageListener((changes) => {
      if (changes.access_token || changes.user_id || changes.user_email || changes.subscription_info) {
        console.log('存储变化检测到，刷新用户资料');
        refreshUserProfile().catch(err => {
          console.error('因存储变化刷新用户资料时出错:', err);
        });
      }
    });
    
    return () => {
      removeListener();
    };
  }, []);

  const value = {
    userProfile,
    loading,
    error,
    refreshUserProfile
  };

  useEffect(() => {
    // @ts-ignore
    window.userContext = {
      refreshUserProfile: refreshUserProfile
    };
    
    return () => {
      // @ts-ignore
      delete window.userContext;
    };
  }, [refreshUserProfile]);

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}

export default UserContext; 
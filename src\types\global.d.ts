// Extend the Window interface to include user<PERSON>ontex<PERSON> and Paddle
declare global {
  interface Window {
    userContext?: {
      refreshUserProfile: () => Promise<void>;
    };
    runSubscriptionTests?: () => void;
    testTokenDecryption?: () => Promise<void>;
    Paddle?: {
      Environment?: {
        set: (environment: 'sandbox' | 'production') => void;
      };
      Initialize: (config: {
        token: string;
        eventCallback?: (data: any) => void;
      }) => void;
      Checkout: {
        open: (config: {
          items: Array<{ priceId: string; quantity: number; }>;
          customer?: { email?: string; id?: string; };
          customData?: any;
          settings?: {
            successUrl?: string;
            cancelUrl?: string;
            displayMode?: 'overlay' | 'inline';
            theme?: string;
            locale?: string;
          };
        }) => void;
        close: () => void;
      };
    };
  }
}

export {};

<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>网站同步登录扩展</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      width: 300px;
      padding: 15px;
      margin: 0;
    }
    .container {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .header h1 {
      font-size: 16px;
      margin: 0;
    }
    .status {
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 10px;
    }
    .status.logged-in {
      background-color: #e8f5e9;
      color: #2e7d32;
    }
    .status.logged-out {
      background-color: #f5f5f5;
      color: #757575;
    }
    .user-info {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
    .user-info p {
      margin: 0;
      font-size: 14px;
    }
    .label {
      font-weight: bold;
      color: #555;
    }
    .value {
      color: #333;
      word-break: break-all;
    }
    .button {
      background-color: #1976d2;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
    }
    .button:hover {
      background-color: #1565c0;
    }
    .button:disabled {
      background-color: #bbdefb;
      cursor: not-allowed;
    }
    .button.secondary {
      background-color: #f5f5f5;
      color: #333;
      border: 1px solid #ddd;
    }
    .button.secondary:hover {
      background-color: #e0e0e0;
    }
    .button-group {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
      margin-top: 10px;
    }
    .footer {
      margin-top: 15px;
      font-size: 12px;
      color: #757575;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>网站同步登录状态</h1>
    </div>
    
    <div id="status-container" class="status">
      <p id="status-message">正在检查登录状态...</p>
    </div>
    
    <div id="user-info" class="user-info" style="display: none;">
      <p>
        <span class="label">用户ID:</span>
        <span id="user-id" class="value">-</span>
      </p>
      <p>
        <span class="label">邮箱:</span>
        <span id="user-email" class="value">-</span>
      </p>
    </div>
    
    <div class="button-group">
      <button id="sync-button" class="button secondary" disabled>同步状态</button>
      <button id="logout-button" class="button" disabled>退出登录</button>
    </div>
    
    <div class="footer">
      <p>此扩展与网站共享登录状态</p>
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html> 
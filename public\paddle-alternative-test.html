<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle替代方案测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .log { max-height: 400px; overflow-y: auto; }
        .token-input { width: 400px; padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Paddle替代方案测试</h1>
        
        <div class="section error">
            <h2>❌ 当前问题</h2>
            <p><strong>JWT获取失败:</strong> "Failed to retrieve JWT"</p>
            <p>这通常表明client-side token权限不足或配置错误。</p>
        </div>

        <div class="section warning">
            <h2>🔧 解决方案</h2>
            <h3>方案1: 重新生成Token (推荐)</h3>
            <ol>
                <li>在Paddle Dashboard中删除当前token</li>
                <li>创建新的client-side token</li>
                <li>确保选择所有必要权限：
                    <ul>
                        <li>checkout:read-write</li>
                        <li>products:read</li>
                        <li>prices:read</li>
                    </ul>
                </li>
                <li>在下面测试新token</li>
            </ol>
            
            <div>
                <label>测试新Token:</label><br>
                <input type="text" id="new-token" class="token-input" placeholder="输入新的client-side token">
                <button onclick="testNewToken()">测试新Token</button>
            </div>
        </div>

        <div class="section info">
            <h2>🧪 Token测试</h2>
            <button onclick="testCurrentToken()">测试当前Token</button>
            <button onclick="testPaddleStatus()">检查Paddle服务状态</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section info">
            <h2>💡 临时解决方案</h2>
            <p>如果token问题持续存在，可以考虑：</p>
            <ul>
                <li><strong>联系Paddle支持</strong> - 可能是账户配置问题</li>
                <li><strong>使用服务端集成</strong> - 通过后端API处理支付</li>
                <li><strong>检查账户状态</strong> - 确认Sandbox账户完全激活</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        let currentToken = 'test_459728092d35d36bc173c60e52d';
        const productId = 'pri_01jxa2azwznm06dqbptzagtm0z';

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        function testCurrentToken() {
            log('🔍 测试当前Token...', 'info');
            testTokenWithPaddle(currentToken);
        }

        function testNewToken() {
            const newToken = document.getElementById('new-token').value.trim();
            if (!newToken) {
                alert('请输入新的Token');
                return;
            }
            
            log(`🔄 测试新Token: ${newToken}`, 'info');
            testTokenWithPaddle(newToken);
        }

        function testTokenWithPaddle(token) {
            try {
                log('🚀 初始化Paddle...', 'info');
                
                // 重置Paddle（如果已初始化）
                if (window.Paddle) {
                    delete window.Paddle;
                    
                    // 重新加载Paddle SDK
                    const oldScript = document.querySelector('script[src*="paddle.js"]');
                    if (oldScript) {
                        oldScript.remove();
                    }
                    
                    const script = document.createElement('script');
                    script.src = 'https://cdn.paddle.com/paddle/v2/paddle.js';
                    script.onload = () => {
                        initializePaddleWithToken(token);
                    };
                    document.head.appendChild(script);
                } else {
                    initializePaddleWithToken(token);
                }
            } catch (error) {
                log(`❌ Token测试失败: ${error.message}`, 'error');
            }
        }

        function initializePaddleWithToken(token) {
            try {
                window.Paddle.Initialize({
                    token: token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            if (data.detail && data.detail.includes('Failed to retrieve JWT')) {
                                log('❌ JWT获取失败 - Token权限不足', 'error');
                                log('💡 建议：重新生成具有完整权限的token', 'warning');
                            } else {
                                log(`❌ Checkout错误: ${data.detail}`, 'error');
                            }
                        } else if (data.type === 'checkout.loaded') {
                            log('✅ Checkout加载成功', 'success');
                        }
                    }
                });

                log('✅ Paddle初始化成功', 'success');
                
                // 尝试打开checkout
                setTimeout(() => {
                    log('💳 尝试打开支付页面...', 'info');
                    
                    window.Paddle.Checkout.open({
                        items: [{
                            priceId: productId,
                            quantity: 1
                        }],
                        customer: {
                            email: '<EMAIL>'
                        }
                    });
                }, 1000);
                
            } catch (error) {
                log(`❌ Paddle初始化失败: ${error.message}`, 'error');
            }
        }

        function testPaddleStatus() {
            log('🌐 检查Paddle服务状态...', 'info');
            
            // 测试Paddle CDN
            fetch('https://cdn.paddle.com/paddle/v2/paddle.js', { method: 'HEAD' })
                .then(response => {
                    log(`Paddle CDN状态: ${response.status}`, response.ok ? 'success' : 'error');
                })
                .catch(error => {
                    log(`Paddle CDN访问失败: ${error.message}`, 'error');
                });

            // 测试Paddle API
            fetch('https://sandbox-api.paddle.com/products', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${currentToken}`,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                log(`Paddle API状态: ${response.status}`, response.ok ? 'success' : 'error');
                if (!response.ok) {
                    return response.text().then(text => {
                        log(`API错误详情: ${text}`, 'error');
                    });
                }
            })
            .catch(error => {
                log(`Paddle API访问失败: ${error.message}`, 'error');
            });
        }

        // 页面加载时的初始化
        window.onload = function() {
            log('🎯 Paddle替代方案测试工具已加载', 'info');
            log('💡 请先尝试重新生成Token，然后测试', 'info');
            log(`当前Token: ${currentToken}`, 'info');
        };
    </script>
</body>
</html>

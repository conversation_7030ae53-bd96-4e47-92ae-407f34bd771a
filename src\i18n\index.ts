import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 导入语言包
import enTranslation from './locales/en';
import zhTranslation from './locales/zh';

// 配置 i18next
i18n
  // 检测用户语言
  .use(LanguageDetector)
  // 将 i18n 实例传递给 react-i18next
  .use(initReactI18next)
  // 初始化 i18next
  .init({
    resources: {
      en: {
        translation: enTranslation
      },
      zh: {
        translation: zhTranslation
      }
    },
    fallbackLng: 'en', // 默认语言
    debug: false, // 关闭调试日志

    interpolation: {
      escapeValue: false, // 不转义 HTML
    },

    // 检测语言的选项
    detection: {
      // 检测顺序
      order: ['querystring', 'cookie', 'localStorage', 'navigator', 'htmlTag'],
      
      // 从 URL 查询参数中获取语言 (例如 ?lng=en)
      lookupQuerystring: 'lng',
      
      // 从 cookie 中获取语言
      lookupCookie: 'i18next',
      
      // 从 localStorage 中获取语言
      lookupLocalStorage: 'i18nextLng',
      
      // 缓存语言到 localStorage
      caches: ['localStorage', 'cookie']
    }
  });

export default i18n; 
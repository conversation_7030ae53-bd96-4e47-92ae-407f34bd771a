import React from 'react';

const RefundPolicyPage: React.FC = () => {
  return (
    <div className="bg-white dark:bg-gray-800 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-extrabold text-gray-900 dark:text-white mb-6">Refund Policy</h1>
        
        <div className="prose prose-lg text-gray-600 dark:text-gray-300 mx-auto">
          <p>Last Updated: {new Date().toLocaleDateString('en-US')}</p>

          <h2 className="text-xl font-bold text-gray-800 dark:text-white mt-8">Overview</h2>
          <p>
            We are committed to providing our customers with the highest quality service. If you are not completely satisfied with our subscription service, we are here to help.
          </p>

          <h2 className="text-xl font-bold text-gray-800 dark:text-white mt-8">Refund Eligibility</h2>
          <p>
            For our annual subscription plans, you may request a full refund within <strong>48 hours</strong> of your initial purchase.
          </p>
          <p>
            To be eligible for a refund, you must meet all of the following conditions:
          </p>
          <ul>
            <li>Your refund request must be submitted within 48 hours of purchasing your subscription.</li>
            <li>You must not have violated our Terms of Service.</li>
          </ul>
          <p>
            After the 48-hour period has elapsed, we will no longer provide refunds. We do not offer refunds for monthly subscriptions.
          </p>

          <h2 className="text-xl font-bold text-gray-800 dark:text-white mt-8">How to Request a Refund</h2>
          <p>
            To request a refund, please contact us through our support email:
            <strong><EMAIL></strong>
          </p>
          <p>
            Please include the following information in your email:
          </p>
          <ul>
            <li>Your account email address.</li>
            <li>The order number or transaction ID used at the time of purchase.</li>
            <li>Reason for the refund (optional, but helps us improve our service).</li>
          </ul>
          <p>
            We will process your refund request within 3-5 business days of receiving it. Refunds will be issued to the original payment method used for the purchase.
          </p>

          <h2 className="text-xl font-bold text-gray-800 dark:text-white mt-8">Contact Us</h2>
          <p>
            If you have any questions about our refund policy, please feel free to <a href="mailto:<EMAIL>" className="text-teal-500 hover:text-teal-600">contact us</a>.
          </p>
        </div>
      </div>
    </div>
  );
};

export default RefundPolicyPage; 
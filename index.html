<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AutoFill React</title>

    <!-- 完全移除CSP以支持Paddle -->
    <!-- CSP已禁用以支持第三方支付服务 -->
    <style>
      /* 初始加载指示器样式 */
      #loading-indicator {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #f9fafb;
        z-index: 9999;
      }
      @media (prefers-color-scheme: dark) {
        #loading-indicator {
          background-color: #111827;
          color: #f9fafb;
        }
      }
      .spinner {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: 5px solid rgba(0, 0, 0, 0.1);
        border-top-color: #3b82f6;
        animation: spin 1s linear infinite;
      }
      @media (prefers-color-scheme: dark) {
        .spinner {
          border-color: rgba(255, 255, 255, 0.1);
          border-top-color: #3b82f6;
        }
      }
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      .loading-text {
        margin-top: 20px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        font-size: 16px;
      }
    </style>
  </head>
  <body>
    <!-- 初始加载指示器 -->
    <div id="loading-indicator">
      <div class="spinner"></div>
      <div class="loading-text">Loading...</div>
    </div>
    
    <div id="root"></div>
    
    <script>
      // 在React应用加载完成后隐藏加载指示器
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingIndicator = document.getElementById('loading-indicator');
          if (loadingIndicator) {
            loadingIndicator.style.opacity = '0';
            loadingIndicator.style.transition = 'opacity 0.5s ease';
            setTimeout(function() {
              loadingIndicator.style.display = 'none';
            }, 500);
          }
        }, 500);
      });
      
      // 如果加载时间过长，显示提示
      setTimeout(function() {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator && loadingIndicator.style.display !== 'none') {
          const loadingText = loadingIndicator.querySelector('.loading-text');
          if (loadingText) {
            loadingText.textContent = 'Loading is taking longer than expected. Please check your network connection...';
          }
        }
      }, 5000);
    </script>
    
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html> 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备ID测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #efe; border-color: #cfc; }
        .error { background-color: #fee; border-color: #fcc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 12px 20px; margin: 8px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        button:hover { background: #005a87; }
        .log { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; font-size: 12px; }
        .feature-box { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .device-id { font-family: monospace; background: #e7f3ff; padding: 10px; border-radius: 3px; word-break: break-all; }
        .test-result { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .test-pass { background: #d4edda; color: #155724; }
        .test-fail { background: #f8d7da; color: #721c24; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .feature-stable { color: #28a745; }
        .feature-unstable { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 设备ID生成测试</h1>
        
        <div class="section info">
            <h2>📋 测试说明</h2>
            <div class="feature-box">
                <p><strong>目标：</strong>验证设备ID的确定性和稳定性</p>
                <ul>
                    <li>✅ 同一设备多次生成ID应该相同</li>
                    <li>✅ 清除localStorage后重新生成应该相同</li>
                    <li>✅ 避免浏览器更新导致ID变化</li>
                    <li>✅ UUID格式符合标准</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🧪 设备ID测试</h2>
            <button onclick="generateNewId()">生成设备ID</button>
            <button onclick="testConsistency()">测试一致性</button>
            <button onclick="clearCacheAndTest()">清除缓存测试</button>
            <button onclick="showFeatures()">显示设备特征</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section" id="current-id-section" style="display: none;">
            <h2>🆔 当前设备ID</h2>
            <div id="current-device-id" class="device-id"></div>
        </div>

        <div class="section" id="features-section" style="display: none;">
            <h2>📊 设备特征详情</h2>
            <table id="features-table">
                <thead>
                    <tr>
                        <th>特征类型</th>
                        <th>特征值</th>
                        <th>稳定性</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody id="features-tbody"></tbody>
            </table>
        </div>

        <div class="section" id="test-results-section" style="display: none;">
            <h2>🧪 测试结果</h2>
            <div id="test-results"></div>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section warning">
            <h2>⚠️ 注意事项</h2>
            <div class="feature-box">
                <ul>
                    <li><strong>隐私保护：</strong>设备ID基于公开的浏览器特征生成，不涉及个人隐私信息</li>
                    <li><strong>稳定性：</strong>正常情况下设备ID保持稳定，硬件升级或系统重装可能导致变化</li>
                    <li><strong>用途限制：</strong>每个设备ID最多允许3个账号登录</li>
                    <li><strong>缓存机制：</strong>使用localStorage缓存提高性能，清除后会重新计算</li>
                </ul>
            </div>
        </div>
    </div>

    <script type="module">
        // 模拟设备ID生成功能（简化版）
        let currentDeviceId = null;
        let deviceFeatures = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '5px';
            logEntry.style.padding = '5px';
            logEntry.style.borderRadius = '3px';
            
            if (type === 'success') {
                logEntry.style.backgroundColor = '#d4edda';
                logEntry.style.color = '#155724';
            } else if (type === 'error') {
                logEntry.style.backgroundColor = '#f8d7da';
                logEntry.style.color = '#721c24';
            } else if (type === 'warning') {
                logEntry.style.backgroundColor = '#fff3cd';
                logEntry.style.color = '#856404';
            } else {
                logEntry.style.backgroundColor = '#d1ecf1';
                logEntry.style.color = '#0c5460';
            }
            
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('current-id-section').style.display = 'none';
            document.getElementById('features-section').style.display = 'none';
            document.getElementById('test-results-section').style.display = 'none';
        }

        // 简化的设备特征收集
        function collectDeviceFeatures() {
            const extractUserAgentCore = (ua) => {
                const core = ua.replace(/\d+\.\d+\.\d+(\.\d+)?/g, 'X.X.X');
                const browserMatch = core.match(/(Chrome|Firefox|Safari|Edge|Opera)/);
                const osMatch = core.match(/(Windows|Mac|Linux|Android|iOS)/i);
                return `${browserMatch?.[1] || 'Unknown'}_${osMatch?.[1] || 'Unknown'}`;
            };

            return {
                screen: `${screen.width}x${screen.height}x${screen.colorDepth}`,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                platform: navigator.platform || 'Unknown',
                hardwareConcurrency: navigator.hardwareConcurrency || 0,
                userAgentCore: extractUserAgentCore(navigator.userAgent),
                language: navigator.language,
                maxTouchPoints: navigator.maxTouchPoints || 0,
                deviceMemory: navigator.deviceMemory || 0
            };
        }

        // 简化的哈希生成
        async function generateHash(features) {
            const featuresString = JSON.stringify(features);
            const encoder = new TextEncoder();
            const data = encoder.encode(featuresString);
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        }

        // 转换为UUID格式
        function hashToUUID(hash) {
            const paddedHash = (hash + '0'.repeat(32)).substring(0, 32);
            return [
                paddedHash.substring(0, 8),
                paddedHash.substring(8, 12),
                '4' + paddedHash.substring(13, 16),
                ((parseInt(paddedHash.substring(16, 17), 16) & 0x3) | 0x8).toString(16) + paddedHash.substring(17, 20),
                paddedHash.substring(20, 32)
            ].join('-');
        }

        // 生成设备ID
        async function generateDeviceId() {
            try {
                log('🔍 收集设备特征...', 'info');
                deviceFeatures = collectDeviceFeatures();
                
                log('🔐 生成设备指纹...', 'info');
                const hash = await generateHash(deviceFeatures);
                
                log('🆔 转换为UUID格式...', 'info');
                const deviceId = hashToUUID(hash);
                
                return deviceId;
            } catch (error) {
                log(`❌ 设备ID生成失败: ${error.message}`, 'error');
                throw error;
            }
        }

        // 显示设备ID
        function showDeviceId(deviceId) {
            document.getElementById('current-device-id').textContent = deviceId;
            document.getElementById('current-id-section').style.display = 'block';
        }

        // 显示设备特征
        function showDeviceFeatures() {
            if (!deviceFeatures) return;

            const tbody = document.getElementById('features-tbody');
            tbody.innerHTML = '';

            const featureInfo = [
                { key: 'screen', name: '屏幕信息', stability: 'stable', desc: '分辨率和色深' },
                { key: 'timezone', name: '时区', stability: 'stable', desc: '系统时区设置' },
                { key: 'platform', name: '平台', stability: 'stable', desc: '操作系统平台' },
                { key: 'hardwareConcurrency', name: 'CPU核心数', stability: 'stable', desc: '处理器核心数量' },
                { key: 'userAgentCore', name: '浏览器核心', stability: 'stable', desc: '浏览器类型（无版本号）' },
                { key: 'language', name: '语言', stability: 'stable', desc: '浏览器语言设置' },
                { key: 'maxTouchPoints', name: '触摸点数', stability: 'stable', desc: '最大同时触摸点' },
                { key: 'deviceMemory', name: '设备内存', stability: 'stable', desc: '设备内存大小（GB）' }
            ];

            featureInfo.forEach(info => {
                const row = tbody.insertRow();
                row.insertCell(0).textContent = info.name;
                row.insertCell(1).textContent = deviceFeatures[info.key];
                const stabilityCell = row.insertCell(2);
                stabilityCell.textContent = info.stability === 'stable' ? '稳定' : '不稳定';
                stabilityCell.className = info.stability === 'stable' ? 'feature-stable' : 'feature-unstable';
                row.insertCell(3).textContent = info.desc;
            });

            document.getElementById('features-section').style.display = 'block';
        }

        // 测试结果显示
        function showTestResults(results) {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '';

            results.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.pass ? 'test-pass' : 'test-fail'}`;
                div.innerHTML = `
                    <strong>${result.pass ? '✅' : '❌'} ${result.name}</strong><br>
                    ${result.description}
                `;
                resultsDiv.appendChild(div);
            });

            document.getElementById('test-results-section').style.display = 'block';
        }

        // 全局函数
        window.generateNewId = async function() {
            try {
                log('🚀 开始生成设备ID...', 'info');
                currentDeviceId = await generateDeviceId();
                log(`✅ 设备ID生成成功: ${currentDeviceId}`, 'success');
                showDeviceId(currentDeviceId);
            } catch (error) {
                log(`❌ 生成失败: ${error.message}`, 'error');
            }
        };

        window.testConsistency = async function() {
            try {
                log('🧪 开始一致性测试...', 'info');
                
                const id1 = await generateDeviceId();
                const id2 = await generateDeviceId();
                const id3 = await generateDeviceId();
                
                const results = [
                    {
                        name: '多次生成一致性',
                        pass: id1 === id2 && id2 === id3,
                        description: `三次生成结果: ${id1 === id2 && id2 === id3 ? '完全一致' : '存在差异'}`
                    },
                    {
                        name: 'UUID格式验证',
                        pass: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id1),
                        description: `格式检查: ${/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id1) ? '符合UUID v4标准' : '格式不正确'}`
                    }
                ];
                
                showTestResults(results);
                currentDeviceId = id1;
                showDeviceId(currentDeviceId);
                
                log('✅ 一致性测试完成', 'success');
            } catch (error) {
                log(`❌ 一致性测试失败: ${error.message}`, 'error');
            }
        };

        window.clearCacheAndTest = async function() {
            try {
                log('🗑️ 清除localStorage缓存...', 'warning');
                localStorage.removeItem('device_fingerprint_id');
                localStorage.removeItem('device_features_cache');
                
                log('🔄 重新生成设备ID...', 'info');
                const newId = await generateDeviceId();
                
                const results = [
                    {
                        name: '缓存清除后一致性',
                        pass: !currentDeviceId || newId === currentDeviceId,
                        description: `清除缓存后: ${!currentDeviceId ? '首次生成' : (newId === currentDeviceId ? 'ID保持一致' : 'ID发生变化')}`
                    }
                ];
                
                showTestResults(results);
                currentDeviceId = newId;
                showDeviceId(currentDeviceId);
                
                log('✅ 缓存清除测试完成', 'success');
            } catch (error) {
                log(`❌ 缓存清除测试失败: ${error.message}`, 'error');
            }
        };

        window.showFeatures = function() {
            if (!deviceFeatures) {
                log('⚠️ 请先生成设备ID', 'warning');
                return;
            }
            showDeviceFeatures();
            log('📊 设备特征已显示', 'info');
        };

        window.clearLog = clearLog;

        // 页面加载时显示说明
        window.onload = function() {
            log('🔍 设备ID测试工具已加载', 'info');
            log('💡 点击"生成设备ID"开始测试', 'info');
        };
    </script>
</body>
</html>

#!/usr/bin/env node

/**
 * Paddle 配置检查脚本
 * 验证不同环境的 Paddle 配置是否正确
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

// 需要检查的环境变量
const requiredPaddleVars = [
  { name: 'VITE_PADDLE_ENVIRONMENT', description: 'Paddle 环境 (sandbox/production)', required: true },
  { name: 'VITE_PADDLE_VENDOR_ID', description: 'Paddle 卖家 ID', required: true },
  { name: 'VITE_PADDLE_PRODUCT_YEARLY', description: 'Paddle 年度产品 ID', required: true },
];

// 检查环境文件
function checkEnvFile(envFile) {
  const filePath = path.join(projectRoot, envFile);

  console.log(`\n📋 检查 ${envFile}:`);

  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${envFile}`);
    console.log(`💡 请创建 ${envFile} 文件并配置 Paddle 相关变量`);
    return false;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const envVars = {};
  
  // 解析环境变量
  lines.forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  let allValid = true;
  
  // 检查必需的变量
  requiredPaddleVars.forEach(({ name, description, required }) => {
    const value = envVars[name];
    if (required && (!value || value.includes('your_') || value.includes('replace_'))) {
      console.log(`❌ ${name}: ${description} - 未正确配置`);
      allValid = false;
    } else if (value) {
      console.log(`✅ ${name}: ${description} - 已配置`);
    } else {
      console.log(`⚠️ ${name}: ${description} - 未设置 (可选)`);
    }
  });
  
  // 验证 Paddle 环境值
  const paddleEnv = envVars['VITE_PADDLE_ENVIRONMENT'];
  if (paddleEnv && !['sandbox', 'production'].includes(paddleEnv)) {
    console.log(`❌ VITE_PADDLE_ENVIRONMENT 值无效: ${paddleEnv} (应为 sandbox 或 production)`);
    allValid = false;
  }
  
  // 验证 Vendor ID 格式
  const vendorId = envVars['VITE_PADDLE_VENDOR_ID'];
  if (vendorId && (isNaN(vendorId) || parseInt(vendorId) <= 0)) {
    console.log(`❌ VITE_PADDLE_VENDOR_ID 格式无效: ${vendorId} (应为正整数)`);
    allValid = false;
  }
  
  // 验证产品 ID 格式
  const productId = envVars['VITE_PADDLE_PRODUCT_YEARLY'];
  if (productId && !productId.startsWith('pri_')) {
    console.log(`❌ VITE_PADDLE_PRODUCT_YEARLY 格式无效: ${productId} (应以 pri_ 开头)`);
    allValid = false;
  }
  
  return allValid;
}

// 主函数
function main() {
  console.log('🔍 Paddle 配置检查工具');
  console.log('====================');

  const envFiles = ['.env.local'];
  let overallValid = true;

  // 读取环境变量
  const envPath = path.join(projectRoot, '.env.local');
  let environment = 'production';
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envMatch = envContent.match(/VITE_PADDLE_ENVIRONMENT=(.+)/);
    if (envMatch) {
      environment = envMatch[1].trim();
    }
  }

  envFiles.forEach(envFile => {
    const isValid = checkEnvFile(envFile);
    if (!isValid) {
      overallValid = false;
    }
  });
  
  console.log('\n📊 检查结果:');
  if (overallValid) {
    console.log('✅ 所有 Paddle 配置检查通过！');
  } else {
    console.log('❌ 发现配置问题，请修复后重试。');
    console.log('\n💡 提示:');
    console.log('1. 从 Paddle 控制台获取正确的 Vendor ID 和 Product ID');
    console.log('2. 确保测试环境使用 sandbox，生产环境使用 production');
    console.log('3. Product ID 应该以 pri_ 开头 (新版 Billing API)');
    process.exit(1);
  }
}

main();

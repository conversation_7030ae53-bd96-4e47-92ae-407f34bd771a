<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扩展功能测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .warning {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
        }
        h1, h2 {
            color: #333;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <h1>AutoFill 扩展功能测试页面</h1>
    
    <div class="instructions">
        <h3>测试说明</h3>
        <p>此页面用于测试 AutoFill 扩展的功能限制。请确保已安装并启用扩展。</p>
        <ul>
            <li><strong>快速填充测试</strong>：右键点击任意输入框，查看预设内容菜单</li>
            <li><strong>网站解析测试</strong>：点击右侧的"解析表单"按钮</li>
            <li><strong>限制测试</strong>：如果是免费用户且数据超出限制，将看到相应提示</li>
        </ul>
    </div>

    <div class="warning">
        <h3>⚠️ 功能限制说明</h3>
        <p><strong>免费版限制：</strong></p>
        <ul>
            <li>最多 3 个分组</li>
            <li>每个分组最多 10 条记录</li>
            <li>最多 3 个网站</li>
            <li>每个网站最多 3 个子网站</li>
            <li>每个子网站最多 10 条规则</li>
        </ul>
        <p><strong>超出限制时：</strong></p>
        <ul>
            <li>右键菜单只显示限制内的预设内容</li>
            <li>网站解析功能将被禁用并提示升级</li>
        </ul>
    </div>

    <div class="form-section">
        <h2>用户信息表单</h2>
        <form>
            <div class="form-group">
                <label for="name">姓名</label>
                <input type="text" id="name" name="name" placeholder="请输入您的姓名">
            </div>
            
            <div class="form-group">
                <label for="email">邮箱</label>
                <input type="email" id="email" name="email" placeholder="请输入您的邮箱地址">
            </div>
            
            <div class="form-group">
                <label for="phone">电话</label>
                <input type="tel" id="phone" name="phone" placeholder="请输入您的电话号码">
            </div>
            
            <div class="form-group">
                <label for="company">公司</label>
                <input type="text" id="company" name="company" placeholder="请输入您的公司名称">
            </div>
            
            <div class="form-group">
                <label for="position">职位</label>
                <input type="text" id="position" name="position" placeholder="请输入您的职位">
            </div>
        </form>
    </div>

    <div class="form-section">
        <h2>地址信息表单</h2>
        <form>
            <div class="form-group">
                <label for="country">国家</label>
                <select id="country" name="country">
                    <option value="">请选择国家</option>
                    <option value="CN">中国</option>
                    <option value="US">美国</option>
                    <option value="JP">日本</option>
                    <option value="KR">韩国</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="province">省份/州</label>
                <input type="text" id="province" name="province" placeholder="请输入省份或州">
            </div>
            
            <div class="form-group">
                <label for="city">城市</label>
                <input type="text" id="city" name="city" placeholder="请输入城市">
            </div>
            
            <div class="form-group">
                <label for="address">详细地址</label>
                <textarea id="address" name="address" placeholder="请输入详细地址"></textarea>
            </div>
            
            <div class="form-group">
                <label for="zipcode">邮政编码</label>
                <input type="text" id="zipcode" name="zipcode" placeholder="请输入邮政编码">
            </div>
        </form>
    </div>

    <div class="form-section">
        <h2>测试功能</h2>
        <p>使用以下按钮测试扩展功能：</p>
        
        <button class="test-button" onclick="testRightClickMenu()">
            测试右键菜单
        </button>
        
        <button class="test-button" onclick="testWebsiteParsing()">
            测试网站解析
        </button>
        
        <button class="test-button" onclick="clearAllInputs()">
            清空所有输入
        </button>
        
        <button class="test-button" onclick="fillSampleData()">
            填充示例数据
        </button>
    </div>

    <script>
        function testRightClickMenu() {
            alert('请右键点击任意输入框查看快速填充菜单。如果是免费用户且数据超出限制，只会显示限制内的预设内容。');
        }

        function testWebsiteParsing() {
            alert('请点击页面右侧的"解析表单"按钮。如果是免费用户且超出限制，将显示升级提示。');
        }

        function clearAllInputs() {
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                if (input.type === 'select-one') {
                    input.selectedIndex = 0;
                } else {
                    input.value = '';
                }
            });
            alert('所有输入框已清空');
        }

        function fillSampleData() {
            document.getElementById('name').value = '张三';
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('phone').value = '13800138000';
            document.getElementById('company').value = '示例公司';
            document.getElementById('position').value = '软件工程师';
            document.getElementById('country').value = 'CN';
            document.getElementById('province').value = '北京市';
            document.getElementById('city').value = '北京市';
            document.getElementById('address').value = '朝阳区示例街道123号';
            document.getElementById('zipcode').value = '100000';
            alert('示例数据已填充');
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('AutoFill 扩展测试页面已加载');
            console.log('请确保已安装并启用 AutoFill 扩展');
        });
    </script>
</body>
</html>

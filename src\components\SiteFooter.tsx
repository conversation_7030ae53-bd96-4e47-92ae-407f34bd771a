import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

export default function SiteFooter() {
  const { t } = useTranslation();

  return (
    <footer className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
      <div className="mx-auto max-w-6xl px-4 py-12">
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
          {/* Column 1: Logo & Description */}
          <div className="lg:col-span-1">
            <div className="mb-4 flex items-center">
              <span className="text-2xl font-bold text-teal-500">AF</span>
              <span className="ml-2 text-2xl font-bold text-gray-800 dark:text-gray-100">
                AutoFill
              </span>
            </div>
            <p className="text-gray-500 dark:text-gray-400 text-sm leading-relaxed">
              {t('footer.description')}
            </p>
          </div>

          {/* Column 2: Services */}
          <div>
            <h5 className="mb-2 font-medium text-pink-500 dark:text-pink-400 text-xs">
              {t('footer.services')}
            </h5>
            <ul className="space-y-1.5">
              <li>
                <a href="/#" className="text-gray-400 hover:text-blue-500 dark:hover:text-blue-300 text-xs">
                  {t('footer.contactUs')}
                </a>
              </li>
              <li>
                <a href="/#" className="text-gray-400 hover:text-blue-500 dark:hover:text-blue-300 text-xs">
                  {t('footer.changelog')}
                </a>
              </li>
            </ul>
          </div>

          {/* Column 3: About */}
          <div>
            <h5 className="mb-2 font-medium text-pink-500 dark:text-pink-400 text-xs">
              {t('footer.about')}
            </h5>
            <ul className="space-y-1.5">
              <li>
                <Link to="/user-agreement" className="text-gray-400 hover:text-blue-500 dark:hover:text-blue-300 text-xs">
                  {t('footer.userAgreement')}
                </Link>
              </li>
              <li>
                <Link to="/privacy-policy" className="text-gray-400 hover:text-blue-500 dark:hover:text-blue-300 text-xs">
                  {t('footer.privacyPolicy')}
                </Link>
              </li>
              <li>
                <Link to="/refund-policy" className="text-gray-400 hover:text-blue-500 dark:hover:text-blue-300 text-xs">
                  {t('footer.refundPolicy')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Column 4: Follow Us */}
          <div>
            <h5 className="mb-2 font-medium text-pink-500 dark:text-pink-400 text-xs">
              {t('footer.followUs')}
            </h5>
            <div className="flex space-x-3">
              <a href="/#" aria-label="Website" title="Website">
                <svg className="h-5 w-5 text-gray-400 hover:text-blue-500 dark:hover:text-blue-300" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2ZM11 19.93A8.001 8.001 0 0 1 11 4.07v15.86ZM13 4.07A8.001 8.001 0 0 1 13 19.93V4.07Z M4.068 13a8.003 8.003 0 0 1 15.864 0H4.068Z"></path></svg>
              </a>
              <a href="/#" aria-label="Twitter" title="Twitter">
                <svg className="h-5 w-5 text-gray-400 hover:text-blue-500 dark:hover:text-blue-300" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M22.46,6C21.69,6.35 20.86,6.58 20,6.67C20.94,6.1 21.66,5.24 22,4.2C21.19,4.69 20.28,5.08 19.31,5.26C18.5,4.49 17.33,4 16,4C13.67,4 11.77,5.89 11.77,8.22C11.77,8.56 11.81,8.9 11.89,9.22C8.23,9.04 5.04,7.29 2.96,4.65C2.58,5.28 2.37,6.03 2.37,6.84C2.37,8.33 3.12,9.62 4.28,10.38C3.59,10.36 2.95,10.17 2.38,9.85V9.9C2.38,11.98 3.85,13.73 5.88,14.15C5.53,14.24 5.16,14.29 4.78,14.29C4.52,14.29 4.27,14.26 4.02,14.21C4.57,15.93 6.15,17.18 8.06,17.22C6.6,18.35 4.76,19.03 2.78,19.03C2.43,19.03 2.09,19.01 1.75,18.95C3.69,20.22 5.97,21 8.5,21C16.29,21 20.74,14.31 20.74,8.77C20.74,8.59 20.73,8.42 20.72,8.24C21.55,7.64 22.09,6.87 22.46,6Z"></path></svg>
              </a>
              <a href="/#" aria-label="Instagram" title="Instagram">
                <svg className="h-5 w-5 text-gray-400 hover:text-blue-500 dark:hover:text-blue-300" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12,2C8.74,2 8.33,2.02 7.32,2.07C6.3,2.12 5.5,2.32 4.88,2.59C4.26,2.86 3.71,3.27 3.27,3.71C2.83,4.15 2.42,4.7 2.15,5.32C1.88,5.94 1.68,6.74 1.63,7.76C1.58,8.78 1.56,9.19 1.56,12.44C1.56,15.69 1.58,16.1 1.63,17.12C1.68,18.14 1.88,18.94 2.15,19.56C2.42,20.18 2.83,20.73 3.27,21.17C3.71,21.61 4.26,22.02 4.88,22.29C5.5,22.56 6.3,22.76 7.32,22.81C8.33,22.86 8.74,22.88 12,22.88C15.26,22.88 15.67,22.86 16.68,22.81C17.7,22.76 18.5,22.56 19.12,22.29C19.74,22.02 20.29,21.61 20.73,21.17C21.17,20.73 21.58,20.18 21.85,19.56C22.12,18.94 22.32,18.14 22.37,17.12C22.42,16.1 22.44,15.69 22.44,12.44C22.44,9.19 22.42,8.78 22.37,7.76C22.32,6.74 22.12,5.94 21.85,5.32C21.58,4.7 21.17,4.15 20.73,3.71C20.29,3.27 19.74,2.86 19.12,2.59C18.5,2.32 17.7,2.12 16.68,2.07C15.67,2.02 15.26,2 12,2M12,3.67C15.19,3.67 15.58,3.69 16.5,3.73C17.34,3.77 17.91,3.92 18.32,4.08C18.82,4.27 19.21,4.5 19.54,4.84C19.88,5.18 20.11,5.57 20.3,6.07C20.46,6.48 20.61,7.05 20.65,7.89C20.69,8.81 20.71,9.2 20.71,12.38C20.71,15.56 20.69,15.95 20.65,16.87C20.61,17.71 20.46,18.28 20.3,18.69C20.11,19.19 19.88,19.58 19.54,19.92C19.21,20.26 18.82,20.49 18.32,20.68C17.91,20.84 17.34,20.99 16.5,21.03C15.58,21.07 15.19,21.09 12,21.09C8.81,21.09 8.42,21.07 7.5,21.03C6.66,20.99 6.09,20.84 5.68,20.68C5.18,20.49 4.79,20.26 4.46,19.92C4.12,19.58 3.89,19.19 3.7,18.69C3.54,18.28 3.39,17.71 3.35,16.87C3.31,15.95 3.29,15.56 3.29,12.38C3.29,9.2 3.31,8.81 3.35,7.89C3.39,7.05 3.54,6.48 3.7,6.07C3.89,5.57 4.12,5.18 4.46,4.84C4.79,4.5 5.18,4.27 5.68,4.08C6.09,3.92 6.66,3.77 7.5,3.73C8.42,3.69 8.81,3.67 12,3.67M12,7.19C9.34,7.19 7.19,9.34 7.19,12C7.19,14.66 9.34,16.81 12,16.81C14.66,16.81 16.81,14.66 16.81,12C16.81,9.34 14.66,7.19 12,7.19M12,15.14C10.28,15.14 8.86,13.72 8.86,12C8.86,10.28 10.28,8.86 12,8.86C13.72,8.86 15.14,10.28 15.14,12C15.14,13.72 13.72,15.14 12,15.14M16.88,7.64C16.88,8.16 16.46,8.58 15.94,8.58C15.42,8.58 15,8.16 15,7.64C15,7.12 15.42,6.7 15.94,6.7C16.46,6.7 16.88,7.12 16.88,7.64Z"></path></svg>
              </a>
            </div>
          </div>
        </div>

        {/* Bottom Copyright */}
        <div className="mt-4 border-t border-gray-200 pt-2 text-center text-xs text-gray-400 dark:border-gray-600 dark:text-gray-500">
          <p className="pb-3">
            {t('footer.copyright')}
          </p>
        </div>
      </div>
    </footer>
  );
} 
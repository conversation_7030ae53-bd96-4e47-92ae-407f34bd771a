<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存储调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .missing { background-color: #f8d7da; padding: 5px; border-radius: 3px; }
        .present { background-color: #d4edda; padding: 5px; border-radius: 3px; }
        .encrypted { background-color: #cce5ff; padding: 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 存储调试工具</h1>
        <p>检查和修复存储中的认证数据问题</p>
        
        <button onclick="checkStorage()">检查存储状态</button>
        <button onclick="simulateLogin()">模拟完整登录数据</button>
        <button onclick="simulateLoginNoEndDate()">模拟登录（无到期时间）</button>
        <button onclick="simulateLoginInactive()">模拟登录（订阅已取消）</button>
        <button onclick="fixMissingTokens()">修复缺失的令牌</button>
        <button onclick="clearStorage()">清除所有数据</button>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        // 加密类
        class SecureStorageEncryption {
            static SENSITIVE_KEYS = ['access_token', 'refresh_token', 'user_id'];
            static encryptionKey = null;

            static generateDeviceKey() {
                if (this.encryptionKey) return this.encryptionKey;
                try {
                    const fingerprint = [
                        navigator.userAgent,
                        navigator.language,
                        `${screen.width}x${screen.height}`,
                        new Date().getTimezoneOffset().toString(),
                        'autofill-secure-key-v1'
                    ].join('|');
                    this.encryptionKey = btoa(fingerprint).replace(/[^a-zA-Z0-9]/g, '').slice(0, 32).padEnd(32, 'A');
                    return this.encryptionKey;
                } catch (error) {
                    this.encryptionKey = 'AutoFillSecureStorageDefaultKey32';
                    return this.encryptionKey;
                }
            }

            static isSensitive(key) {
                return this.SENSITIVE_KEYS.includes(key);
            }

            static encrypt(data) {
                if (!data) return data;
                try {
                    const key = this.generateDeviceKey();
                    const encrypted = data.split('').map((char, i) => {
                        const keyChar = key.charCodeAt(i % key.length);
                        const dataChar = char.charCodeAt(0);
                        return String.fromCharCode(dataChar ^ keyChar);
                    }).join('');
                    return 'ENCRYPTED:' + btoa(encrypted);
                } catch (error) {
                    console.error('加密失败:', error);
                    return data;
                }
            }

            static decrypt(encryptedData) {
                if (!encryptedData || !encryptedData.startsWith('ENCRYPTED:')) {
                    return encryptedData;
                }
                try {
                    const key = this.generateDeviceKey();
                    const base64Data = encryptedData.replace('ENCRYPTED:', '');
                    const encrypted = atob(base64Data);
                    const decrypted = encrypted.split('').map((char, i) => {
                        const keyChar = key.charCodeAt(i % key.length);
                        const encryptedChar = char.charCodeAt(0);
                        return String.fromCharCode(encryptedChar ^ keyChar);
                    }).join('');
                    return decrypted;
                } catch (error) {
                    console.error('解密失败:', error);
                    return encryptedData;
                }
            }
        }

        function checkStorage() {
            const resultDiv = document.getElementById('result');
            const expectedKeys = [
                'access_token',
                'refresh_token', 
                'user_id',
                'user_email',
                'subscription_info',
                'access_token_expires_at',
                'token_type'
            ];
            
            let result = '<span class="info">📊 存储状态检查报告</span>\n\n';
            
            expectedKeys.forEach(key => {
                const value = localStorage.getItem(key);
                const isSensitive = SecureStorageEncryption.isSensitive(key);
                
                if (value) {
                    const isEncrypted = value.startsWith('ENCRYPTED:');
                    let status = '';
                    let className = '';
                    
                    if (isSensitive) {
                        if (isEncrypted) {
                            status = '🔒 已加密';
                            className = 'encrypted';
                        } else {
                            status = '⚠️ 应加密但未加密';
                            className = 'warning';
                        }
                    } else {
                        status = '📝 明文存储';
                        className = 'present';
                    }
                    
                    result += `<span class="${className}">${key}: ${status}</span>\n`;
                    
                    // 显示值的预览
                    let preview = value;
                    if (isEncrypted && isSensitive) {
                        try {
                            const decrypted = SecureStorageEncryption.decrypt(value);
                            preview = `${value.substring(0, 30)}... (解密: ${decrypted.substring(0, 20)}...)`;
                        } catch (e) {
                            preview = `${value.substring(0, 30)}... (解密失败)`;
                        }
                    } else {
                        preview = value.length > 50 ? value.substring(0, 50) + '...' : value;
                    }
                    result += `  值: ${preview}\n\n`;
                } else {
                    result += `<span class="missing">${key}: ❌ 缺失</span>\n\n`;
                }
            });
            
            // 统计
            const totalKeys = expectedKeys.length;
            const presentKeys = expectedKeys.filter(key => localStorage.getItem(key)).length;
            const missingKeys = totalKeys - presentKeys;
            
            result += `\n<span class="info">📈 统计信息:</span>\n`;
            result += `总计: ${totalKeys} 个键\n`;
            result += `存在: ${presentKeys} 个\n`;
            result += `缺失: ${missingKeys} 个\n`;
            
            if (missingKeys > 0) {
                result += `\n<span class="error">⚠️ 发现 ${missingKeys} 个缺失的键，这可能导致应用功能异常</span>`;
            } else {
                result += `\n<span class="success">✅ 所有必要的键都存在</span>`;
            }
            
            resultDiv.innerHTML = result;
        }

        function simulateLogin() {
            const resultDiv = document.getElementById('result');
            
            // 模拟完整的登录响应数据
            const mockLoginData = {
                access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************.mjpXcMUsoV14vNTnUnf0WJTZCd+DFHQzNdFCq8One9U=',
                refresh_token: '33ebce89-5e4f-4393-978e-5a8ecf0fc70b',
                token_type: 'Bearer',
                access_token_expires_in: 31536000,
                user_id: 'e00a7952-e549-4df1-95a8-0cd63bb2504c',
                email: '<EMAIL>'
            };
            
            let result = '<span class="info">🧪 模拟完整登录数据</span>\n\n';
            
            try {
                // 保存令牌数据（加密敏感数据）
                const storageData = {
                    'access_token': mockLoginData.access_token,
                    'refresh_token': mockLoginData.refresh_token,
                    'token_type': mockLoginData.token_type,
                    'access_token_expires_at': String(Date.now() + mockLoginData.access_token_expires_in * 1000),
                    'user_id': mockLoginData.user_id,
                    'user_email': mockLoginData.email,
                    'subscription_info': JSON.stringify({
                        user_id: mockLoginData.user_id,
                        subscription_id: 'sub_mock_annual_1a2b3c4d5e6f',
                        status: 'active',
                        plan_id: 'price_mock_annual_abcdef123456',
                        cancel_at_period_end: false,
                        current_period_start: '2025-06-22T10:00:00Z',
                        current_period_end: '2027-06-23T10:00:00Z'
                    })
                };
                
                Object.entries(storageData).forEach(([key, value]) => {
                    if (SecureStorageEncryption.isSensitive(key)) {
                        const encrypted = SecureStorageEncryption.encrypt(value);
                        localStorage.setItem(key, encrypted);
                        result += `<span class="success">✅ ${key}: 已加密存储</span>\n`;
                    } else {
                        localStorage.setItem(key, value);
                        result += `<span class="info">📝 ${key}: 已明文存储</span>\n`;
                    }
                });
                
                result += '\n<span class="success">🎉 模拟登录数据创建完成！请刷新主应用查看效果。</span>';
                
            } catch (error) {
                result += `<span class="error">❌ 模拟登录失败: ${error.message}</span>`;
            }
            
            resultDiv.innerHTML = result;
        }

        function simulateLoginNoEndDate() {
            const resultDiv = document.getElementById('result');

            // 模拟有订阅但没有到期时间的登录数据
            const mockLoginData = {
                access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************.mjpXcMUsoV14vNTnUnf0WJTZCd+DFHQzNdFCq8One9U=',
                refresh_token: '33ebce89-5e4f-4393-978e-5a8ecf0fc70b',
                token_type: 'Bearer',
                access_token_expires_in: 31536000,
                user_id: 'e00a7952-e549-4df1-95a8-0cd63bb2504c',
                email: '<EMAIL>'
            };

            let result = '<span class="info">🧪 模拟登录数据（无到期时间）</span>\n\n';

            try {
                // 保存令牌数据（加密敏感数据）
                const storageData = {
                    'access_token': mockLoginData.access_token,
                    'refresh_token': mockLoginData.refresh_token,
                    'token_type': mockLoginData.token_type,
                    'access_token_expires_at': String(Date.now() + mockLoginData.access_token_expires_in * 1000),
                    'user_id': mockLoginData.user_id,
                    'user_email': mockLoginData.email,
                    'subscription_info': JSON.stringify({
                        user_id: mockLoginData.user_id,
                        subscription_id: 'sub_mock_annual_1a2b3c4d5e6f',
                        status: 'active',
                        plan_id: 'price_mock_annual_abcdef123456',
                        cancel_at_period_end: false,
                        current_period_start: '2025-06-22T10:00:00Z'
                        // 注意：这里没有 current_period_end
                    })
                };

                Object.entries(storageData).forEach(([key, value]) => {
                    if (SecureStorageEncryption.isSensitive(key)) {
                        const encrypted = SecureStorageEncryption.encrypt(value);
                        localStorage.setItem(key, encrypted);
                        result += `<span class="success">✅ ${key}: 已加密存储</span>\n`;
                    } else {
                        localStorage.setItem(key, value);
                        result += `<span class="info">📝 ${key}: 已明文存储</span>\n`;
                    }
                });

                result += '\n<span class="success">🎉 模拟登录数据（无到期时间）创建完成！请刷新主应用查看效果。</span>';

            } catch (error) {
                result += `<span class="error">❌ 模拟登录失败: ${error.message}</span>`;
            }

            resultDiv.innerHTML = result;
        }

        function simulateLoginInactive() {
            const resultDiv = document.getElementById('result');

            // 模拟有订阅但状态为cancelled的登录数据
            const mockLoginData = {
                access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************.mjpXcMUsoV14vNTnUnf0WJTZCd+DFHQzNdFCq8One9U=',
                refresh_token: '33ebce89-5e4f-4393-978e-5a8ecf0fc70b',
                token_type: 'Bearer',
                access_token_expires_in: 31536000,
                user_id: 'e00a7952-e549-4df1-95a8-0cd63bb2504c',
                email: '<EMAIL>'
            };

            let result = '<span class="info">🧪 模拟登录数据（订阅已取消）</span>\n\n';

            try {
                // 保存令牌数据（加密敏感数据）
                const storageData = {
                    'access_token': mockLoginData.access_token,
                    'refresh_token': mockLoginData.refresh_token,
                    'token_type': mockLoginData.token_type,
                    'access_token_expires_at': String(Date.now() + mockLoginData.access_token_expires_in * 1000),
                    'user_id': mockLoginData.user_id,
                    'user_email': mockLoginData.email,
                    'subscription_info': JSON.stringify({
                        user_id: mockLoginData.user_id,
                        subscription_id: 'sub_mock_annual_1a2b3c4d5e6f',
                        status: 'cancelled', // 注意：状态为cancelled
                        plan_id: 'price_mock_annual_abcdef123456',
                        cancel_at_period_end: true,
                        current_period_start: '2025-06-22T10:00:00Z',
                        current_period_end: '2025-12-22T10:00:00Z'
                    })
                };

                Object.entries(storageData).forEach(([key, value]) => {
                    if (SecureStorageEncryption.isSensitive(key)) {
                        const encrypted = SecureStorageEncryption.encrypt(value);
                        localStorage.setItem(key, encrypted);
                        result += `<span class="success">✅ ${key}: 已加密存储</span>\n`;
                    } else {
                        localStorage.setItem(key, value);
                        result += `<span class="info">📝 ${key}: 已明文存储</span>\n`;
                    }
                });

                result += '\n<span class="success">🎉 模拟登录数据（订阅已取消）创建完成！请刷新主应用查看效果。</span>';

            } catch (error) {
                result += `<span class="error">❌ 模拟登录失败: ${error.message}</span>`;
            }

            resultDiv.innerHTML = result;
        }

        function fixMissingTokens() {
            const resultDiv = document.getElementById('result');
            
            // 检查当前存储的用户信息
            const userEmail = localStorage.getItem('user_email');
            const userId = localStorage.getItem('user_id');
            
            if (!userEmail || !userId) {
                resultDiv.innerHTML = '<span class="error">❌ 无法修复：缺少用户基本信息</span>';
                return;
            }
            
            let result = '<span class="info">🔧 修复缺失的令牌数据</span>\n\n';
            
            // 生成模拟令牌（基于现有用户信息）
            const mockTokens = {
                access_token: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.${btoa(JSON.stringify({
                    sub: userId.startsWith('ENCRYPTED:') ? SecureStorageEncryption.decrypt(userId) : userId,
                    email: userEmail,
                    iat: Math.floor(Date.now() / 1000),
                    exp: Math.floor(Date.now() / 1000) + 31536000
                }))}.mock-signature`,
                refresh_token: `refresh-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                token_type: 'Bearer',
                access_token_expires_at: String(Date.now() + 31536000 * 1000)
            };
            
            Object.entries(mockTokens).forEach(([key, value]) => {
                if (!localStorage.getItem(key)) {
                    if (SecureStorageEncryption.isSensitive(key)) {
                        const encrypted = SecureStorageEncryption.encrypt(value);
                        localStorage.setItem(key, encrypted);
                        result += `<span class="success">✅ ${key}: 已生成并加密存储</span>\n`;
                    } else {
                        localStorage.setItem(key, value);
                        result += `<span class="info">📝 ${key}: 已生成并存储</span>\n`;
                    }
                } else {
                    result += `<span class="warning">⚠️ ${key}: 已存在，跳过</span>\n`;
                }
            });
            
            result += '\n<span class="success">🎉 令牌修复完成！请刷新主应用查看效果。</span>';
            resultDiv.innerHTML = result;
        }

        function clearStorage() {
            const keys = ['access_token', 'refresh_token', 'user_id', 'user_email', 'subscription_info', 'access_token_expires_at', 'token_type'];
            keys.forEach(key => localStorage.removeItem(key));
            document.getElementById('result').innerHTML = '<span class="success">🗑️ 所有数据已清除</span>';
        }

        // 页面加载时自动检查
        window.addEventListener('load', checkStorage);
    </script>
</body>
</html>

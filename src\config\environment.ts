/**
 * 环境配置
 * 用于控制不同环境下的行为
 */

// 检查是否为生产环境
export const isProduction = import.meta.env.PROD || import.meta.env.NODE_ENV === 'production';

// 检查是否为开发环境
export const isDevelopment = import.meta.env.DEV || import.meta.env.NODE_ENV === 'development';

// 检查是否启用调试模式
export const isDebugEnabled = !isProduction && (
  import.meta.env.VITE_DEBUG === 'true' || 
  localStorage.getItem('debug_mode') === 'true'
);

// 日志级别配置
export const LOG_LEVELS = {
  ERROR: 0,   // 错误日志 - 生产环境显示
  WARN: 1,    // 警告日志 - 生产环境显示
  INFO: 2,    // 信息日志 - 仅开发环境显示
  DEBUG: 3    // 调试日志 - 仅开发环境显示
} as const;

// 当前日志级别
export const currentLogLevel = isProduction ? LOG_LEVELS.WARN : LOG_LEVELS.DEBUG;

/**
 * 安全的日志输出函数
 * 在生产环境中自动过滤敏感信息
 */
export const safeLog = {
  error: (...args: any[]) => {
    if (currentLogLevel >= LOG_LEVELS.ERROR) {
      console.error('[ERROR]', ...args);
    }
  },
  
  warn: (...args: any[]) => {
    if (currentLogLevel >= LOG_LEVELS.WARN) {
      console.warn('[WARN]', ...args);
    }
  },
  
  info: (...args: any[]) => {
    if (currentLogLevel >= LOG_LEVELS.INFO) {
      console.info('[INFO]', ...args);
    }
  },
  
  debug: (...args: any[]) => {
    if (currentLogLevel >= LOG_LEVELS.DEBUG) {
      console.log('[DEBUG]', ...args);
    }
  }
};

/**
 * 生产环境优化配置
 */
export const PRODUCTION_CONFIG = {
  // 禁用详细日志
  DISABLE_VERBOSE_LOGGING: isProduction,
  
  // 禁用调试工具
  DISABLE_DEBUG_TOOLS: isProduction,
  
  // 禁用性能监控日志
  DISABLE_PERFORMANCE_LOGS: isProduction,
  
  // 禁用 API 调用详细日志
  DISABLE_API_LOGS: isProduction,
  
  // 禁用存储操作详细日志
  DISABLE_STORAGE_LOGS: isProduction,
  
  // 禁用用户操作日志
  DISABLE_USER_LOGS: isProduction
};

/**
 * 开发环境调试工具
 */
export const devTools = {
  /**
   * 启用调试模式
   */
  enableDebug: () => {
    if (!isProduction) {
      localStorage.setItem('debug_mode', 'true');
      console.log('🔧 调试模式已启用，请刷新页面');
    }
  },

  /**
   * 禁用调试模式
   */
  disableDebug: () => {
    localStorage.removeItem('debug_mode');
    console.log('🔒 调试模式已禁用，请刷新页面');
  },

  /**
   * 检查调试状态
   */
  isDebugEnabled: () => isDebugEnabled,

  /**
   * 检查生产环境状态
   */
  isProduction: () => isProduction,

  /**
   * 获取环境信息
   */
  getEnvironmentInfo: () => ({
    isProduction,
    isDevelopment,
    isDebugEnabled,
    currentLogLevel,
    nodeEnv: import.meta.env.NODE_ENV,
    mode: import.meta.env.MODE
  })
};

// 在开发环境中将调试工具暴露到全局
if (!isProduction && typeof window !== 'undefined') {
  (window as any).devTools = devTools;
  (window as any).environmentInfo = devTools.getEnvironmentInfo();
}

export default {
  isProduction,
  isDevelopment,
  isDebugEnabled,
  currentLogLevel,
  safeLog,
  PRODUCTION_CONFIG,
  devTools
};

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle API 直接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .log { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Paddle API 直接测试</h1>
        
        <div class="section">
            <h2>📋 配置信息</h2>
            <pre id="config-display"></pre>
        </div>

        <div class="section">
            <h2>🧪 API 测试</h2>
            <button onclick="testProductAPI()">测试产品API</button>
            <button onclick="testPriceAPI()">测试价格API</button>
            <button onclick="testTokenValidation()">测试Token验证</button>
            <button onclick="clearResults()">清空结果</button>
        </div>

        <div class="section">
            <h2>📝 测试结果</h2>
            <div id="test-results" class="log"></div>
        </div>
    </div>

    <script>
        const config = {
            token: 'test_8064b08184b64e6031ae8c7dd5a',
            productId: 'pri_01jxa2azwznm06dqbptzagtm0z',
            vendorId: '32634',
            environment: 'sandbox'
        };

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            resultsDiv.appendChild(logEntry);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        function showConfig() {
            document.getElementById('config-display').textContent = JSON.stringify(config, null, 2);
        }

        // 测试产品API
        async function testProductAPI() {
            log('🛍️ 测试产品API...', 'info');
            
            try {
                const response = await fetch('https://sandbox-api.paddle.com/products', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${config.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log(`产品API响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`找到 ${data.data?.length || 0} 个产品`, 'success');
                    log(`产品数据: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    const errorText = await response.text();
                    log(`产品API错误: ${errorText}`, 'error');
                }
            } catch (error) {
                log(`产品API请求失败: ${error.message}`, 'error');
            }
        }

        // 测试价格API
        async function testPriceAPI() {
            log('💰 测试价格API...', 'info');
            
            try {
                const response = await fetch(`https://sandbox-api.paddle.com/prices/${config.productId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${config.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log(`价格API响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`价格信息: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    log(`价格API错误: ${errorText}`, 'error');
                    
                    if (response.status === 404) {
                        log('❌ 价格ID不存在，请检查产品配置', 'error');
                    }
                }
            } catch (error) {
                log(`价格API请求失败: ${error.message}`, 'error');
            }
        }

        // 测试Token验证
        async function testTokenValidation() {
            log('🔑 测试Token验证...', 'info');
            
            // 检查token格式
            if (!config.token.startsWith('test_')) {
                log('❌ Token格式错误：应该以 test_ 开头', 'error');
                return;
            }
            
            if (config.token.length !== 30) {
                log(`⚠️ Token长度异常：当前${config.token.length}字符，标准应该是30字符`, 'error');
            }
            
            try {
                // 尝试获取账户信息来验证token
                const response = await fetch('https://sandbox-api.paddle.com/notifications', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${config.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log(`Token验证响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.status === 401) {
                    log('❌ Token无效或已过期', 'error');
                } else if (response.status === 403) {
                    log('❌ Token权限不足', 'error');
                } else if (response.ok) {
                    log('✅ Token验证成功', 'success');
                } else {
                    const errorText = await response.text();
                    log(`Token验证错误: ${errorText}`, 'error');
                }
            } catch (error) {
                log(`Token验证请求失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示配置
        window.onload = function() {
            showConfig();
            log('🎯 Paddle API 测试工具已加载', 'info');
            log('💡 请按顺序点击测试按钮进行API验证', 'info');
        };
    </script>
</body>
</html>

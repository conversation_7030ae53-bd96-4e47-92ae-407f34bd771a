import React from 'react';
import { useTranslation } from 'react-i18next';

// Feature icon components
const QuickFillIcon = () => (
  <svg className="w-8 h-8 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
  </svg>
);

const SiteFillIcon = () => (
  <svg className="w-8 h-8 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
  </svg>
);

const ManagementIcon = () => (
  <svg className="w-8 h-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
  </svg>
);

const SecurityIcon = () => (
  <svg className="w-8 h-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
  </svg>
);

const CompatibilityIcon = () => (
  <svg className="w-8 h-8 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
  </svg>
);

// Feature card component
const FeatureCard = ({ 
  title, 
  description, 
  features, 
  icon,
  isReversed = false,
  bgColor = 'bg-white'
}: { 
  title: string, 
  description: string, 
  features: string[], 
  icon: React.ReactNode,
  isReversed?: boolean,
  bgColor?: string
}) => {
  return (
    <div className={`${bgColor} dark:bg-gray-800 rounded-xl shadow-md overflow-hidden mb-12`}>
      <div className={`flex flex-col ${isReversed ? 'md:flex-row-reverse' : 'md:flex-row'} items-center`}>
        <div className="md:w-1/2 p-8 flex justify-center">
          <div className="w-full max-w-md flex flex-col items-center md:items-start">
            <div className="p-3 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
              {icon}
            </div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">{title}</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 text-center md:text-left">{description}</p>
            <ul className="space-y-2 w-full">
              {features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="md:w-1/2 bg-gray-50 dark:bg-gray-900 p-8 flex items-center justify-center">
          <div className="w-64 h-64 rounded-xl bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
            <span className="text-gray-400 dark:text-gray-500 text-sm">Feature illustration</span>
          </div>
        </div>
      </div>
    </div>
  );
};

const FeaturesPage: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <div className="bg-slate-50 dark:bg-slate-900 py-12 md:py-20">
      <div className="mx-auto max-w-7xl px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl md:text-5xl">
            {t('features.title')}
          </h1>
          <p className="mt-4 text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {t('features.subtitle')}
          </p>
          <p className="mt-6 text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {t('features.description')}
          </p>
        </div>
        
        {/* Feature Cards */}
        <div className="mt-12">
          <FeatureCard 
            title={t('features.quickFill.title')}
            description={t('features.quickFill.description')}
            features={t('features.quickFill.features', { returnObjects: true }) as string[]}
            icon={<QuickFillIcon />}
            bgColor="bg-teal-50"
          />
          
          <FeatureCard 
            title={t('features.siteFill.title')}
            description={t('features.siteFill.description')}
            features={t('features.siteFill.features', { returnObjects: true }) as string[]}
            icon={<SiteFillIcon />}
            isReversed={true}
            bgColor="bg-purple-50"
          />
          
          <FeatureCard 
            title={t('features.management.title')}
            description={t('features.management.description')}
            features={t('features.management.features', { returnObjects: true }) as string[]}
            icon={<ManagementIcon />}
            bgColor="bg-blue-50"
          />
          
          <FeatureCard 
            title={t('features.security.title')}
            description={t('features.security.description')}
            features={t('features.security.features', { returnObjects: true }) as string[]}
            icon={<SecurityIcon />}
            isReversed={true}
            bgColor="bg-red-50"
          />
          
          <FeatureCard 
            title={t('features.compatibility.title')}
            description={t('features.compatibility.description')}
            features={t('features.compatibility.features', { returnObjects: true }) as string[]}
            icon={<CompatibilityIcon />}
            bgColor="bg-orange-50"
          />
        </div>
        
        {/* Footer CTA */}
        <div className="mt-12 text-center">
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            {t('features.content')}
          </p>
          <a 
            href="/" 
            className="inline-block bg-teal-500 text-white px-8 py-3 rounded-md font-medium hover:bg-teal-600 transition-colors"
          >
            {t('home.getStarted')}
          </a>
        </div>
      </div>
    </div>
  );
};

export default FeaturesPage; 
/**
 * 存储适配器服务
 * 提供统一的接口同时支持 localStorage 和 chrome.storage API
 * 支持敏感数据加密存储
 */

// 防止存储事件循环的标志
let inUpdate = false;

/**
 * 安全存储加密器
 * 用于敏感数据的加密和解密
 */
class SecureStorageEncryption {
  // 定义敏感数据键名
  private static readonly SENSITIVE_KEYS = [
    'access_token',
    'refresh_token',
    'user_id',
    'user_email'
  ];

  // 加密密钥（基于设备指纹生成）
  private static encryptionKey: string | null = null;

  /**
   * 生成基于设备的加密密钥
   */
  private static generateDeviceKey(): string {
    if (this.encryptionKey) {
      return this.encryptionKey;
    }

    try {
      // 基于设备特征生成唯一密钥
      const fingerprint = [
        typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
        typeof navigator !== 'undefined' ? navigator.language : 'en',
        typeof screen !== 'undefined' ? `${screen.width}x${screen.height}` : '1920x1080',
        typeof window !== 'undefined' ? new Date().getTimezoneOffset().toString() : '0',
        'autofill-secure-key-v1' // 应用标识
      ].join('|');

      // 生成32位密钥
      this.encryptionKey = btoa(fingerprint).replace(/[^a-zA-Z0-9]/g, '').slice(0, 32).padEnd(32, 'A');
      return this.encryptionKey;
    } catch (error) {
      this.encryptionKey = 'AutoFillSecureStorageDefaultKey32';
      return this.encryptionKey;
    }
  }

  /**
   * 判断是否为敏感数据
   */
  public static isSensitive(key: string): boolean {
    return this.SENSITIVE_KEYS.includes(key);
  }

  /**
   * 加密数据
   */
  public static encrypt(data: string): string {
    if (!data) {
      return data;
    }

    try {
      const key = this.generateDeviceKey();

      // 使用简单但有效的XOR加密
      const encrypted = data.split('').map((char, i) => {
        const keyChar = key.charCodeAt(i % key.length);
        const dataChar = char.charCodeAt(0);
        return String.fromCharCode(dataChar ^ keyChar);
      }).join('');

      // Base64编码并添加标识前缀
      const result = 'ENCRYPTED:' + btoa(encrypted);
      return result;
    } catch (error) {
      console.error('[SecureStorage] 加密失败:', error);
      return data; // 加密失败时返回原数据
    }
  }

  /**
   * 解密数据
   */
  public static decrypt(encryptedData: string): string {
    if (!encryptedData || !encryptedData.startsWith('ENCRYPTED:')) {
      return encryptedData; // 不是加密数据，直接返回
    }

    try {
      const key = this.generateDeviceKey();

      // 移除前缀并解码
      const base64Data = encryptedData.replace('ENCRYPTED:', '');
      const encrypted = atob(base64Data);

      // XOR解密
      const decrypted = encrypted.split('').map((char, i) => {
        const keyChar = key.charCodeAt(i % key.length);
        const encryptedChar = char.charCodeAt(0);
        return String.fromCharCode(encryptedChar ^ keyChar);
      }).join('');

      return decrypted;
    } catch (error) {
      console.error('[SecureStorage] 解密失败:', error);
      return encryptedData; // 解密失败时返回原数据
    }
  }

  /**
   * 处理存储值（加密敏感数据）
   */
  public static processForStorage(key: string, value: any): any {
    if (!this.isSensitive(key) || value === null || value === undefined) {
      return value;
    }

    const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
    const encrypted = this.encrypt(stringValue);
    return encrypted;
  }

  /**
   * 处理读取值（解密敏感数据）
   */
  public static processFromStorage(key: string, value: any): any {
    if (!this.isSensitive(key) || value === null || value === undefined) {
      this.logToPersistentStorage(`processFromStorage: ${key} 不需要解密或值为空`);
      return value;
    }

    this.logToPersistentStorage(`processFromStorage: 开始解密 ${key}, 原始值长度: ${String(value).length}`);
    const stringValue = typeof value === 'string' ? value : String(value);

    try {
      const decrypted = this.decrypt(stringValue);
      this.logToPersistentStorage(`processFromStorage: ${key} 解密成功, 解密值长度: ${decrypted.length}`);

      // 尝试解析JSON，如果失败则返回字符串
      try {
        const parsed = JSON.parse(decrypted);
        this.logToPersistentStorage(`processFromStorage: ${key} JSON解析成功`);
        return parsed;
      } catch {
        this.logToPersistentStorage(`processFromStorage: ${key} JSON解析失败，返回字符串`);
        return decrypted;
      }
    } catch (error) {
      this.logToPersistentStorage(`processFromStorage: ${key} 解密失败: ${error}`);
      console.error(`[SecureStorage] processFromStorage: ${key} 解密失败:`, error);
      return null;
    }
  }

  /**
   * 持久化日志，即使页面刷新也能保留
   */
  private static logToPersistentStorage(message: string): void {
    // 生产环境中禁用持久化日志
    if (import.meta.env.PROD) {
      return;
    }

    try {
      const timestamp = new Date().toISOString();
      const logEntry = `${timestamp}: ${message}`;
      const existingLogs = localStorage.getItem('debug_storage_logs') || '[]';
      const logs = JSON.parse(existingLogs);
      logs.push(logEntry);

      // 只保留最近50条日志
      if (logs.length > 50) {
        logs.splice(0, logs.length - 50);
      }

      localStorage.setItem('debug_storage_logs', JSON.stringify(logs));
    } catch (error) {
      // 忽略日志存储错误
    }
  }
}

// 声明 chrome 类型，避免 TypeScript 错误
declare namespace chrome {
  namespace storage {
    interface StorageChange {
      oldValue?: any;
      newValue?: any;
    }
    
    interface StorageArea {
      get(keys: string | string[] | object | null, callback?: (items: Record<string, any>) => void): void;
      set(items: object, callback?: () => void): void;
      remove(keys: string | string[], callback?: () => void): void;
      clear(callback?: () => void): void;
      getBytesInUse?(keys: string | string[] | null, callback?: (bytesInUse: number) => void): void;
    }
    
    interface StorageAreaSync extends StorageArea {}
    interface StorageAreaLocal extends StorageArea {}
    
    const sync: StorageAreaSync;
    const local: StorageAreaLocal;
    
    const onChanged: {
      addListener(callback: (changes: Record<string, StorageChange>, areaName: string) => void): void;
      removeListener(callback: (changes: Record<string, StorageChange>, areaName: string) => void): void;
    };
  }
  
  namespace runtime {
    const lastError: Error | undefined;
    
    function sendMessage(extensionId: string, message: any, callback?: (response: any) => void): void;
  }
}

// 检查是否可以使用 chrome.storage API
export const hasExtensionStorage = (): boolean => {
  // 如果注入脚本设置了全局标志，优先使用它
  if (typeof window !== 'undefined' && 'hasExtensionStorage' in window) {
    return window.hasExtensionStorage as boolean;
  }
  
  // 否则尝试直接检测 chrome.storage
  return typeof chrome !== 'undefined' && 
         chrome.storage !== undefined && 
         chrome.storage.local !== undefined;
};

/**
 * 设置存储项
 * @param key 键名
 * @param value 值
 */
export const setStorageItem = (key: string, value: any): Promise<void> => {
  return new Promise((resolve) => {
    // 如果已经在更新中，避免触发循环
    if (inUpdate) {
      resolve();
      return;
    }

    inUpdate = true;

    try {
    // 处理敏感数据加密
    const processedValue = SecureStorageEncryption.processForStorage(key, value);

    // 始终保存到 localStorage 作为备份
    try {
      if (processedValue === null || processedValue === undefined) {
        localStorage.removeItem(key);
      } else {
        const storageValue = typeof processedValue === 'string' ? processedValue : JSON.stringify(processedValue);
        localStorage.setItem(key, storageValue);

        // 敏感数据已加密存储
      }
    } catch (error) {
      console.error(`localStorage 存储错误 (${key}):`, error);
    }
    
    // 如果可用，也保存到 chrome.storage（使用相同的加密处理）
    if (hasExtensionStorage()) {
      const data: Record<string, any> = {};
      data[key] = processedValue;

        try {
      chrome.storage.local.set(data, () => {
        if (chrome.runtime.lastError) {
          console.error(`chrome.storage 存储错误 (${key}):`, chrome.runtime.lastError);
        }
        inUpdate = false;
        resolve();
      });
        } catch (error) {
          console.error(`chrome.storage 访问错误 (${key}):`, error);
          inUpdate = false;
          resolve();
        }
      } else {
        // 尝试通过StorageBridge同步到扩展
        try {
          // 如果window.dispatchEvent可用，触发自定义事件
          if (typeof window !== 'undefined' && window.dispatchEvent) {
            // 如果有操作验证函数，先验证操作类型
            if (typeof window !== 'undefined' && 'validateStorageOperation' in window) {
              const isValid = (window as any).validateStorageOperation('set');
              if (!isValid) {
                throw new Error('未知的存储操作');
              }
            }
            
            window.dispatchEvent(new CustomEvent('sync-to-extension', { 
              detail: { key, value } 
            }));
          }
        } catch (error) {
          // 如果有错误处理函数，使用它
          if (typeof window !== 'undefined' && 'handleStorageError' in window) {
            (window as any).handleStorageError(error);
    } else {
            console.error('[StorageBridge] 存储操作错误:', error);
          }
        }
        
        inUpdate = false;
        resolve();
      }
    } catch (error) {
      console.error(`存储操作错误 (${key}):`, error);
      inUpdate = false;
      resolve();
    }
  });
};

/**
 * 批量设置存储项
 * @param items 键值对对象
 */
export const setStorageItems = (items: Record<string, any>): Promise<void> => {
  return new Promise((resolve) => {
    // 如果已经在更新中，避免触发循环
    if (inUpdate) {
      resolve();
      return;
    }

    inUpdate = true;
    
    // 保存到 localStorage（处理加密）
    const processedItems: Record<string, any> = {};
    try {
      Object.entries(items).forEach(([key, value]) => {
        const processedValue = SecureStorageEncryption.processForStorage(key, value);
        processedItems[key] = processedValue;

        if (processedValue === null || processedValue === undefined) {
          localStorage.removeItem(key);
        } else {
          const storageValue = typeof processedValue === 'string' ? processedValue : JSON.stringify(processedValue);
          localStorage.setItem(key, storageValue);

          // 批量存储敏感数据已加密
        }
      });
    } catch (error) {
      console.error('localStorage 批量存储错误:', error);
    }

    // 如果可用，也保存到 chrome.storage（使用加密处理后的数据）
    if (hasExtensionStorage()) {
      chrome.storage.local.set(processedItems, () => {
        if (chrome.runtime.lastError) {
          console.error('chrome.storage 批量存储错误:', chrome.runtime.lastError);
        } else {
          // 敏感数据已同步到插件存储
        }
        inUpdate = false;
        resolve();
      });
    } else {
      // 使用安全的方式触发事件
      safeDispatchStorageEvent('set', { keys: Object.keys(items) });
      inUpdate = false;
      resolve();
    }
  });
};

/**
 * 获取存储项
 * @param key 键名
 * @param defaultValue 默认值
 */
export const getStorageItem = <T>(key: string, defaultValue?: T): Promise<T | null> => {
  return new Promise((resolve) => {
    if (hasExtensionStorage()) {
      chrome.storage.local.get(key, (result: Record<string, any>) => {
        if (chrome.runtime.lastError) {
          console.error(`chrome.storage 读取错误 (${key}):`, chrome.runtime.lastError);
          // 降级到 localStorage
          resolveFromLocalStorage();
        } else if (result[key] !== undefined) {
          // 处理解密
          const decryptedValue = SecureStorageEncryption.processFromStorage(key, result[key]);
          resolve(decryptedValue);
        } else {
          // 如果 chrome.storage 中没有，尝试从 localStorage 获取
          resolveFromLocalStorage();
        }
      });
    } else {
      resolveFromLocalStorage();
    }
    
    function resolveFromLocalStorage() {
      try {
        const value = localStorage.getItem(key);
        if (value !== null) {
          // 先处理解密
          const decryptedValue = SecureStorageEncryption.processFromStorage(key, value);

          // 如果解密后的值已经是对象，直接返回
          if (typeof decryptedValue === 'object' && decryptedValue !== null) {
            resolve(decryptedValue);
            return;
          }

          try {
            // 尝试解析 JSON
            resolve(JSON.parse(decryptedValue));
          } catch {
            // 如果不是 JSON，直接返回字符串
            resolve(decryptedValue as unknown as T);
          }
        } else {
          resolve(defaultValue !== undefined ? defaultValue : null);
        }
      } catch (error) {
        console.error(`localStorage 读取错误 (${key}):`, error);
        resolve(defaultValue !== undefined ? defaultValue : null);
      }
    }
  });
};

/**
 * 批量获取存储项
 * @param keys 键名数组
 */
export const getStorageItems = <T extends Record<string, any>>(keys: string[]): Promise<T> => {
  return new Promise((resolve) => {
    if (hasExtensionStorage()) {
      chrome.storage.local.get(keys, (result: Record<string, any>) => {
        if (chrome.runtime.lastError) {
          console.error('chrome.storage 批量读取错误:', chrome.runtime.lastError);
          // 降级到 localStorage
          resolveFromLocalStorage();
        } else {
          // 处理解密
          const decryptedResult: Record<string, any> = {};
          Object.entries(result).forEach(([key, value]) => {
            decryptedResult[key] = SecureStorageEncryption.processFromStorage(key, value);
          });

          // 检查是否所有键都存在，如果不存在则从 localStorage 补充
          const missingKeys = keys.filter(key => result[key] === undefined);
          if (missingKeys.length > 0) {
            const localValues = getMultipleFromLocalStorage(missingKeys);
            resolve({ ...decryptedResult, ...localValues } as T);
          } else {
            resolve(decryptedResult as T);
          }
        }
      });
    } else {
      resolveFromLocalStorage();
    }
    
    function resolveFromLocalStorage() {
      resolve(getMultipleFromLocalStorage(keys) as T);
    }
    
    function getMultipleFromLocalStorage(keysToGet: string[]): Record<string, any> {
      const result: Record<string, any> = {};
      keysToGet.forEach(key => {
        try {
          const value = localStorage.getItem(key);
          if (value !== null) {
            // 先处理解密
            const decryptedValue = SecureStorageEncryption.processFromStorage(key, value);

            // 如果解密后的值已经是对象，直接使用
            if (typeof decryptedValue === 'object' && decryptedValue !== null) {
              result[key] = decryptedValue;
              return;
            }

            try {
              // 尝试解析 JSON
              result[key] = JSON.parse(decryptedValue);
            } catch {
              // 如果不是 JSON，直接返回字符串
              result[key] = decryptedValue;
            }
          }
        } catch (error) {
          console.error(`localStorage 读取错误 (${key}):`, error);
        }
      });
      return result;
    }
  });
};

/**
 * 移除存储项
 * @param key 键名或键名数组
 */
export const removeStorageItem = (key: string | string[]): Promise<void> => {
  return new Promise((resolve) => {
    // 如果已经在更新中，避免触发循环
    if (inUpdate) {
      resolve();
      return;
    }
    
    inUpdate = true;
    const keys = Array.isArray(key) ? key : [key];
    
    // 从 localStorage 移除
    keys.forEach(k => {
      try {
        localStorage.removeItem(k);
      } catch (error) {
        console.error(`localStorage 删除错误 (${k}):`, error);
      }
    });
    
    // 如果可用，也从 chrome.storage 移除
    if (hasExtensionStorage()) {
      chrome.storage.local.remove(keys, () => {
        if (chrome.runtime.lastError) {
          console.error('chrome.storage 删除错误:', chrome.runtime.lastError);
        }
        inUpdate = false;
        resolve();
      });
    } else {
      // 使用安全的方式触发事件
      safeDispatchStorageEvent('remove', { keys });
      inUpdate = false;
      resolve();
    }
  });
};

/**
 * 清除所有存储
 */
export const clearStorage = (): Promise<void> => {
  return new Promise((resolve) => {
    // 如果已经在更新中，避免触发循环
    if (inUpdate) {
      resolve();
      return;
    }
    
    inUpdate = true;
    
    // 清除 localStorage
    try {
      localStorage.clear();
    } catch (error) {
      console.error('localStorage 清除错误:', error);
    }
    
    // 如果可用，也清除 chrome.storage
    if (hasExtensionStorage()) {
      chrome.storage.local.clear(() => {
        if (chrome.runtime.lastError) {
          console.error('chrome.storage 清除错误:', chrome.runtime.lastError);
        }
        inUpdate = false;
        resolve();
      });
    } else {
      // 使用安全的方式触发事件
      safeDispatchStorageEvent('clear', { cleared: true });
      inUpdate = false;
      resolve();
    }
  });
};

/**
 * 添加存储变化监听器
 * @param callback 回调函数
 */
export const addStorageListener = (callback: (changes: Record<string, any>) => void): () => void => {
  // 监听 localStorage 变化（仅在不同窗口/标签页之间有效）
  const handleStorageEvent = (event: StorageEvent) => {
    if (event.key) {
      const changes: Record<string, any> = {};
      changes[event.key] = { newValue: event.newValue };
      callback(changes);
    }
  };
  
  // 监听自定义事件（同一窗口内的变化）
  const handleCustomEvent = (event: CustomEvent) => {
    if (event.detail && event.detail.keys) {
      const changes: Record<string, any> = {};
      event.detail.keys.forEach((key: string) => {
        changes[key] = { newValue: localStorage.getItem(key) };
      });
      callback(changes);
    } else if (event.detail && event.detail.cleared) {
      callback({ cleared: true });
    }
  };
  
  // 监听 chrome.storage 变化
  const handleChromeStorageChange = (changes: Record<string, chrome.storage.StorageChange>, area: string) => {
    if (area === 'local') {
      callback(changes);
    }
  };
  
  window.addEventListener('storage', handleStorageEvent);
  window.addEventListener('storage-changed', handleCustomEvent as EventListener);
  
  if (hasExtensionStorage()) {
    chrome.storage.onChanged.addListener(handleChromeStorageChange);
  }
  
  // 返回清理函数
  return () => {
    window.removeEventListener('storage', handleStorageEvent);
    window.removeEventListener('storage-changed', handleCustomEvent as EventListener);
  };
};

/**
 * 安全地触发存储变更事件
 * @param operationType 操作类型 (e.g., 'set', 'remove', 'clear', 'storage_changed', 'storage_changed_result')
 * @param detail 事件详情
 */
const safeDispatchStorageEvent = (operationType: string, detail: any): void => {
  try {
    // 检查是否可以触发事件
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      // 如果有操作验证函数，先验证操作类型
      if ('validateStorageOperation' in window) {
        const isValid = (window as any).validateStorageOperation(operationType);
        if (!isValid) {
          // 操作类型验证失败，但继续执行
        }
      }
      
      // 创建并分发事件
      const event = new CustomEvent('storage-changed', { detail });
      window.dispatchEvent(event);
    }
  } catch (error) {
    // 如果有错误处理函数，使用它
    if (typeof window !== 'undefined' && 'handleStorageError' in window) {
      (window as any).handleStorageError(error);
    } else {
      console.error('[StorageBridge] 存储操作错误（在 safeDispatchStorageEvent 中捕获）:', error);
    }
  }
};

/**
 * 数据迁移：将现有明文敏感数据转换为加密存储
 * 这个函数应该在应用启动时调用一次
 */
export const migrateToSecureStorage = async (): Promise<void> => {
  // 生产环境中静默执行迁移
  try {
    const sensitiveKeys = ['access_token', 'refresh_token', 'user_id'];
    const needsMigration: string[] = [];

    // 检查哪些敏感数据需要迁移
    for (const key of sensitiveKeys) {
      try {
        const value = localStorage.getItem(key);
        if (value && !value.startsWith('ENCRYPTED:')) {
          needsMigration.push(key);
        }
      } catch (error) {
        // 忽略检查错误
      }
    }

    if (needsMigration.length === 0) {
      return;
    }

    // 执行迁移
    for (const key of needsMigration) {
      try {
        const plainValue = localStorage.getItem(key);
        if (plainValue) {
          // 加密并重新存储
          const encryptedValue = SecureStorageEncryption.encrypt(plainValue);
          localStorage.setItem(key, encryptedValue);

          // 如果有插件存储，也同步更新
          if (hasExtensionStorage()) {
            const data: Record<string, any> = {};
            data[key] = encryptedValue;
            chrome.storage.local.set(data, () => {
              if (chrome.runtime.lastError) {
                console.error(`[SecureStorage] 插件存储迁移失败 (${key}):`, chrome.runtime.lastError);
              }
            });
          }

          // 敏感数据迁移完成
        }
      } catch (error) {
        console.error(`[SecureStorage] 迁移失败 (${key}):`, error);
      }
    }

    // 数据迁移完成
  } catch (error) {
    console.error('[SecureStorage] 数据迁移过程出错:', error);
  }
};

/**
 * 强制重新加密所有敏感数据
 * 用于调试和确保数据正确加密
 */
export const forceEncryptSensitiveData = async (): Promise<void> => {

  try {
    const sensitiveKeys = ['access_token', 'refresh_token', 'user_id'];

    for (const key of sensitiveKeys) {
      try {
        const value = localStorage.getItem(key);
        if (value) {
          // 如果已经加密，先解密
          let plainValue = value;
          if (value.startsWith('ENCRYPTED:')) {
            plainValue = SecureStorageEncryption.decrypt(value);
          }

          // 重新加密
          const encryptedValue = SecureStorageEncryption.encrypt(plainValue);
          localStorage.setItem(key, encryptedValue);

          // 同步到插件存储
          if (hasExtensionStorage()) {
            const data: Record<string, any> = {};
            data[key] = encryptedValue;
            chrome.storage.local.set(data, () => {
              if (chrome.runtime.lastError) {
                console.error(`[SecureStorage] 插件存储更新失败 (${key}):`, chrome.runtime.lastError);
              }
            });
          }

          // 重新加密完成
        }
      } catch (error) {
        console.error(`[SecureStorage] 重新加密失败 (${key}):`, error);
      }
    }

    // 强制加密完成
  } catch (error) {
    console.error('[SecureStorage] 强制加密过程出错:', error);
  }
};
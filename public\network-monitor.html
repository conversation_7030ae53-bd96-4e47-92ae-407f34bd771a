<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络请求监控</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #efe; border-color: #cfc; }
        .error { background-color: #fee; border-color: #fcc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 12px 20px; margin: 8px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        button:hover { background: #005a87; }
        input { padding: 10px; width: 300px; border: 1px solid #ddd; border-radius: 3px; margin: 5px; }
        .log { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; font-size: 12px; }
        .request-details { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .highlight { background: #ffeb3b; padding: 2px 4px; border-radius: 2px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 网络请求监控 - device_id 验证</h1>
        
        <div class="section info">
            <h2>📋 监控目标</h2>
            <p>监控 <code>POST /auth/request-code</code> 请求，验证是否包含 <span class="highlight">device_id</span> 参数</p>
        </div>

        <div class="section">
            <h2>🧪 测试验证码请求</h2>
            <div>
                <label><strong>邮箱地址:</strong></label><br>
                <input type="email" id="email" value="<EMAIL>" placeholder="输入邮箱地址">
                <br>
                <button onclick="testRequestCode()">🚀 发送验证码请求</button>
                <button onclick="clearLog()">清空日志</button>
            </div>
        </div>

        <div class="section" id="request-details" style="display: none;">
            <h2>📊 请求详情</h2>
            <div id="request-content"></div>
        </div>

        <div class="section">
            <h2>📝 监控日志</h2>
            <div id="monitor-log" class="log"></div>
        </div>

        <div class="section warning">
            <h2>⚠️ 检查要点</h2>
            <ul>
                <li><strong>device_id 存在:</strong> 请求体中应包含 device_id 字段</li>
                <li><strong>UUID 格式:</strong> device_id 应为标准 UUID 格式</li>
                <li><strong>device_info 存在:</strong> 应包含额外的设备信息</li>
                <li><strong>请求头正确:</strong> Content-Type 应为 application/json</li>
            </ul>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('monitor-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '5px';
            logEntry.style.padding = '5px';
            logEntry.style.borderRadius = '3px';
            
            if (type === 'success') {
                logEntry.style.backgroundColor = '#d4edda';
                logEntry.style.color = '#155724';
            } else if (type === 'error') {
                logEntry.style.backgroundColor = '#f8d7da';
                logEntry.style.color = '#721c24';
            } else if (type === 'warning') {
                logEntry.style.backgroundColor = '#fff3cd';
                logEntry.style.color = '#856404';
            } else {
                logEntry.style.backgroundColor = '#d1ecf1';
                logEntry.style.color = '#0c5460';
            }
            
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('monitor-log').innerHTML = '';
            document.getElementById('request-details').style.display = 'none';
        }

        function showRequestDetails(requestData, responseData) {
            const contentDiv = document.getElementById('request-content');
            
            // 检查 device_id
            const hasDeviceId = requestData.device_id ? true : false;
            const deviceIdValid = hasDeviceId && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(requestData.device_id);
            
            contentDiv.innerHTML = `
                <div class="request-details">
                    <h3>📤 请求数据</h3>
                    <p><strong>URL:</strong> <code>POST /auth/request-code</code></p>
                    <p><strong>device_id 状态:</strong> 
                        <span style="color: ${hasDeviceId ? '#28a745' : '#dc3545'};">
                            ${hasDeviceId ? '✅ 存在' : '❌ 缺失'}
                        </span>
                    </p>
                    ${hasDeviceId ? `
                        <p><strong>device_id 值:</strong> <code>${requestData.device_id}</code></p>
                        <p><strong>UUID 格式:</strong> 
                            <span style="color: ${deviceIdValid ? '#28a745' : '#dc3545'};">
                                ${deviceIdValid ? '✅ 有效' : '❌ 无效'}
                            </span>
                        </p>
                    ` : ''}
                    <p><strong>device_info 状态:</strong> 
                        <span style="color: ${requestData.device_info ? '#28a745' : '#dc3545'};">
                            ${requestData.device_info ? '✅ 存在' : '❌ 缺失'}
                        </span>
                    </p>
                    
                    <h4>完整请求体:</h4>
                    <pre>${JSON.stringify(requestData, null, 2)}</pre>
                    
                    <h3>📥 响应数据</h3>
                    <pre>${JSON.stringify(responseData, null, 2)}</pre>
                </div>
            `;
            
            document.getElementById('request-details').style.display = 'block';
        }

        // 模拟设备ID生成（简化版）
        async function generateDeviceId() {
            // 收集设备特征
            const features = {
                screen: `${screen.width}x${screen.height}x${screen.colorDepth}`,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                platform: navigator.platform || 'Unknown',
                language: navigator.language,
                userAgent: navigator.userAgent.replace(/\d+\.\d+\.\d+(\.\d+)?/g, 'X.X.X')
            };
            
            // 生成哈希
            const featuresString = JSON.stringify(features);
            const encoder = new TextEncoder();
            const data = encoder.encode(featuresString);
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            const hash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
            
            // 转换为UUID格式
            const paddedHash = (hash + '0'.repeat(32)).substring(0, 32);
            const uuid = [
                paddedHash.substring(0, 8),
                paddedHash.substring(8, 12),
                '4' + paddedHash.substring(13, 16),
                ((parseInt(paddedHash.substring(16, 17), 16) & 0x3) | 0x8).toString(16) + paddedHash.substring(17, 20),
                paddedHash.substring(20, 32)
            ].join('-');
            
            return uuid;
        }

        async function testRequestCode() {
            const email = document.getElementById('email').value.trim();
            
            if (!email) {
                log('❌ 请输入邮箱地址', 'error');
                return;
            }
            
            log('🚀 开始测试验证码请求...', 'info');
            log(`📧 邮箱: ${email}`, 'info');
            
            try {
                // 生成设备ID
                log('🔍 生成设备ID...', 'info');
                const deviceId = await generateDeviceId();
                log(`✅ 设备ID生成成功: ${deviceId}`, 'success');
                
                // 构建请求数据
                const requestData = {
                    email: email,
                    device_id: deviceId,
                    device_info: {
                        userAgent: navigator.userAgent,
                        platform: navigator.platform,
                        screen: `${screen.width}x${screen.height}`,
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    }
                };
                
                log('📤 发送请求到服务器...', 'info');
                log(`🎯 URL: http://localhost:5173/api/auth/request-code`, 'info');
                
                // 发送请求
                const response = await fetch('http://localhost:5173/api/auth/request-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                log(`📊 响应状态: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                
                let responseData;
                try {
                    const responseText = await response.text();
                    responseData = responseText ? JSON.parse(responseText) : {};
                } catch (parseError) {
                    responseData = { error: '响应解析失败', raw: await response.text() };
                }
                
                // 显示请求详情
                showRequestDetails(requestData, responseData);
                
                if (response.ok) {
                    log('✅ 验证码请求发送成功', 'success');
                    log('✅ device_id 已正确包含在请求中', 'success');
                } else {
                    log(`❌ 验证码请求失败: ${response.status}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
                
                // 即使请求失败，也显示请求数据用于验证
                const deviceId = await generateDeviceId();
                const requestData = {
                    email: email,
                    device_id: deviceId,
                    device_info: {
                        userAgent: navigator.userAgent,
                        platform: navigator.platform,
                        screen: `${screen.width}x${screen.height}`,
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    }
                };
                
                showRequestDetails(requestData, { error: error.message });
            }
        }

        // 页面加载时显示信息
        window.onload = function() {
            log('🔍 网络请求监控工具已加载', 'info');
            log('💡 输入邮箱地址并点击"发送验证码请求"开始测试', 'info');
            log('🎯 将验证 device_id 是否正确包含在请求中', 'info');
        };
    </script>
</body>
</html>

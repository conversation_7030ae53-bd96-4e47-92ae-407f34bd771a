# API配置指南

## 问题概述

如果您在使用此应用时遇到以下错误：
- "无效或过期的Access Token"
- "认证失败 (401)"
- "API请求缺少API密钥"

这通常是因为环境变量中缺少必要的API密钥和API URL配置。

## 解决步骤

### 1. 检查环境变量配置

运行以下命令检查您的环境变量配置：

```bash
npm run check-env
```

此命令将检查您的环境变量文件，并告诉您是否缺少必要的配置。

### 2. 设置API环境变量

1. 在项目根目录创建一个名为 `.env.local` 的文件
2. 添加以下内容：

```
VITE_API_URL=https://autoapi.utilityapp.site
VITE_API_KEY=您的实际API密钥
```

**注意：** 
- 必须设置 `VITE_API_URL` 为 `https://autoapi.utilityapp.site`
- 必须设置有效的 `VITE_API_KEY`

您可以从以下位置获取API密钥：
- 如果您是项目开发者，请联系项目管理员获取API密钥
- 如果您是项目管理员，请登录API管理平台获取密钥

### 3. 重启开发服务器

设置环境变量后，请重启开发服务器：

```bash
npm run dev
```

## 常见问题

### API密钥无效

如果您已设置API密钥但仍然收到401错误，可能是因为：
- API密钥已过期或被吊销
- API密钥格式不正确（请确保没有多余的空格或引号）
- 您的IP地址不在API的允许列表中

### 令牌刷新失败

如果您看到"刷新令牌失败"的错误，可能是因为：
- 刷新令牌已过期
- 服务器拒绝了刷新请求
- 网络连接问题

请尝试重新登录应用程序。

### 环境变量未加载

在某些情况下，即使您设置了环境变量，它们也可能未被正确加载。请确保：
- 环境变量文件名称正确（`.env.local`）
- 文件位于项目根目录
- 变量名称正确（`VITE_API_KEY` 和 `VITE_API_URL`）
- 重启开发服务器后再试

### API URL错误

所有API请求必须发送到 `https://autoapi.utilityapp.site`。不要使用本地代理或其他地址。这是确保应用程序正常工作的必要条件。

## 其他资源

如需更多帮助，请参考：
- [Vite环境变量文档](https://vitejs.dev/guide/env-and-mode.html)
- 项目内部API文档（README.md）

如果问题仍然存在，请联系项目维护者。 
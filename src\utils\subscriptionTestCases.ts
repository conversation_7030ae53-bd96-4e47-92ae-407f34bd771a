/**
 * 订阅退订逻辑测试用例
 * 用于验证canCancelSubscription函数在不同场景下的行为
 */

import { SubscriptionInfo, canCancelSubscription, isInTrialPeriod } from '../services/userService';
import { log } from './logger';

// 测试用例数据
export const testCases: Array<{
  name: string;
  subscriptionInfo: SubscriptionInfo | null;
  expectedCanCancel: boolean;
  expectedReason?: string;
}> = [
  {
    name: '无订阅用户',
    subscriptionInfo: null,
    expectedCanCancel: false,
    expectedReason: '无有效订阅'
  },
  {
    name: '试用期用户 (status: trialing)',
    subscriptionInfo: {
      status: 'trialing',
      plan_name: 'Premium Plan',
      is_trial: true,
      current_period_start: '2025-07-25T10:09:29.000Z',
      current_period_end: '2025-08-24T10:09:29.000Z',
      trial_ends_at: '2025-08-24T10:09:29.000Z',
      cancel_at_period_end: false,
      can_cancel: true,
      cancel_reason: null,
      days_remaining: 25
    },
    expectedCanCancel: true
  },
  {
    name: '付费用户（试用期内, status: active）',
    subscriptionInfo: {
      status: 'active',
      plan_name: 'Premium Plan',
      is_trial: false,
      current_period_start: '2025-07-25T10:09:29.000Z',
      current_period_end: '2025-08-25T10:09:29.000Z',
      trial_ends_at: '2025-07-30T10:09:29.000Z',
      cancel_at_period_end: false,
      can_cancel: true,
      cancel_reason: null,
      days_remaining: 5
    },
    expectedCanCancel: true
  },
  {
    name: '付费用户（试用期外）',
    subscriptionInfo: {
      status: 'active',
      plan_name: 'Premium Plan',
      is_trial: false,
      current_period_start: '2025-07-25T10:09:29.000Z',
      current_period_end: '2025-08-25T10:09:29.000Z',
      trial_ends_at: '2025-07-30T10:09:29.000Z',
      cancel_at_period_end: false,
      can_cancel: false,
      cancel_reason: '试用期已结束，付费订阅不支持直接取消',
      days_remaining: 0
    },
    expectedCanCancel: false,
    expectedReason: '试用期已结束，付费订阅不支持直接取消'
  },
  {
    name: '已取消订阅用户',
    subscriptionInfo: {
      status: 'canceled',
      plan_name: 'Premium Plan',
      is_trial: false,
      current_period_start: '2025-07-25T10:09:29.000Z',
      current_period_end: '2025-08-25T10:09:29.000Z',
      trial_ends_at: '2025-07-30T10:09:29.000Z',
      cancel_at_period_end: true,
      can_cancel: false,
      cancel_reason: '订阅已取消',
      days_remaining: 0
    },
    expectedCanCancel: false,
    expectedReason: '订阅已取消'
  },
  {
    name: '无订阅信息的用户',
    subscriptionInfo: {
      status: null,
      plan_name: 'Unknown Plan',
      is_trial: false,
      current_period_start: null,
      current_period_end: null,
      trial_ends_at: null,
      cancel_at_period_end: false,
      can_cancel: false,
      cancel_reason: '无有效订阅',
      days_remaining: 0
    },
    expectedCanCancel: false,
    expectedReason: '无有效订阅'
  }
];

/**
 * 运行所有测试用例
 */
export function runSubscriptionTests(): void {
  log.group('订阅退订逻辑测试');

  let passedTests = 0;
  let totalTests = testCases.length;

  testCases.forEach((testCase, index) => {
    log.info(`测试 ${index + 1}: ${testCase.name}`);
    
    const result = canCancelSubscription(testCase.subscriptionInfo);
    const isTrialResult = isInTrialPeriod(testCase.subscriptionInfo);
    
    // 检查can_cancel结果
    const canCancelPassed = result.canCancel === testCase.expectedCanCancel;
    const reasonPassed = !testCase.expectedReason || result.reason === testCase.expectedReason;
    
    const testPassed = canCancelPassed && reasonPassed;
    
    if (testPassed) {
      log.info('✅ 通过');
      passedTests++;
    } else {
      log.error('❌ 失败');
      log.error(`期望: canCancel=${testCase.expectedCanCancel}, reason="${testCase.expectedReason}"`);
      log.error(`实际: canCancel=${result.canCancel}, reason="${result.reason}"`);
    }
    
    // 只有非空订阅信息才检查试用期状态
    if (testCase.subscriptionInfo) {
      log.debug(`试用期状态: ${isTrialResult}`);
    }
    log.debug(`剩余天数: ${result.daysRemaining}`);
  });

  log.info(`测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    log.info('🎉 所有测试通过！');
  } else {
    log.warn('⚠️  部分测试失败，请检查逻辑');
  }
  
  log.groupEnd();
}

/**
 * 在浏览器控制台中运行测试的便捷函数
 */

/**
 * 测试Token解密功能
 */
export async function testTokenDecryption(): Promise<void> {
  log.group('Token解密测试');

  try {
    // 导入存储适配器
    const { getStorageItem } = await import('../services/storageAdapter');
    const { getAccessToken } = await import('../services/authService');

    // 1. 直接从localStorage获取原始值
    const rawToken = localStorage.getItem('access_token');
    log.debug('原始存储值:', rawToken ? `${rawToken.substring(0, 50)}...` : 'null');

    // 2. 通过存储适配器获取（应该自动解密）
    const decryptedToken = await getStorageItem<string>('access_token');
    log.debug('解密后的值:', decryptedToken ? `${decryptedToken.substring(0, 50)}...` : 'null');

    // 3. 通过authService获取
    const authToken = await getAccessToken();
    log.debug('通过authService获取:', authToken ? `${authToken.substring(0, 50)}...` : 'null');

    // 4. 比较结果
    if (decryptedToken === authToken) {
      log.info('✅ 存储适配器和authService返回相同的token');
    } else {
      log.error('❌ 存储适配器和authService返回不同的token');
    }

    // 5. 检查token格式
    if (authToken) {
      if (authToken.startsWith('ENCRYPTED:')) {
        log.error('❌ Token仍然是加密状态，解密失败！');
      } else if (authToken.includes('.')) {
        const parts = authToken.split('.');
        log.info(`✅ Token看起来像JWT格式（${parts.length}部分）`);
        
        if (parts.length === 3) {
          try {
            const header = JSON.parse(atob(parts[0]));
            log.debug('JWT Header:', header);
          } catch (e) {
            log.warn('⚠️  无法解析JWT header');
          }
        }
      } else {
        log.info('ℹ️  Token不是JWT格式');
      }
    }

  } catch (error) {
    log.error('Token解密测试失败', error);
  }

  log.groupEnd();
}

// 将测试函数暴露到全局作用域（仅在开发环境）
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  window.runSubscriptionTests = runSubscriptionTests;
  window.testTokenDecryption = testTokenDecryption;
}
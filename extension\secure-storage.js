/**
 * 浏览器插件安全存储工具
 * 与网站端保持一致的加密/解密逻辑
 */

/**
 * 安全存储加密器（插件版本）
 */
class SecureStorageEncryption {
  // 定义敏感数据键名（与网站端保持一致）
  static SENSITIVE_KEYS = [
    'access_token',
    'refresh_token', 
    'user_id'
  ];

  // 加密密钥缓存
  static encryptionKey = null;

  /**
   * 生成基于设备的加密密钥（与网站端保持一致）
   */
  static generateDeviceKey() {
    if (this.encryptionKey) {
      return this.encryptionKey;
    }

    try {
      // 基于设备特征生成唯一密钥（与网站端逻辑一致）
      const fingerprint = [
        typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
        typeof navigator !== 'undefined' ? navigator.language : 'en',
        typeof screen !== 'undefined' ? `${screen.width}x${screen.height}` : '1920x1080',
        typeof window !== 'undefined' ? new Date().getTimezoneOffset().toString() : '0',
        'autofill-secure-key-v1' // 应用标识
      ].join('|');
      
      // 生成32位密钥
      this.encryptionKey = btoa(fingerprint).replace(/[^a-zA-Z0-9]/g, '').slice(0, 32).padEnd(32, 'A');
      return this.encryptionKey;
    } catch (error) {
      console.warn('[SecureStorage] 生成设备密钥失败，使用默认密钥:', error);
      this.encryptionKey = 'AutoFillSecureStorageDefaultKey32';
      return this.encryptionKey;
    }
  }

  /**
   * 判断是否为敏感数据
   */
  static isSensitive(key) {
    return this.SENSITIVE_KEYS.includes(key);
  }

  /**
   * 加密数据
   */
  static encrypt(data) {
    if (!data) return data;
    
    try {
      const key = this.generateDeviceKey();
      
      // 使用简单但有效的XOR加密
      const encrypted = data.split('').map((char, i) => {
        const keyChar = key.charCodeAt(i % key.length);
        const dataChar = char.charCodeAt(0);
        return String.fromCharCode(dataChar ^ keyChar);
      }).join('');
      
      // Base64编码并添加标识前缀
      return 'ENCRYPTED:' + btoa(encrypted);
    } catch (error) {
      console.error('[SecureStorage] 加密失败:', error);
      return data; // 加密失败时返回原数据
    }
  }

  /**
   * 解密数据
   */
  static decrypt(encryptedData) {
    if (!encryptedData || !encryptedData.startsWith('ENCRYPTED:')) {
      return encryptedData; // 不是加密数据，直接返回
    }
    
    try {
      const key = this.generateDeviceKey();
      
      // 移除前缀并解码
      const base64Data = encryptedData.replace('ENCRYPTED:', '');
      const encrypted = atob(base64Data);
      
      // XOR解密
      const decrypted = encrypted.split('').map((char, i) => {
        const keyChar = key.charCodeAt(i % key.length);
        const encryptedChar = char.charCodeAt(0);
        return String.fromCharCode(encryptedChar ^ keyChar);
      }).join('');
      
      return decrypted;
    } catch (error) {
      console.error('[SecureStorage] 解密失败:', error);
      return encryptedData; // 解密失败时返回原数据
    }
  }

  /**
   * 处理存储值（加密敏感数据）
   */
  static processForStorage(key, value) {
    if (!this.isSensitive(key) || value === null || value === undefined) {
      return value;
    }
    
    const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
    return this.encrypt(stringValue);
  }

  /**
   * 处理读取值（解密敏感数据）
   */
  static processFromStorage(key, value) {
    if (!this.isSensitive(key) || value === null || value === undefined) {
      return value;
    }
    
    const stringValue = typeof value === 'string' ? value : String(value);
    const decrypted = this.decrypt(stringValue);
    
    // 尝试解析JSON，如果失败则返回字符串
    try {
      return JSON.parse(decrypted);
    } catch {
      return decrypted;
    }
  }
}

/**
 * 安全存储适配器（插件版本）
 */
class SecureStorageAdapter {
  /**
   * 安全设置存储项
   */
  static setItem(key, value, callback) {
    const processedValue = SecureStorageEncryption.processForStorage(key, value);
    const data = {};
    data[key] = processedValue;
    
    chrome.storage.local.set(data, () => {
      if (chrome.runtime.lastError) {
        console.error(`[SecureStorage] 插件存储错误 (${key}):`, chrome.runtime.lastError);
      } else if (SecureStorageEncryption.isSensitive(key)) {
        console.log(`[SecureStorage] 敏感数据已加密存储到插件: ${key}`);
      }
      
      if (callback) callback();
    });
  }

  /**
   * 安全获取存储项
   */
  static getItem(key, callback) {
    chrome.storage.local.get(key, (result) => {
      if (chrome.runtime.lastError) {
        console.error(`[SecureStorage] 插件读取错误 (${key}):`, chrome.runtime.lastError);
        callback(null);
      } else {
        const value = result[key];
        const decryptedValue = SecureStorageEncryption.processFromStorage(key, value);
        callback(decryptedValue);
      }
    });
  }

  /**
   * 安全批量获取存储项
   */
  static getItems(keys, callback) {
    chrome.storage.local.get(keys, (result) => {
      if (chrome.runtime.lastError) {
        console.error('[SecureStorage] 插件批量读取错误:', chrome.runtime.lastError);
        callback({});
      } else {
        const decryptedResult = {};
        Object.entries(result).forEach(([key, value]) => {
          decryptedResult[key] = SecureStorageEncryption.processFromStorage(key, value);
        });
        callback(decryptedResult);
      }
    });
  }

  /**
   * 安全批量设置存储项
   */
  static setItems(items, callback) {
    const processedItems = {};
    Object.entries(items).forEach(([key, value]) => {
      processedItems[key] = SecureStorageEncryption.processForStorage(key, value);
    });
    
    chrome.storage.local.set(processedItems, () => {
      if (chrome.runtime.lastError) {
        console.error('[SecureStorage] 插件批量存储错误:', chrome.runtime.lastError);
      } else {
        const sensitiveCount = Object.keys(processedItems).filter(key => 
          SecureStorageEncryption.isSensitive(key)
        ).length;
        if (sensitiveCount > 0) {
          console.log(`[SecureStorage] ${sensitiveCount}个敏感数据已加密存储到插件`);
        }
      }
      
      if (callback) callback();
    });
  }

  /**
   * 移除存储项
   */
  static removeItems(keys, callback) {
    chrome.storage.local.remove(keys, () => {
      if (chrome.runtime.lastError) {
        console.error('[SecureStorage] 插件删除错误:', chrome.runtime.lastError);
      }
      
      if (callback) callback();
    });
  }
}

// 导出到全局作用域，供其他脚本使用
if (typeof window !== 'undefined') {
  window.SecureStorageAdapter = SecureStorageAdapter;
  window.SecureStorageEncryption = SecureStorageEncryption;
}

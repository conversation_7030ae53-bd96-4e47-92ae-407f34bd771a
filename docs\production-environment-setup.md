# 正式环境问题解决方案

## 🚨 主要问题

### 403 Forbidden 错误
**原因**: 使用了 sandbox 环境而不是 production 环境

**解决**:
```bash
# 在 Cloudflare Pages 环境变量中设置
VITE_PADDLE_ENVIRONMENT=production
```

### Failed to retrieve JWT 错误
**原因**: API 配置错误

**解决**:
```bash
VITE_API_URL=https://autoapi.utilityapp.site
VITE_API_KEY=your_production_api_key
```

### showExtensionStatus is not defined
**已修复**: 在 `src/utils/ConsoleCommands.ts` 中添加了全局函数定义

## 🔧 快速检查

```bash
# 检查环境变量
npm run check-env

# 检查 Paddle 配置  
npm run check-paddle
```

## 📋 必需的环境变量

```bash
VITE_PADDLE_ENVIRONMENT=production
VITE_PADDLE_CLIENT_TOKEN=your_token
VITE_PADDLE_PRODUCT_YEARLY=pri_xxxxx
VITE_API_URL=https://autoapi.utilityapp.site
VITE_API_KEY=your_api_key
```

## 🌐 Paddle 控制台设置

1. 登录 [Paddle 控制台](https://vendors.paddle.com/)
2. 进入 **Settings** → **Checkout Settings**
3. 在 **Allowed Domains** 中添加您的域名

## 🐛 调试步骤

1. 检查 Cloudflare Pages 环境变量
2. 验证 Paddle 域名白名单
3. 查看浏览器控制台错误
4. 运行诊断脚本 
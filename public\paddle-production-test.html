<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle正式环境测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        .critical { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .log { max-height: 400px; overflow-y: auto; }
        .config-info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .domain-list { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Paddle正式环境测试</h1>
        
        <div class="section critical">
            <h2>🚨 检测到的问题</h2>
            <ul>
                <li>❌ <strong>CSP错误:</strong> "frame-ancestors http://localhost" - 域名白名单问题</li>
                <li>❌ <strong>403 Forbidden:</strong> checkout-service.paddle.com - 权限或配置问题</li>
            </ul>
        </div>

        <div class="section info">
            <h2>📋 当前正式环境配置</h2>
            <div class="config-info">
                <h3>🔧 配置信息</h3>
                <p><strong>环境:</strong> Production ✅</p>
                <p><strong>Vendor ID:</strong> <code>231761</code></p>
                <p><strong>产品ID:</strong> <code>pri_01k0rfjdvjgsyrqxbf6sn6kk3w</code></p>
                <p><strong>Token:</strong> <code>live_6534d079211be149aae16b81d86</code> (正式环境)</p>
                <p><strong>当前域名:</strong> <code id="current-domain"></code></p>
            </div>
        </div>

        <div class="section warning">
            <h2>🎯 需要在正式环境Dashboard中配置</h2>
            
            <div class="domain-list">
                <h3>📋 域名白名单配置</h3>
                <p><strong>配置路径:</strong> <a href="https://vendors.paddle.com/" target="_blank">正式环境Dashboard</a> > Developer tools > Checkout settings > Allowed domains</p>
                
                <h4>需要添加的域名:</h4>
                <ul>
                    <li>✅ <code>http://localhost:5173</code></li>
                    <li>✅ <code>http://localhost:5174</code></li>
                    <li>✅ <code>http://localhost</code></li>
                    <li>✅ <code>https://autofill.utilityapp.site</code></li>
                </ul>
                
                <p><strong>⚠️ 重要:</strong> 确保在<strong>正式环境</strong>Dashboard中配置，不是sandbox！</p>
            </div>
        </div>

        <div class="section">
            <h2>🧪 正式环境测试</h2>
            <button onclick="testProductionConfig()">1. 测试正式环境配置</button>
            <button onclick="testMinimalProduction()">2. 测试最简配置</button>
            <button onclick="testDomainCheck()">3. 检查域名配置</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section error">
            <h2>🔧 解决步骤</h2>
            <ol>
                <li><strong>配置域名白名单:</strong>
                    <ul>
                        <li>登录 <a href="https://vendors.paddle.com/" target="_blank">正式环境Dashboard</a></li>
                        <li>进入 Developer tools > Checkout settings</li>
                        <li>在 Allowed domains 中添加上述域名</li>
                        <li>保存配置</li>
                    </ul>
                </li>
                
                <li><strong>验证产品配置:</strong>
                    <ul>
                        <li>确认产品 <code>pri_01k0rfjdvjgsyrqxbf6sn6kk3w</code> 在正式环境中存在</li>
                        <li>确认产品状态为 Active</li>
                        <li>确认价格配置正确</li>
                    </ul>
                </li>
                
                <li><strong>验证Token权限:</strong>
                    <ul>
                        <li>确认Token是正式环境的Client-side Token</li>
                        <li>确认Token有Checkout权限</li>
                        <li>如有问题，重新生成Token</li>
                    </ul>
                </li>
                
                <li><strong>账户验证:</strong>
                    <ul>
                        <li>确认正式环境账户完全验证</li>
                        <li>确认完成了KYC验证</li>
                        <li>确认绑定了有效的支付方式</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="section success">
            <h2>✅ 配置完成后的预期结果</h2>
            <ul>
                <li>✅ 不再出现CSP错误</li>
                <li>✅ 不再出现403错误</li>
                <li>✅ 支付页面正常打开</li>
                <li>✅ 显示正确的产品信息</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'live_6534d079211be149aae16b81d86',
            productId: 'pri_01k0rfjdvjgsyrqxbf6sn6kk3w',
            environment: 'production'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        function showCurrentDomain() {
            document.getElementById('current-domain').textContent = window.location.origin;
        }

        async function initializePaddle() {
            return new Promise((resolve) => {
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            log(`❌ Checkout错误: ${data.detail}`, 'error');
                            
                            if (data.detail && data.detail.includes('403')) {
                                log('🔍 403错误 - 权限或配置问题', 'error');
                                log('💡 检查产品ID是否在正式环境中存在', 'warning');
                            } else if (data.detail && data.detail.includes('frame-ancestors')) {
                                log('🔍 CSP错误 - 域名白名单问题', 'error');
                                log('💡 需要在正式环境Dashboard中配置域名', 'warning');
                            }
                        } else if (data.type === 'checkout.loaded') {
                            log('✅ Checkout加载成功！', 'success');
                        } else if (data.name === 'checkout.closed' && data.data) {
                            const item = data.data.items?.[0];
                            if (item?.price_id && item?.product?.id) {
                                log('🎉 正式环境配置成功！', 'success');
                            }
                        }
                    }
                });
                resolve();
            });
        }

        async function testProductionConfig() {
            log('🚀 测试1: 正式环境配置...', 'info');
            log(`环境: ${config.environment}`, 'info');
            log(`Token: ${config.token.substring(0, 15)}...`, 'info');
            log(`产品ID: ${config.productId}`, 'info');
            
            try {
                await initializePaddle();
                
                const testConfig = {
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                log(`配置: ${JSON.stringify(testConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(testConfig);
                log('✅ 正式环境测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 正式环境测试失败: ${error.message}`, 'error');
            }
        }

        async function testMinimalProduction() {
            log('🧪 测试2: 最简正式环境配置...', 'info');
            
            try {
                await initializePaddle();
                
                const minimalConfig = {
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }]
                };
                
                log(`最简配置: ${JSON.stringify(minimalConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(minimalConfig);
                log('✅ 最简配置测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 最简配置测试失败: ${error.message}`, 'error');
            }
        }

        async function testDomainCheck() {
            log('🌐 测试3: 域名配置检查...', 'info');
            
            const currentDomain = window.location.origin;
            log(`当前域名: ${currentDomain}`, 'info');
            
            if (currentDomain.includes('localhost')) {
                log('✅ 检测到localhost环境', 'success');
                log('💡 请确保在正式环境Dashboard中添加了localhost域名', 'warning');
            } else {
                log('✅ 检测到生产域名', 'success');
                log(`💡 请确保在正式环境Dashboard中添加了 ${currentDomain}`, 'warning');
            }
            
            log('🔗 正式环境Dashboard: https://vendors.paddle.com/', 'info');
            log('📍 配置路径: Developer tools > Checkout settings > Allowed domains', 'info');
        }

        // 监听CSP错误
        window.addEventListener('securitypolicyviolation', function(e) {
            log(`🚨 CSP违规检测: ${e.violatedDirective} - ${e.blockedURI}`, 'error');
            log('💡 确认域名白名单配置在正式环境Dashboard中', 'warning');
        });

        // 页面加载时初始化
        window.onload = function() {
            showCurrentDomain();
            log('🚀 Paddle正式环境测试工具已加载', 'info');
            log('⚠️ 检测到CSP和403错误，需要配置正式环境', 'warning');
            log('🎯 请先在正式环境Dashboard中配置域名白名单', 'info');
        };
    </script>
</body>
</html>

import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { useUser } from '../contexts/UserContext';
import { cancelSubscription, canCancelSubscription, isInTrialPeriod } from '../services/userService';
import { log } from '../utils/logger';

// Paddle相关导入已移除，订阅功能改为跳转到定价页面



const ProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser, logout } = useAuth();
  const { userProfile, loading, error, refreshUserProfile } = useUser();
  const { t } = useTranslation();


  const [notification, setNotification] = useState<{ message: string; type: 'success' | 'error' | null }>({
    message: '',
    type: null,
  });
  const [hasRefreshedFromUrl, setHasRefreshedFromUrl] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmModalType, setConfirmModalType] = useState<'cancel' | 'contact'>('cancel');
  const [confirmModalData, setConfirmModalData] = useState<any>(null);
  const [showResultModal, setShowResultModal] = useState(false);
  const [resultModalType, setResultModalType] = useState<'success' | 'failure'>('success');



  // 复制邮箱到剪贴板
  const copyEmailToClipboard = useCallback(async () => {
    const email = '<EMAIL>';
    try {
      await navigator.clipboard.writeText(email);
      setNotification({
        message: '邮箱地址已复制到剪贴板',
        type: 'success'
      });
    } catch (error) {
      console.error('复制邮箱失败:', error);
      // 降级方案：使用传统的复制方法
      const textArea = document.createElement('textarea');
      textArea.value = email;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        setNotification({
          message: '邮箱地址已复制到剪贴板',
          type: 'success'
        });
      } catch (fallbackError) {
        setNotification({
          message: '复制失败，请手动复制邮箱：' + email,
          type: 'error'
        });
      }
      document.body.removeChild(textArea);
    }
  }, []);

  // 打开邮件软件
  const openEmailClient = useCallback(() => {
    const email = '<EMAIL>';
    const subject = encodeURIComponent('AutoFill 退订问题咨询');
    const body = encodeURIComponent('您好，\n\n我在使用 AutoFill 时遇到了退订问题，希望能得到帮助。\n\n谢谢！');
    const mailtoUrl = `mailto:${email}?subject=${subject}&body=${body}`;

    try {
      window.location.href = mailtoUrl;
    } catch (error) {
      console.error('打开邮件软件失败:', error);
      setNotification({
        message: '无法打开邮件软件，请手动发送邮件到：' + email,
        type: 'error'
      });
    }
  }, []);

  // Paddle相关代码已移除，订阅功能改为跳转到定价页面

  // 显示临时通知的辅助函数
  const showTemporaryNotification = (message: string, type: 'success' | 'error') => {
    setNotification({
      message,
      type,
    });

    // 3秒后自动清除通知
    setTimeout(() => {
      setNotification({
        message: '',
        type: null,
      });
    }, 3000);
  };

  // 检查 URL 参数，显示订阅成功通知
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    if (params.get('subscription_success') === 'true' && !hasRefreshedFromUrl) {
      showTemporaryNotification(t('pricing.notifications.success'), 'success');
      // 刷新用户信息以获取最新的订阅状态
      refreshUserProfile();

      setHasRefreshedFromUrl(true);

      // 清理 URL 参数
      navigate(location.pathname, { replace: true });
    }
  }, [location, refreshUserProfile, navigate, hasRefreshedFromUrl, t]);
  
  // 监听Paddle支付成功消息和URL参数
  useEffect(() => {
    // 首先检查URL参数以确定是否是支付成功后的重定向
    const urlParams = new URLSearchParams(window.location.search);
    const paymentSuccess = urlParams.get('paddle_payment_success');
    const subscriptionSuccess = urlParams.get('subscription_success'); // 保持向后兼容
    
    if (paymentSuccess === 'true' || subscriptionSuccess === 'true') {
      log.info('🎉 检测到Paddle支付成功URL参数，刷新用户资料');
      showTemporaryNotification(t('pricing.notifications.success'), 'success');
      
      // 清理URL参数
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('paddle_payment_success');
      newUrl.searchParams.delete('subscription_success');
      window.history.replaceState({}, '', newUrl.toString());
      
      // 延迟刷新用户资料
      setTimeout(() => {
        refreshUserProfile();
      }, 1000);
    }

    // 保留postMessage监听作为备用方案
    const handleMessage = (event: MessageEvent) => {
      log.debug('收到窗口消息:', event.data);
      
      if (event.data && event.data.type === 'paddle_payment_success') {
        const source = event.data.source || 'unknown';
        log.info(`🎉 收到Paddle支付成功消息 (来源: ${source})，刷新用户资料`);
        
        // 显示成功通知
        showTemporaryNotification(t('pricing.notifications.success'), 'success');
        
        // 延迟一下再刷新，确保后端订阅状态已更新
        setTimeout(() => {
          refreshUserProfile();
        }, 1000);
      }
    };
    
    // 监听来自iframe、弹窗等的消息
    window.addEventListener('message', handleMessage);
    
    // 也监听自定义事件（备用方案）
    const handleCustomEvent = (event: any) => {
      if (event.detail && event.detail.type === 'paddle_payment_success') {
        log.info('🎉 收到Paddle支付成功自定义事件');
        showTemporaryNotification(t('pricing.notifications.success'), 'success');
        setTimeout(() => {
          refreshUserProfile();
        }, 1000);
      }
    };
    
    window.addEventListener('paddle_payment_success', handleCustomEvent);
    
    return () => {
      window.removeEventListener('message', handleMessage);
      window.removeEventListener('paddle_payment_success', handleCustomEvent);
    };
  }, [refreshUserProfile, t]);

  // 订阅功能已移除，直接跳转到定价页面





  // 跳转到定价页面
  const handleGoToPricing = () => {
    navigate('/pricing');
  };

  // 处理登出
  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('登出失败:', error);
      // 即使登出失败，也尝试跳转到首页
      navigate('/');
    }
  };

  // 处理退订
  const handleCancelSubscription = useCallback(async () => {
    log.info('开始执行退订操作');
    
    if (!userProfile?.subscription_info) {
      log.warn('没有订阅信息，无法退订');
      setNotification({
        message: '无有效订阅可以取消',
        type: 'error'
      });
      return;
    }

    // 检查是否可以退订（基于后端返回的字段）
    const cancelCheck = canCancelSubscription(userProfile.subscription_info);
    log.debug('最终退订检查结果:', cancelCheck);
    
    if (!cancelCheck.canCancel) {
      log.warn('不能退订，显示错误通知');
      setNotification({
        message: cancelCheck.reason || '无法取消订阅',
        type: 'error'
      });
      return;
    }

    // 直接执行退订，不再显示确认对话框（确认已在悬浮框中完成）
    log.debug('通过退订检查，开始调用API');

    setIsCancelling(true);

    try {
      // 通过后端 API 取消订阅，后端会根据试用期状态自动处理
      const result = await cancelSubscription();

      // 根据响应结果显示悬浮框
      if (result.success) {
        // 退订成功 - 显示成功悬浮框
        setResultModalType('success');
        setShowResultModal(true);

        // 注意：不在这里刷新用户信息，而是在用户关闭弹窗后刷新
        // 这样用户可以先看到成功消息，然后在关闭弹窗时看到更新后的状态
      } else {
        // 退订失败 - 显示失败悬浮框
        log.warn('退订失败，显示失败对话框');
        log.warn('失败原因:', result.errorMessage || result.message || '未知错误');
        setResultModalType('failure');
        setShowResultModal(true);
        
        // 同时显示通知
        setNotification({
          message: result.errorMessage || result.message || '退订失败，请联系客服',
          type: 'error'
        });
      }

    } catch (error: any) {
      log.error('取消订阅失败', error);

      // 网络错误或其他异常 - 显示失败悬浮框
      setResultModalType('failure');
      setShowResultModal(true);
    } finally {
      setIsCancelling(false);
    }
  }, [userProfile, refreshUserProfile]);

  // 处理退订链接点击
  const handleCancelLinkClick = useCallback(() => {
    log.debug('点击退订按钮');
    log.debug('用户信息:', userProfile);
    log.debug('订阅信息:', userProfile?.subscription_info);

    if (!userProfile?.subscription_info) {
      log.warn('没有订阅信息');
      setNotification({
        message: '无有效订阅可以取消',
        type: 'error'
      });
      return;
    }

    // 检查是否可以退订
    const cancelCheck = canCancelSubscription(userProfile.subscription_info);
    log.debug('退订检查结果:', cancelCheck);

    if (cancelCheck.canCancel) {
      // 可以退订，显示退订确认悬浮框
      const isTrialing = isInTrialPeriod(userProfile.subscription_info);
      log.debug('可以退订，试用期状态:', isTrialing);

      setConfirmModalType('cancel');
      setConfirmModalData({
        isTrialing: isTrialing,
        daysRemaining: cancelCheck.daysRemaining
      });
      setShowConfirmModal(true);
      log.debug('显示确认对话框');
    } else {
      // 不能退订，显示联系客服悬浮框
      log.warn('不能退订，原因:', cancelCheck.reason);
      setConfirmModalType('contact');
      setConfirmModalData({
        reason: cancelCheck.reason || '已超过退订期限'
      });
      setShowConfirmModal(true);
    }
  }, [userProfile]);

  // 确认退订
  const handleConfirmCancel = useCallback(async () => {
    setShowConfirmModal(false);
    await handleCancelSubscription();
  }, [handleCancelSubscription]);

  // 复制邮箱地址
  const handleCopyEmail = useCallback(async () => {
    try {
      await navigator.clipboard.writeText('<EMAIL>');
      setNotification({
        message: '邮箱地址已复制到剪贴板',
        type: 'success'
      });
    } catch (error) {
      console.error('复制到剪贴板失败:', error);
      setNotification({
        message: '请手动复制邮箱地址：<EMAIL>',
        type: 'error'
      });
    }
    setShowConfirmModal(false);
  }, []);

  // 处理结果弹窗关闭
  const handleResultModalClose = useCallback(async () => {
    setShowResultModal(false);

    // 如果是取消订阅成功，关闭弹窗后刷新用户数据
    if (resultModalType === 'success') {
      try {
        await refreshUserProfile();
      } catch (error) {
        console.error('刷新用户数据失败:', error);
        // 即使刷新失败也不显示错误，因为用户已经看到了成功消息
      }
    }
  }, [resultModalType, refreshUserProfile]);



  // 通知组件
  const Notification = () => {
    if (!notification.type) return null;

    return (
      <div
        className={`fixed top-4 right-4 p-4 rounded-md shadow-md z-50 ${
          notification.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}
      >
        <div className="flex items-center">
          {notification.type === 'success' ? (
            <svg className="h-5 w-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
          ) : (
            <svg className="h-5 w-5 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          )}
          <span>{notification.message}</span>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 py-8">
      <div className="container mx-auto max-w-6xl px-4">
        {/* 通知 */}
        <Notification />

        {/* Paddle错误显示已移除 */}


        
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-gray-300 border-t-blue-600"></div>
              <p className="mt-4 text-lg text-gray-500 dark:text-gray-400">{t('profile.loading')}</p>
            </div>
          </div>
        ) : error ? (
          <div className="bg-white/80 dark:bg-gray-800/90 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/50 p-8">
            <div className="text-center">
              <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">加载失败</h3>
              <p className="text-red-500 dark:text-red-400 mb-6">{error}</p>
              {error.includes('登录已过期') || error.includes('请登录') ? (
                <button
                  type="button"
                  onClick={() => navigate('/login', { state: { from: location.pathname } })}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  {t('common.login')}
                </button>
              ) : (
                <button
                  type="button"
                  onClick={() => refreshUserProfile()}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  {t('profile.reload')}
                </button>
              )}
            </div>
          </div>
        ) : (
          <div className="max-w-4xl mx-auto">
            {/* 订阅信息 */}
              <div className="bg-white/80 dark:bg-gray-800/90 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/50 p-6">
                {userProfile?.subscription_info && (userProfile.subscription_info.status === 'active' || userProfile.subscription_info.status === 'trialing') ? (
                  <div className="border-2 border-orange-200 dark:border-orange-700 rounded-xl p-6 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20">
                    {/* 头部：账号信息 + Plan + 价格 */}
                    <div className="mb-6">
                      {/* 第一行：账号信息 */}
                      <div className="mb-3">
                        <div className="flex items-center justify-between">
                          <div className="text-gray-700 dark:text-gray-300 text-sm">
                            <span className="font-medium">{t('profile.basic.email')}：</span>
                            <span>{userProfile?.email || t('profile.basic.notSet')}</span>
                          </div>
                          <button
                            type="button"
                            onClick={handleLogout}
                            className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 text-sm transition-colors duration-200"
                          >
                            {t('profile.logout')}
                          </button>
                        </div>
                      </div>

                      {/* 第二行：PREMIUM标签 + Plan + 价格 */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          {/* PREMIUM 标签 */}
                          <div className="bg-orange-500 text-white px-3 py-1 rounded-md text-sm font-medium flex items-center">
                            <span className="mr-1">{t('profile.subscription.labels.premium')}</span>
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                          {/* 试用期标记 */}
                          {userProfile.subscription_info.status === 'trialing' && (
                            <div className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100 px-2 py-1 rounded-md text-xs font-medium">
                              试用期
                            </div>
                          )}
                          <span className="text-gray-600 dark:text-gray-300 font-medium">{t('profile.subscription.labels.plan')}</span>
                        </div>
                        {/* 价格 */}
                        <div className="text-right">
                          <div className="text-3xl font-bold text-gray-900 dark:text-white">
                            $9.99<span className="text-gray-500 dark:text-gray-400 text-sm ml-1">{t('profile.subscription.billing.yearSuffix')}</span>
                          </div>
                        </div>
                      </div>
                    </div>


                    {/* 功能列表和说明信息 */}
                    <div className="grid grid-cols-3 gap-6 mb-6">
                      {/* 左侧：功能列表（两列） */}
                      <div className="col-span-2 grid grid-cols-2 gap-x-6 gap-y-1">
                        <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                          <svg className="w-4 h-4 mr-2 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          {t('profile.subscription.features.unlimitedWebsites')}
                        </div>
                        <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                          <svg className="w-4 h-4 mr-2 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          {t('profile.subscription.features.unlimitedGroups')}
                        </div>
                        <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                          <svg className="w-4 h-4 mr-2 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          {t('profile.subscription.features.unlimitedSubpages')}
                        </div>
                        <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                          <svg className="w-4 h-4 mr-2 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          {t('profile.subscription.features.unlimitedPresets')}
                        </div>
                        <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                          <svg className="w-4 h-4 mr-2 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          {t('profile.subscription.features.unlimitedRules')}
                        </div>
                      </div>

                      {/* 右侧：说明信息 */}
                      <div className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                        <div className="font-medium text-gray-900 dark:text-white mb-2">
                          {t('profile.subscription.billing.nextBillingDate')}: {userProfile.subscription_info.current_period_end
                            ? new Date(userProfile.subscription_info.current_period_end).toLocaleDateString('en-US', {
                                day: '2-digit',
                                month: '2-digit',
                                year: 'numeric'
                              })
                            : (
                                <button
                                  type="button"
                                  onClick={handleGoToPricing}
                                  className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-1 px-3 rounded-md transition-all duration-200 text-xs ml-2"
                                >
                                  {t('profile.subscription.goSubscribe')}
                                </button>
                              )
                          }
                        </div>
                        <div className="text-xs">
                          您将在每次续费时被收取 $9.99，直到您在这个订阅周期开始的七天内
                          <button
                            type="button"
                            onClick={handleCancelLinkClick}
                            className="text-blue-600 hover:text-blue-800 underline mx-1"
                          >
                            点击这里退订
                          </button>
                          ，或者联系我们取消。如果您取消，之前的费用将不予退还，但您可以继续使用服务直到您付费期限结束。
                        </div>
                      </div>
                    </div>

                    {/* 管理按钮 */}
                    <div className="flex gap-2">
                      {userProfile.subscription_info.update_url && (
                        <a
                          href={userProfile.subscription_info.update_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 text-sm"
                        >
                          {t('profile.subscription.updatePayment')}
                        </a>
                      )}


                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* 顶部账号信息栏 */}
                    <div className="border border-gray-200 dark:border-gray-600 rounded-xl p-4 bg-white dark:bg-gray-800">
                      <div className="flex items-center justify-between">
                        <div className="text-gray-700 dark:text-gray-300 text-sm">
                          <span className="font-medium">{t('profile.basic.email')}：</span>
                          <span>{userProfile?.email || t('profile.basic.notSet')}</span>
                        </div>
                        <button
                          type="button"
                          onClick={handleLogout}
                          className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 text-sm transition-colors duration-200"
                        >
                          {t('profile.logout')}
                        </button>
                      </div>
                    </div>

                    {/* 订阅计划对比 */}
                    <div className="space-y-6">
                      {/* 标题 */}
                      <div className="text-center">
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                          {t('profile.subscription.plan')}
                        </h2>
                        <p className="text-gray-600 dark:text-gray-400">
                          {t('profile.subscription.subtitle')}
                        </p>
                      </div>

                      {/* 计划卡片容器 */}
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* FREE 计划卡片 */}
                        <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-xl transition-all duration-300">
                          {/* 卡片头部 */}
                          <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 text-white">
                            <div className="flex items-center justify-between mb-4">
                              <div className="flex items-center space-x-3">
                                <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 flex items-center">
                                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                  <span className="font-semibold text-lg">{t('profile.subscription.labels.free')}</span>
                                </div>
                                <span className="text-blue-100 font-medium">{t('profile.subscription.labels.plan')}</span>
                              </div>
                              <div className="text-right">
                                <div className="text-3xl font-bold">{t('profile.subscription.freePlan.price')}</div>
                                <div className="text-blue-100 text-sm">{t('profile.subscription.freePlan.forever')}</div>
                              </div>
                            </div>
                            <p className="text-blue-100 text-sm">
                              {t('profile.subscription.freePlan.description')}
                            </p>
                          </div>

                          {/* 功能列表 */}
                          <div className="p-6">
                            <div className="space-y-4">
                              <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                                  <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                {t('profile.subscription.freeFeatures.limitedGroups')}
                              </div>
                              <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                                  <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                {t('profile.subscription.freeFeatures.limitedPresets')}
                              </div>
                              <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                                  <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                {t('profile.subscription.freeFeatures.limitedWebsites')}
                              </div>
                              <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                                  <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                {t('profile.subscription.freeFeatures.limitedSubpages')}
                              </div>
                              <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                                  <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                {t('profile.subscription.freeFeatures.limitedRules')}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* PREMIUM 计划卡片 */}
                        <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-orange-200 dark:border-orange-700 overflow-hidden hover:shadow-xl transition-all duration-300">
                          {/* 推荐标签 */}
                          <div className="absolute top-0 right-0 bg-orange-500 text-white px-4 py-1 text-xs font-semibold rounded-bl-lg">
                            {t('profile.subscription.recommended')}
                          </div>
                          
                          {/* 卡片头部 */}
                          <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-6 text-white">
                            <div className="flex items-center justify-between mb-4">
                              <div className="flex items-center space-x-3">
                                <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 flex items-center">
                                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                                  </svg>
                                  <span className="font-semibold text-lg">{t('profile.subscription.labels.premium')}</span>
                                </div>
                                <span className="text-orange-100 font-medium">{t('profile.subscription.labels.plan')}</span>
                              </div>
                              <div className="text-right">
                                <div className="text-3xl font-bold">{t('profile.subscription.premiumPlan.price')}</div>
                                <div className="text-orange-100 text-sm">{t('profile.subscription.billing.yearSuffix')}</div>
                              </div>
                            </div>
                            <p className="text-orange-100 text-sm">
                              {t('profile.subscription.premiumPlan.description')}
                            </p>
                          </div>

                          {/* 功能列表 */}
                          <div className="p-6">
                            <div className="grid grid-cols-1 gap-4">
                              <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                <div className="w-6 h-6 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mr-3">
                                  <svg className="w-4 h-4 text-orange-600 dark:text-orange-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                {t('profile.subscription.features.unlimitedWebsites')}
                              </div>
                              <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                <div className="w-6 h-6 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mr-3">
                                  <svg className="w-4 h-4 text-orange-600 dark:text-orange-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                {t('profile.subscription.features.unlimitedGroups')}
                              </div>
                              <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                <div className="w-6 h-6 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mr-3">
                                  <svg className="w-4 h-4 text-orange-600 dark:text-orange-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                {t('profile.subscription.features.unlimitedSubpages')}
                              </div>
                              <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                <div className="w-6 h-6 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mr-3">
                                  <svg className="w-4 h-4 text-orange-600 dark:text-orange-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                {t('profile.subscription.features.unlimitedPresets')}
                              </div>
                              <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                <div className="w-6 h-6 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mr-3">
                                  <svg className="w-4 h-4 text-orange-600 dark:text-orange-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                {t('profile.subscription.features.unlimitedRules')}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-8">
                        <button
                          type="button"
                          onClick={handleGoToPricing}
                          className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                        >
                          {t('profile.subscription.viewPlans')}
                        </button>
                        <button
                          type="button"
                          onClick={handleGoToPricing}
                          className="w-full sm:w-auto bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-8 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                        >
                          {t('profile.subscription.subscribe')}
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
          </div>
        )}
      </div>

      {/* 确认悬浮框 */}
      {showConfirmModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
            {confirmModalType === 'cancel' ? (
              // 退订确认悬浮框
              <>
                <div className="flex items-center mb-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      确认取消订阅
                    </h3>
                  </div>
                </div>

                <div className="mb-6">
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {confirmModalData?.isTrialing
                      ? '确定要取消试用订阅吗？取消后将立即失去试用权益。'
                      : confirmModalData?.daysRemaining > 0
                        ? `确定要取消付费订阅吗？您还有 ${confirmModalData.daysRemaining} 天的退订期限。取消后将立即停止计费并失去订阅权益。`
                        : '确定要取消付费订阅吗？取消后将立即停止计费并失去订阅权益。'
                    }
                  </p>
                </div>

                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowConfirmModal(false)}
                    className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    取消
                  </button>
                  <button
                    type="button"
                    onClick={handleConfirmCancel}
                    disabled={isCancelling}
                    className="flex-1 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    {isCancelling ? '取消中...' : '确认退订'}
                  </button>
                </div>
              </>
            ) : (
              // 联系客服悬浮框
              <>
                <div className="flex items-center mb-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      联系客服
                    </h3>
                  </div>
                </div>

                <div className="mb-6">
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                    很抱歉，{confirmModalData?.reason}。
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                    如需取消订阅，请发送邮件至：
                  </p>
                  <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg">
                    <code className="text-sm font-mono text-gray-800 dark:text-gray-200">
                      <EMAIL>
                    </code>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    请在邮件中包含您的账号信息，我们会尽快为您处理。
                  </p>
                </div>

                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowConfirmModal(false)}
                    className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    关闭
                  </button>
                  <button
                    type="button"
                    onClick={handleCopyEmail}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    复制邮箱地址
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* 退订结果悬浮框 */}
      {showResultModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full mx-4 p-6">
            {resultModalType === 'success' ? (
              // 退订成功悬浮框
              <>
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      退订成功
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      您的订阅已成功取消
                    </p>
                  </div>
                </div>

                <div className="mb-6">
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    感谢您使用 AutoFill！您的订阅已成功取消，相关权益将立即停止。如果您有任何问题或建议，欢迎随时联系我们。
                  </p>
                </div>

                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={handleResultModalClose}
                    className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
                  >
                    确定
                  </button>
                </div>
              </>
            ) : (
              // 退订失败悬浮框
              <>
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      退订失败
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      无法完成退订操作
                    </p>
                  </div>
                </div>

                <div className="mb-6">
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                    很抱歉，退订操作失败。这可能是由于订阅状态异常或其他技术问题导致的。
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                    如果您需要帮助，请发邮件到：
                  </p>
                  <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 mb-4">
                    <code className="text-sm text-blue-600 dark:text-blue-400 font-mono">
                      <EMAIL>
                    </code>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={handleResultModalClose}
                    className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    关闭
                  </button>
                  <button
                    type="button"
                    onClick={copyEmailToClipboard}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    复制邮箱
                  </button>
                  <button
                    type="button"
                    onClick={openEmailClient}
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    发送邮件
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfilePage;

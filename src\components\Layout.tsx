import React from 'react';
import MainNavigation from './MainNavigation';
import SiteFooter from './SiteFooter';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="flex flex-col min-h-screen">
      <MainNavigation />
      <main className="flex-grow">
        {children}
      </main>
      <SiteFooter />
    </div>
  );
};

export default Layout; 
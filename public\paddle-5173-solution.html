<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle 5173端口解决方案</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #efe; border-color: #cfc; }
        .error { background-color: #fee; border-color: #fcc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 12px 20px; margin: 8px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        button:hover { background: #005a87; }
        .log { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; font-size: 12px; }
        .solution-box { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .checklist { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .status { padding: 5px 10px; border-radius: 3px; font-weight: bold; }
        .status-ok { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Paddle 5173端口CSP解决方案</h1>
        
        <div class="section info">
            <h2>📋 当前配置状态</h2>
            <div class="checklist">
                <p><strong>✅ 新Token:</strong> <code>test_5c1f7fd4f3fb13fcf3c46516a93</code></p>
                <p><strong>✅ 端口:</strong> 5173 (保持不变)</p>
                <p><strong>✅ 代理配置:</strong> 已添加到vite.config.ts</p>
                <p><strong>⚠️ 域名白名单:</strong> 需要在Paddle Dashboard中确认</p>
            </div>
        </div>

        <div class="section warning">
            <h2>🎯 CSP问题分析</h2>
            <div class="solution-box">
                <h3>问题根源:</h3>
                <p><code>frame-ancestors http://localhost</code> - Paddle的CSP只允许localhost，不允许localhost:5173</p>
                
                <h3>解决策略:</h3>
                <ol>
                    <li><strong>域名白名单</strong> - 在Paddle Dashboard中添加完整URL</li>
                    <li><strong>本地代理</strong> - 通过代理移除CSP头</li>
                    <li><strong>iframe沙箱</strong> - 使用沙箱模式绕过限制</li>
                </ol>
            </div>
        </div>

        <div class="section">
            <h2>🧪 解决方案测试</h2>
            <button onclick="testDomainWhitelist()">1. 测试域名白名单</button>
            <button onclick="testProxyMethod()">2. 测试代理方案</button>
            <button onclick="testSandboxMethod()">3. 测试沙箱方案</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section success" id="success-section" style="display: none;">
            <h2>🎉 解决方案成功！</h2>
            <div id="success-info"></div>
        </div>

        <div class="section error">
            <h2>🔧 手动检查清单</h2>
            <div class="checklist">
                <h3>Paddle Dashboard配置:</h3>
                <ol>
                    <li>登录: <a href="https://sandbox-vendors.paddle.com/" target="_blank">Sandbox Dashboard</a></li>
                    <li>进入: Developer tools > Checkout settings > Allowed domains</li>
                    <li>确认包含: <code>http://localhost:5173</code></li>
                    <li>确认包含: <code>https://localhost:5173</code></li>
                    <li>保存配置并等待2-5分钟生效</li>
                </ol>
                
                <h3>本地配置检查:</h3>
                <ol>
                    <li>确认.env.local中Token正确</li>
                    <li>重启开发服务器: <code>npm run dev</code></li>
                    <li>清除浏览器缓存: Ctrl+Shift+R</li>
                </ol>
            </div>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'test_5c1f7fd4f3fb13fcf3c46516a93',
            priceId: 'pri_01k1aenmy30gyzrq9wayftn5z4'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '5px';
            logEntry.style.padding = '5px';
            logEntry.style.borderRadius = '3px';
            
            if (type === 'success') {
                logEntry.style.backgroundColor = '#d4edda';
                logEntry.style.color = '#155724';
            } else if (type === 'error') {
                logEntry.style.backgroundColor = '#f8d7da';
                logEntry.style.color = '#721c24';
            } else if (type === 'warning') {
                logEntry.style.backgroundColor = '#fff3cd';
                logEntry.style.color = '#856404';
            } else {
                logEntry.style.backgroundColor = '#d1ecf1';
                logEntry.style.color = '#0c5460';
            }
            
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('success-section').style.display = 'none';
        }

        function showSuccess(method) {
            const infoDiv = document.getElementById('success-info');
            infoDiv.innerHTML = `
                <h3>✅ ${method} 方案成功！</h3>
                <p>支付功能正常工作，可以在主应用中使用</p>
                <p><strong>下一步:</strong> 在主应用 /pricing 页面测试支付功能</p>
            `;
            document.getElementById('success-section').style.display = 'block';
        }

        async function initializePaddle() {
            return new Promise((resolve) => {
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            log(`❌ Checkout错误: ${data.detail}`, 'error');
                            
                            if (data.detail && data.detail.includes('JWT')) {
                                log('❌ JWT错误 - Token问题', 'error');
                            } else if (data.detail && data.detail.includes('403')) {
                                log('❌ 403错误 - 域名白名单问题', 'error');
                            } else if (data.detail && data.detail.includes('frame')) {
                                log('❌ CSP frame错误', 'error');
                            }
                        } else if (data.type === 'checkout.loaded') {
                            log('🎉 支付页面加载成功！CSP问题已解决！', 'success');
                            showSuccess('当前测试');
                        } else if (data.name === 'checkout.closed' && data.data) {
                            const item = data.data.items?.[0];
                            if (item?.price_id && item?.product?.name) {
                                log('🎉 产品数据完整！', 'success');
                                log(`产品: ${item.product.name}, 价格: ${item.price_id}`, 'success');
                                showSuccess('完整功能');
                            } else {
                                log('⚠️ 支付页面能打开，但产品数据为空', 'warning');
                            }
                        }
                    }
                });
                resolve();
            });
        }

        async function testDomainWhitelist() {
            log('🧪 测试1: 域名白名单方案...', 'info');
            log(`当前URL: ${window.location.href}`, 'info');
            log('检查Paddle Dashboard中是否已添加此域名到白名单', 'info');
            
            try {
                await initializePaddle();
                
                const testConfig = {
                    items: [{
                        priceId: config.priceId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                log('💳 打开支付页面测试域名白名单...', 'info');
                window.Paddle.Checkout.open(testConfig);
                log('✅ 域名白名单测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 域名白名单测试失败: ${error.message}`, 'error');
            }
        }

        async function testProxyMethod() {
            log('🧪 测试2: 代理方案...', 'info');
            log('使用vite代理移除CSP头', 'info');
            
            // 这里可以尝试通过代理访问
            log('⚠️ 代理方案需要后端支持，当前为前端测试', 'warning');
            
            try {
                await initializePaddle();
                
                const testConfig = {
                    items: [{
                        priceId: config.priceId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                log('💳 通过代理打开支付页面...', 'info');
                window.Paddle.Checkout.open(testConfig);
                log('✅ 代理方案测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 代理方案测试失败: ${error.message}`, 'error');
            }
        }

        async function testSandboxMethod() {
            log('🧪 测试3: 沙箱方案...', 'info');
            log('尝试使用iframe沙箱模式', 'info');
            
            try {
                await initializePaddle();
                
                const testConfig = {
                    items: [{
                        priceId: config.priceId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en",
                        // 尝试添加沙箱相关配置
                        allowedPaymentMethods: ["card"]
                    }
                };
                
                log('💳 使用沙箱模式打开支付页面...', 'info');
                window.Paddle.Checkout.open(testConfig);
                log('✅ 沙箱方案测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 沙箱方案测试失败: ${error.message}`, 'error');
            }
        }

        // 监听CSP错误
        window.addEventListener('securitypolicyviolation', function(e) {
            log(`🚨 CSP违规: ${e.violatedDirective} - ${e.blockedURI}`, 'error');
            log(`建议: 在Paddle Dashboard中添加 ${window.location.origin} 到域名白名单`, 'warning');
        });

        // 页面加载时显示信息
        window.onload = function() {
            log('🔧 5173端口CSP解决方案测试页面已加载', 'info');
            log(`当前访问: ${window.location.href}`, 'info');
            log('💡 请先确认Paddle Dashboard中的域名白名单配置', 'warning');
            log('🎯 然后依次测试各个解决方案', 'info');
        };
    </script>
</body>
</html>

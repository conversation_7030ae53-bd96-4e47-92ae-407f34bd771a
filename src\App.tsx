import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

// 导入页面组件
import HomePage from './pages/HomePage';
import PricingPage from './pages/PricingPage';
import ProfilePage from './pages/ProfilePage';
import FeaturesPage from './pages/FeaturesPage';
import LoginPage from './pages/LoginPage';
import UserAgreementPage from './pages/UserAgreementPage';
import PrivacyPolicyPage from './pages/PrivacyPolicyPage';
import RefundPolicyPage from './pages/RefundPolicyPage';
import CheckoutPage from './pages/CheckoutPage';
import PaddleTestPage from './pages/PaddleTestPage';
import DeviceIdTestPage from './pages/DeviceIdTestPage';

// 导入布局和保护组件
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';

// 导入Context
import { AuthProvider } from './contexts/AuthContext';
import { UserProvider } from './contexts/UserContext';

// 初始化插件监控器和调试命令（仅开发环境）
if (import.meta.env.DEV) {
  import('./utils/ExtensionMonitor');
  import('./utils/ConsoleCommands');
}

// 初始化Cookie保护（仅在明确启用时）
if (import.meta.env.VITE_ENABLE_COOKIE_PROTECTION === 'true') {
  import('./utils/CookieProtection').then(({ cookieProtection }) => {
    cookieProtection.enableProtection();
  });
}

// 预加载设备ID（在应用启动时）
import('./services/deviceIdService').then(({ preloadDeviceId }) => {
  preloadDeviceId().catch(error => {
    console.warn('设备ID预加载失败:', error);
  });
});

const App: React.FC = () => {
  return (
    <AuthProvider>
      <UserProvider>
        <Layout>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/pricing" element={<PricingPage />} />
            <Route path="/checkout" element={<CheckoutPage />} />
            <Route path="/paddle-test" element={<PaddleTestPage />} />
            <Route path="/device-id-test" element={<DeviceIdTestPage />} />
            <Route
              path="/profile"
              element={
                <ProtectedRoute>
                  <ProfilePage />
                </ProtectedRoute>
              }
            />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/features" element={<FeaturesPage />} />
            <Route path="/user-agreement" element={<UserAgreementPage />} />
            <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
            <Route path="/refund-policy" element={<RefundPolicyPage />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Layout>
      </UserProvider>
    </AuthProvider>
  );
};

export default App; 
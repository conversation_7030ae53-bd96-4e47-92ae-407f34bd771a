import React from 'react';

const PrivacyPolicyPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-white text-[#23272f]">
      <div className="max-w-3xl mx-auto py-12 px-4">
        <h1 className="text-5xl font-extrabold mb-8 leading-tight">Privacy Policy</h1>
        <p className="text-base mb-8 leading-relaxed">
          This Privacy Policy governs how the Autofill Assistant (hereinafter "the Plugin") collects, uses, maintains, and discloses information collected from users (each, a "User") of the Plugin. This Privacy Policy applies solely to the Plugin.
        </p>
        <p className="text-base mb-8 leading-relaxed">
          Your privacy is critically important to us. We are committed to protecting your personal data and greatly value the trust you place in us. We understand your concerns about personal data, especially sensitive information you input.
        </p>

        <h2 className="text-2xl font-bold mt-10 mb-4">What is Personal Data?</h2>
        <p className="text-base mb-8 leading-relaxed">
          Personal data is any information that relates to an identified or identifiable living individual. Different pieces of information, which collected together can lead to the identification of a particular person, also constitute personal data. Personal data that has been de-identified, encrypted, or pseudonymized but can still be used to re-identify a person is still personal data.
        </p>

        <h2 className="text-2xl font-bold mt-10 mb-4">Important Note Regarding Your Preset Autofill Data</h2>
        <p className="text-base mb-8 leading-relaxed">
          It is crucial to understand that all preset autofill information you manually create through the Autofill Assistant service (including preset response content and associated website settings) is stored entirely on your local device (your browser). We do not collect, access, store, or upload this data to any servers. This means your preset data is kept entirely by you.
        </p>
        <p className="text-base mb-8 leading-relaxed">
          Because your preset data is stored locally, we cannot provide cloud backup or synchronization services for you (unless specifically stated otherwise and with your explicit consent, which is not offered in the current version). Therefore, we strongly recommend that you regularly back up your preset data. We have provided a convenient export function for all users, which you can use through the Plugin interface to save a copy of your preset data. Before changing devices, reinstalling your browser, or clearing browser data, be sure to export your data to avoid losing it. The backup and management of your data are solely your responsibility.
        </p>

        <h2 className="text-2xl font-bold mt-10 mb-4">Information We Collect</h2>
        <p className="text-base mb-8 leading-relaxed">
          In order to provide and improve the functionality of our Plugin (subscription features and overall plugin performance improvements), we may collect the following information:
        </p>
        <h3 className="text-xl font-semibold mt-6 mb-3">Information You Provide Directly to Us:</h3>
        <ul className="list-disc pl-6 mb-4">
          <li>Account Information (if you choose to register): If you choose to register for an account to use subscription features, we may collect necessary information such as your email address for account creation and management. This information is used solely to verify your subscription eligibility and manage your account.</li>
          <li>Support-Related Communication: Information you provide during communication if you choose to contact our technical support.</li>
        </ul>
        <h3 className="text-xl font-semibold mt-6 mb-3">Non-Personally Identifiable Information Collected Automatically:</h3>
        <ul className="list-disc pl-6 mb-8">
          <li>When you interact with our Plugin, we may automatically collect some non-personally identifiable information. This information is not associated with your identity, such as:</li>
          <li>Plugin version</li>
          <li>Browser type and version</li>
          <li>Operating system type</li>
          <li>Plugin usage data (e.g., plugin installation/uninstallation events, plugin feature enable/disable status, number of times plugin configuration settings are changed). This usage data is aggregated and anonymized, intended to help us understand overall plugin usage patterns for feature improvements and bug fixes. We do not track your specific activity on specific websites or collect the content of pages you visit.</li>
          <li>Technical errors or crash reports encountered by the Plugin (usually containing anonymized technical information, not containing your personal data or your preset response content).</li>
        </ul>

        <h2 className="text-2xl font-bold mt-10 mb-4">Data We Do Not Collect or Process</h2>
        <p className="text-base mb-8 leading-relaxed">
          Please pay special attention to the following key commitments:
        </p>
        <ul className="list-disc pl-6 mb-8">
          <li>
            <strong>Your Preset Response Content and Associated Website Settings:</strong> The preset response content you create, edit, and manage in the Plugin, as well as the websites (such as URLs or domains) you set for these presets, are completely stored locally in your browser. We pledge not to collect, access, store, or upload this data to any servers. This data resides entirely on your local device, controlled and managed by you. They are the basis of the Plugin's core function (quick autofill), but they will never leave your browser environment.
          </li>
          <li>
            <strong>Data You Actually Autofill into Web Input Fields:</strong> When you use the Plugin to autofill preset response content into web input fields, this operation is performed locally in your browser. The Plugin does not log or upload any actual data you autofill into web input fields. We cannot know which website or which input field you filled the preset content into, nor can we obtain the actual content you filled.
          </li>
          <li>
            <strong>Your Browsing History or Webpage Content:</strong> Our Plugin does not collect your browsing history, nor does it actively read or analyze the full content of the webpages you are visiting, except for limited interaction required by browser APIs to enable the autofill function (e.g., identifying input fields).
          </li>
          <li>
            <strong>Payment Information:</strong> We use a third-party payment gateway, Paddle, to process subscription payments. We do not collect, process, use, or control your sensitive payment information such as credit card numbers or bank account information. Your payment information is handled directly by Paddle. Please review Paddle's Privacy Policy (<a href="https://www.paddle.com/legal/privacy" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">https://www.paddle.com/legal/privacy</a>) to understand how they handle your payment information.
          </li>
        </ul>

        <h2 className="text-2xl font-bold mt-10 mb-4">Web Browser Cookies</h2>
        <p className="text-base mb-8 leading-relaxed">
          Our Plugin itself does not use traditional HTTP Cookies to track your behavior or store personal data.
        </p>
        <p className="text-base mb-8 leading-relaxed">
          However, your browser itself and the websites you visit do use Cookies. When our Plugin is running, it interacts with the webpage you are browsing. These webpages may use Cookies. Please note that our Plugin does not read, collect, or interfere with the settings or stored Cookies of the websites you visit, except for the minimum information required by browser APIs to enable the autofill function (e.g., identifying input fields for autofill).
        </p>
        <p className="text-base mb-8 leading-relaxed">
          Your Plugin primarily relies on the local storage capabilities provided by the browser (e.g., localStorage or indexedDB) to securely and locally store your preset response content and related settings. This is different from traditional Cookies and is typically used for storing larger, more structured data, and this data is also accessible only within your local browser environment.
        </p>
        <p className="text-base mb-8 leading-relaxed">
          You can manage or clear Cookies and local storage data through your browser settings. Please refer to your browser's help documentation for details.
        </p>

        <h2 className="text-2xl font-bold mt-10 mb-4">How We Use Collected Information</h2>
        <p className="text-base mb-8 leading-relaxed">
          The information we collect (namely, account information and non-personally identifiable usage data) is primarily used for the following purposes:
        </p>
        <ul className="list-disc pl-6 mb-8">
          <li>
            <strong>Account Management and Subscription Verification (if you choose to register):</strong> If you register for an account and subscribe to paid features, we will use your account information to verify and manage your subscription status, ensuring you can properly use the paid services. The sole purpose of login and registration is to provide subscription services.
          </li>
          <li>
            <strong>Improving Our Plugin:</strong> Using the automatically collected non-personally identifiable information to analyze overall plugin usage trends, identify potential issues, fix bugs, and optimize and improve the performance and user experience of the Plugin based on user needs.
          </li>
          <li>
            <strong>Providing Technical Support:</strong> Responding to your technical support requests and helping you resolve issues encountered while using the Plugin.
          </li>
        </ul>

        <h2 className="text-2xl font-bold mt-10 mb-4">How We Protect Your Information</h2>
        <p className="text-base mb-8 leading-relaxed">
          We adopt appropriate data collection, storage and processing practices, and security measures to protect the information we collect, preventing unauthorized access, alteration, disclosure, or destruction. Specific measures include:
        </p>
        <ul className="list-disc pl-6 mb-8">
          <li>
            <strong>Security of Local Storage:</strong> Your most important Plugin data (preset response content and website settings) is stored locally in your browser's storage. The security mechanisms of the browser provide basic protection. While we cannot fully control your local device environment, we ensure that the Plugin's code itself does not handle this data in an insecure manner and will not send it to remote servers.
          </li>
          <li>
            <strong>Protection of Account Information:</strong> If you register for an account, your account information is stored on our servers, and we will implement industry-standard encryption measures and access controls to protect this information.
          </li>
          <li>
            <strong>Security of Communication with Servers:</strong> If the Plugin needs to communicate with our servers (e.g., for subscription verification), we will use secured connections (such as HTTPS) to protect the data during transmission.
          </li>
        </ul>

        <h2 className="text-2xl font-bold mt-10 mb-4">Sharing Your Information</h2>
        <p className="text-base mb-8 leading-relaxed">
          We do not sell, trade, or rent your personally identifiable information to others. Your preset response content and website settings, due to being locally stored, are not shared at all. We will not share any personal data that could identify you, except for the minimum information required to provide your subscription features (such as verifying whether you are a subscriber).
        </p>
        <ul className="list-disc pl-6 mb-8">
          <li>
            <strong>Service Providers:</strong> We may employ third-party service providers to help us operate the Plugin or provide related services, such as analytics services (used to understand overall plugin usage) or error reporting services. These service providers can only process non-personally identifiable information and are authorized to use this information solely for the purpose of providing these services to us, and are subject to strict confidentiality obligations.
          </li>
          <li>
            <strong>Payment Processors:</strong> We use Paddle as a third-party payment processor. When you make a subscription payment, your payment information is shared directly with Paddle, and we do not obtain or store your sensitive payment information. Please refer to Paddle's Privacy Policy <a href="https://www.paddle.com/legal/privacy" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">https://www.paddle.com/legal/privacy</a> to understand how they handle your payment information.
          </li>
        </ul>

        <h2 className="text-2xl font-bold mt-10 mb-4">Children's Privacy</h2>
        <p className="text-base mb-8 leading-relaxed">
          Our Plugin is not directed at children, and we do not knowingly collect personal data from children under 13 years of age. If you find that a child is using our Plugin and has provided personal data to us (e.g., registered an account), please contact us through our support channels, and we will take steps to delete this information.
        </p>

        <h2 className="text-2xl font-bold mt-10 mb-4">Changes to This Privacy Policy</h2>
        <p className="text-base mb-8 leading-relaxed">
          We have the discretion to update this Privacy Policy at any time. When we do, we will revise the updated date at the bottom of this Privacy Policy. We may notify you of significant changes to the Privacy Policy through plugin update notes, plugin store page notifications, or by displaying a prompt within the Plugin interface. We encourage you to regularly check this Privacy Policy to stay informed about how we are helping to protect the information we collect. Your continued use of the Plugin after changes to this Privacy Policy will be deemed your acceptance of those changes.
        </p>

        <h2 className="text-2xl font-bold mt-10 mb-4">Your Acceptance of These Terms</h2>
        <p className="text-base mb-8 leading-relaxed">
          By installing or using this Plugin, you signify your acceptance of this Privacy Policy. If you do not agree to this policy, please do not install or use this Plugin. Your continued use of the Plugin following the posting of changes to this Privacy Policy will be deemed your acceptance of those changes.
        </p>

        <h2 className="text-2xl font-bold mt-10 mb-4">Contacting Us</h2>
        <p className="text-base mb-4 leading-relaxed">
          If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.
        </p>

        <div className="mt-10 pt-6 border-t border-gray-200 text-sm text-gray-500 text-center">
          <p>Last updated: April 27, 2025</p>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicyPage;
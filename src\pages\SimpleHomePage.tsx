import React from 'react';

const SimpleHomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">🏠 简单主页测试</h1>
          
          <div className="space-y-4">
            <p className="text-lg text-gray-700">
              如果您能看到这个页面，说明React应用基本功能正常。
            </p>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-blue-900 mb-2">✅ 正常工作的功能</h2>
              <ul className="text-blue-800 space-y-1">
                <li>• React组件渲染</li>
                <li>• Tailwind CSS样式</li>
                <li>• 路由系统</li>
                <li>• 基本布局</li>
              </ul>
            </div>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-yellow-900 mb-2">🔧 下一步</h2>
              <p className="text-yellow-800">
                如果这个页面正常显示，说明问题出在原始HomePage组件中。
                可能是翻译文件、复杂动画或其他组件导致的问题。
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h3 className="font-semibold text-green-900">测试链接</h3>
                <ul className="text-green-800 mt-2 space-y-1">
                  <li><a href="/features" className="underline hover:text-green-600">功能页面</a></li>
                  <li><a href="/pricing" className="underline hover:text-green-600">定价页面</a></li>
                  <li><a href="/login" className="underline hover:text-green-600">登录页面</a></li>
                </ul>
              </div>
              
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <h3 className="font-semibold text-purple-900">调试工具</h3>
                <ul className="text-purple-800 mt-2 space-y-1">
                  <li><a href="/device-id-test" className="underline hover:text-purple-600">设备ID测试</a></li>
                  <li><a href="/paddle-test" className="underline hover:text-purple-600">Paddle测试</a></li>
                  <li><a href="/test-homepage.html" className="underline hover:text-purple-600">诊断工具</a></li>
                </ul>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-gray-100 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">🔍 系统信息</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p><strong>当前时间:</strong> {new Date().toLocaleString()}</p>
                <p><strong>用户代理:</strong> {navigator.userAgent}</p>
                <p><strong>屏幕分辨率:</strong> {screen.width} x {screen.height}</p>
                <p><strong>语言:</strong> {navigator.language}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleHomePage;

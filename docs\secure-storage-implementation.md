# 🔐 安全存储实现方案

## 概述

本项目实现了**分层加密存储**方案，确保敏感数据（如access_token、refresh_token、user_id）在浏览器和插件中都以加密形式存储，同时保持网站和插件之间的数据同步。

## 🛡️ 安全特性

### 1. 分层存储策略
- **敏感数据**: `access_token`, `refresh_token`, `user_id` - 加密存储
- **一般数据**: `user_email`, `subscription_info` - 明文存储  
- **配置数据**: 其他设置 - 明文存储

### 2. 加密算法
- **算法**: XOR加密 + Base64编码
- **密钥生成**: 基于设备指纹（UserAgent + 语言 + 屏幕分辨率 + 时区）
- **标识前缀**: `ENCRYPTED:` 用于识别加密数据

### 3. 双端同步
- **网站端**: 使用 `storageAdapter.ts` 统一管理
- **插件端**: 使用 `secure-storage.js` 保持一致性
- **数据同步**: 网站 ↔ 插件实时双向同步

## 📁 文件结构

```
src/services/
├── storageAdapter.ts          # 网站端安全存储适配器
└── StorageBridge.ts          # 存储桥接服务

extension/
├── secure-storage.js         # 插件端安全存储工具
├── background.js            # 后台脚本（已更新）
├── popup.js                # 弹窗脚本（已更新）
├── content-script.js       # 内容脚本（已更新）
└── manifest.json           # 清单文件（已更新）

docs/
└── secure-storage-implementation.md  # 本文档

public/
└── secure-storage-test.html  # 测试页面
```

## 🔧 核心实现

### 1. 加密类 (SecureStorageEncryption)

```typescript
class SecureStorageEncryption {
  // 敏感数据键名定义
  private static readonly SENSITIVE_KEYS = [
    'access_token', 'refresh_token', 'user_id'
  ];
  
  // 设备指纹密钥生成
  private static generateDeviceKey(): string
  
  // 数据加密/解密
  public static encrypt(data: string): string
  public static decrypt(encryptedData: string): string
  
  // 存储处理
  public static processForStorage(key: string, value: any): any
  public static processFromStorage(key: string, value: any): any
}
```

### 2. 存储适配器更新

**网站端 (storageAdapter.ts)**:
- ✅ 自动加密敏感数据
- ✅ 自动解密读取数据  
- ✅ 支持localStorage和chrome.storage双重存储
- ✅ 数据迁移功能

**插件端 (secure-storage.js)**:
- ✅ 与网站端相同的加密逻辑
- ✅ 安全存储适配器类
- ✅ 支持批量操作

### 3. 数据迁移

应用启动时自动执行：
```typescript
// 在 main.tsx 中调用
migrateToSecureStorage().catch(error => {
  console.error('安全存储迁移失败:', error);
});
```

## 🚀 使用方法

### 网站端使用

```typescript
import { setStorageItem, getStorageItem } from './services/storageAdapter';

// 存储（自动加密敏感数据）
await setStorageItem('access_token', 'your_token_here');
await setStorageItem('user_email', '<EMAIL>'); // 不加密

// 读取（自动解密）
const token = await getStorageItem('access_token'); // 自动解密
const email = await getStorageItem('user_email');   // 直接返回
```

### 插件端使用

```javascript
// 存储
SecureStorageAdapter.setItem('access_token', 'your_token_here', () => {
  console.log('存储完成');
});

// 读取
SecureStorageAdapter.getItem('access_token', (decryptedValue) => {
  console.log('解密后的token:', decryptedValue);
});
```

## 🔍 安全验证

### 1. 查看加密效果

打开浏览器开发者工具 → Application → Local Storage：
- **加密前**: `access_token: "eyJhbGciOiJIUzI1NiIs..."`
- **加密后**: `access_token: "ENCRYPTED:SGVsbG9Xb3JsZA=="`

### 2. 测试页面

访问 `/secure-storage-test.html` 进行功能测试：
- ✅ 存储测试数据
- ✅ 查看原始存储内容  
- ✅ 读取解密后的数据
- ✅ 插件存储同步测试
- ✅ 数据迁移测试

## 📊 安全等级对比

| 存储方式 | 安全等级 | XSS防护 | 开发者工具可见性 | 性能影响 |
|---------|---------|---------|-----------------|---------|
| 明文存储 | ⭐ | ❌ | 完全可见 | 无 |
| 客户端加密 | ⭐⭐⭐⭐ | ⚠️ 部分 | 加密内容 | 极小 |
| HttpOnly Cookie | ⭐⭐⭐⭐⭐ | ✅ 完全 | 不可见 | 小 |

## 🎯 优势

1. **向后兼容**: 现有代码无需修改，自动处理加密/解密
2. **透明操作**: 开发者无需关心加密细节
3. **双端同步**: 网站和插件数据完美同步
4. **渐进增强**: 可以逐步升级到更高安全级别
5. **性能优秀**: 加密开销极小，用户体验无影响

## 🔄 升级路径

**当前实现** (分层加密存储)
↓
**未来升级** (HttpOnly Cookie + 短期Token)
↓  
**最终目标** (Token代理模式)

## 🚨 注意事项

1. **密钥安全**: 当前基于设备指纹生成密钥，相对安全但不是绝对安全
2. **XSS防护**: 仍需要其他XSS防护措施配合
3. **数据迁移**: 首次部署时会自动迁移现有明文数据
4. **插件权限**: 确保插件有storage权限

## 📝 测试清单

- [ ] 网站端敏感数据加密存储
- [ ] 插件端敏感数据加密存储  
- [ ] 网站↔插件数据同步
- [ ] 数据迁移功能
- [ ] 用户登录/登出流程
- [ ] 多标签页数据同步
- [ ] 插件弹窗显示正确用户信息

## 🎉 部署完成

安全存储方案已成功实现！现在你的敏感数据将以加密形式存储，大大提升了应用的安全性。

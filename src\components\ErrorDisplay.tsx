import React from 'react';

interface ErrorDisplayProps {
  title: string;
  message: string;
  details?: string;
  actionText?: string;
  onAction?: () => void;
}

/**
 * Generic error display component
 */
const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  title,
  message,
  details,
  actionText = 'Refresh Page',
  onAction = () => window.location.reload()
}) => {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100 dark:bg-gray-950 p-4 text-center">
      <h1 className="text-2xl font-bold text-red-600 mb-4">{title}</h1>
      <p className="mb-4 text-gray-700 dark:text-gray-300">{message}</p>
      {details && (
        <p className="mb-6 text-sm text-gray-500 dark:text-gray-400">
          {details}
        </p>
      )}
      <button
        onClick={onAction}
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        {actionText}
      </button>
    </div>
  );
};

/**
 * API configuration error display component
 */
export const ApiConfigError: React.FC<{ error: string }> = ({ error }) => {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100 dark:bg-gray-950 p-4 text-center">
      <h1 className="text-2xl font-bold text-yellow-600 mb-4">API Configuration Error</h1>
      <p className="mb-4 text-gray-700 dark:text-gray-300">{error}</p>
      <p className="mb-6 text-sm text-gray-500 dark:text-gray-400">
        Please check your environment variables to ensure the correct API URL and API key are set.
      </p>
      <div className="space-y-2">
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          Create a <code className="bg-gray-200 dark:bg-gray-800 px-1 rounded">.env.local</code> file in the project root directory and add:
        </p>
        <pre className="bg-gray-200 dark:bg-gray-800 p-2 rounded text-left text-sm">
          VITE_API_URL=https://autoapi.utilityapp.site<br/>
          VITE_API_KEY=your_api_key
        </pre>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mt-4"
        >
          Reload
        </button>
      </div>
    </div>
  );
};

export default ErrorDisplay; 
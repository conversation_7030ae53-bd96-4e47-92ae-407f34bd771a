<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码登录测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #efe; border-color: #cfc; }
        .error { background-color: #fee; border-color: #fcc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 12px 20px; margin: 8px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        button:hover { background: #005a87; }
        input { padding: 10px; width: 300px; border: 1px solid #ddd; border-radius: 3px; margin: 5px; }
        .log { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; font-size: 12px; }
        .test-result { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .highlight { background: #ffeb3b; padding: 2px 4px; border-radius: 2px; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #007cba; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 验证码登录接口测试</h1>
        
        <div class="section info">
            <h2>📋 测试目标</h2>
            <p>验证 <code>POST /auth/verify-code</code> 接口的完整功能：</p>
            <ul>
                <li>✅ 请求体包含 <span class="highlight">device_id</span> 参数</li>
                <li>✅ 成功响应保持现状</li>
                <li>✅ 失败响应统一提示："验证失败，请稍后重试，请联系***********************"</li>
                <li>✅ 设备ID与请求验证码时保持一致</li>
            </ul>
        </div>

        <div class="section">
            <h2>🧪 测试步骤</h2>
            
            <div class="step">
                <h3>步骤1: 请求验证码</h3>
                <label><strong>邮箱地址:</strong></label><br>
                <input type="email" id="email" value="<EMAIL>" placeholder="输入邮箱地址">
                <br>
                <button onclick="requestCode()">🚀 请求验证码</button>
            </div>

            <div class="step">
                <h3>步骤2: 验证登录</h3>
                <label><strong>验证码:</strong></label><br>
                <input type="text" id="code" placeholder="输入6位验证码" maxlength="6">
                <br>
                <button onclick="verifyCode()">🔐 验证登录</button>
                <button onclick="testInvalidCode()">❌ 测试错误验证码</button>
            </div>

            <div class="step">
                <h3>步骤3: 清空日志</h3>
                <button onclick="clearLog()">🗑️ 清空日志</button>
            </div>
        </div>

        <div class="section" id="request-details" style="display: none;">
            <h2>📊 请求详情</h2>
            <div id="request-content"></div>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section warning">
            <h2>⚠️ 预期结果</h2>
            <div class="test-result">
                <h3>✅ 成功情况</h3>
                <ul>
                    <li><strong>请求体包含:</strong> email, code, device_id</li>
                    <li><strong>响应格式:</strong> access_token, refresh_token, token_type 等</li>
                    <li><strong>设备ID一致:</strong> 与请求验证码时相同</li>
                </ul>
                
                <h3>❌ 失败情况</h3>
                <ul>
                    <li><strong>统一错误信息:</strong> "验证失败，请稍后重试，请联系***********************"</li>
                    <li><strong>所有错误码:</strong> 400, 401, 404, 422, 500 都显示相同信息</li>
                    <li><strong>网络错误:</strong> 也显示统一错误信息</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let currentDeviceId = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '5px';
            logEntry.style.padding = '5px';
            logEntry.style.borderRadius = '3px';
            
            if (type === 'success') {
                logEntry.style.backgroundColor = '#d4edda';
                logEntry.style.color = '#155724';
            } else if (type === 'error') {
                logEntry.style.backgroundColor = '#f8d7da';
                logEntry.style.color = '#721c24';
            } else if (type === 'warning') {
                logEntry.style.backgroundColor = '#fff3cd';
                logEntry.style.color = '#856404';
            } else {
                logEntry.style.backgroundColor = '#d1ecf1';
                logEntry.style.color = '#0c5460';
            }
            
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('request-details').style.display = 'none';
        }

        function showRequestDetails(title, requestData, responseData) {
            const contentDiv = document.getElementById('request-content');
            
            // 检查 device_id
            const hasDeviceId = requestData.device_id ? true : false;
            const deviceIdValid = hasDeviceId && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(requestData.device_id);
            
            contentDiv.innerHTML = `
                <div class="test-result">
                    <h3>📤 ${title}</h3>
                    <p><strong>device_id 状态:</strong> 
                        <span style="color: ${hasDeviceId ? '#28a745' : '#dc3545'};">
                            ${hasDeviceId ? '✅ 存在' : '❌ 缺失'}
                        </span>
                    </p>
                    ${hasDeviceId ? `
                        <p><strong>device_id 值:</strong> <code>${requestData.device_id}</code></p>
                        <p><strong>UUID 格式:</strong> 
                            <span style="color: ${deviceIdValid ? '#28a745' : '#dc3545'};">
                                ${deviceIdValid ? '✅ 有效' : '❌ 无效'}
                            </span>
                        </p>
                        ${currentDeviceId ? `
                            <p><strong>设备ID一致性:</strong> 
                                <span style="color: ${requestData.device_id === currentDeviceId ? '#28a745' : '#dc3545'};">
                                    ${requestData.device_id === currentDeviceId ? '✅ 一致' : '❌ 不一致'}
                                </span>
                            </p>
                        ` : ''}
                    ` : ''}
                    
                    <h4>完整请求体:</h4>
                    <pre>${JSON.stringify(requestData, null, 2)}</pre>
                    
                    <h4>响应数据:</h4>
                    <pre>${JSON.stringify(responseData, null, 2)}</pre>
                </div>
            `;
            
            document.getElementById('request-details').style.display = 'block';
        }

        // 简化的设备ID生成
        async function generateDeviceId() {
            const features = {
                screen: `${screen.width}x${screen.height}x${screen.colorDepth}`,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                platform: navigator.platform || 'Unknown',
                language: navigator.language,
                userAgent: navigator.userAgent.replace(/\d+\.\d+\.\d+(\.\d+)?/g, 'X.X.X')
            };
            
            const featuresString = JSON.stringify(features);
            const encoder = new TextEncoder();
            const data = encoder.encode(featuresString);
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            const hash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
            
            const paddedHash = (hash + '0'.repeat(32)).substring(0, 32);
            return [
                paddedHash.substring(0, 8),
                paddedHash.substring(8, 12),
                '4' + paddedHash.substring(13, 16),
                ((parseInt(paddedHash.substring(16, 17), 16) & 0x3) | 0x8).toString(16) + paddedHash.substring(17, 20),
                paddedHash.substring(20, 32)
            ].join('-');
        }

        async function requestCode() {
            const email = document.getElementById('email').value.trim();
            
            if (!email) {
                log('❌ 请输入邮箱地址', 'error');
                return;
            }
            
            log('🚀 开始请求验证码...', 'info');
            
            try {
                // 生成设备ID并保存
                currentDeviceId = await generateDeviceId();
                log(`🔍 生成设备ID: ${currentDeviceId}`, 'success');
                
                const requestData = {
                    email: email,
                    device_id: currentDeviceId,
                    device_info: {
                        userAgent: navigator.userAgent,
                        platform: navigator.platform,
                        screen: `${screen.width}x${screen.height}`,
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    }
                };
                
                const response = await fetch('http://localhost:5173/api/auth/request-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                const responseData = await response.json();
                
                showRequestDetails('请求验证码', requestData, responseData);
                
                if (response.ok) {
                    log(`✅ 验证码请求成功，状态码: ${responseData.message}`, 'success');
                } else {
                    log(`❌ 验证码请求失败: ${response.status}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        async function verifyCode() {
            const email = document.getElementById('email').value.trim();
            const code = document.getElementById('code').value.trim();
            
            if (!email || !code) {
                log('❌ 请输入邮箱地址和验证码', 'error');
                return;
            }
            
            if (!currentDeviceId) {
                log('❌ 请先请求验证码以生成设备ID', 'error');
                return;
            }
            
            log('🔐 开始验证登录...', 'info');
            
            try {
                const requestData = {
                    email: email,
                    code: code,
                    device_id: currentDeviceId
                };
                
                log(`📤 发送验证请求，设备ID: ${currentDeviceId}`, 'info');
                
                const response = await fetch('http://localhost:5173/api/auth/verify-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                let responseData;
                try {
                    const responseText = await response.text();
                    responseData = responseText ? JSON.parse(responseText) : {};
                } catch (parseError) {
                    responseData = { error: '响应解析失败' };
                }
                
                showRequestDetails('验证码登录', requestData, responseData);
                
                if (response.ok) {
                    log('🎉 登录成功！', 'success');
                    log(`✅ 获得访问令牌: ${responseData.access_token ? '是' : '否'}`, 'success');
                    log(`✅ 获得刷新令牌: ${responseData.refresh_token ? '是' : '否'}`, 'success');
                } else {
                    log(`❌ 登录失败，状态码: ${response.status}`, 'error');
                    log(`📝 错误信息: ${JSON.stringify(responseData)}`, 'error');
                    log('💡 前端应显示: "验证失败，请稍后重试，请联系***********************"', 'warning');
                }
                
            } catch (error) {
                log(`❌ 验证失败: ${error.message}`, 'error');
                log('💡 前端应显示: "验证失败，请稍后重试，请联系***********************"', 'warning');
            }
        }

        async function testInvalidCode() {
            const email = document.getElementById('email').value.trim();
            
            if (!email) {
                log('❌ 请输入邮箱地址', 'error');
                return;
            }
            
            if (!currentDeviceId) {
                log('❌ 请先请求验证码以生成设备ID', 'error');
                return;
            }
            
            log('🧪 测试错误验证码...', 'warning');
            
            try {
                const requestData = {
                    email: email,
                    code: '000000', // 故意使用错误验证码
                    device_id: currentDeviceId
                };
                
                const response = await fetch('http://localhost:5173/api/auth/verify-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                let responseData;
                try {
                    const responseText = await response.text();
                    responseData = responseText ? JSON.parse(responseText) : {};
                } catch (parseError) {
                    responseData = { error: '响应解析失败' };
                }
                
                showRequestDetails('错误验证码测试', requestData, responseData);
                
                if (response.ok) {
                    log('⚠️ 意外成功！错误验证码不应该通过验证', 'warning');
                } else {
                    log(`✅ 正确拒绝错误验证码，状态码: ${response.status}`, 'success');
                    log('💡 前端应显示统一错误信息', 'info');
                }
                
            } catch (error) {
                log(`✅ 正确处理错误: ${error.message}`, 'success');
                log('💡 前端应显示: "验证失败，请稍后重试，请联系***********************"', 'info');
            }
        }

        // 页面加载时显示信息
        window.onload = function() {
            log('🔍 验证码登录测试工具已加载', 'info');
            log('📋 测试目标: 验证device_id参数和统一错误处理', 'info');
            log('🎯 请按步骤进行测试', 'info');
        };
    </script>
</body>
</html>

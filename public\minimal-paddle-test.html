<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最简Paddle测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        button { padding: 15px 30px; margin: 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background: #005a87; }
        .log { margin: 20px 0; padding: 15px; background: #f5f5f5; border-radius: 5px; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 最简Paddle测试</h1>
        <p>这个测试使用Paddle官方示例配置，排除所有自定义配置问题</p>
        
        <button onclick="testOfficialExample()">测试官方示例</button>
        <button onclick="testWithOurToken()">测试我们的Token</button>
        <button onclick="clearLog()">清空日志</button>
        
        <div id="log" class="log"></div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div><strong>[${timestamp}]</strong> ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 测试官方示例（使用Paddle官方文档中的示例配置）
        function testOfficialExample() {
            log('🔄 开始测试官方示例配置...');
            
            try {
                // 使用Paddle官方文档示例token（如果有效）
                Paddle.Initialize({
                    token: 'test_8064b08184b64e6031ae8c7dd5a'
                });
                
                log('✅ Paddle初始化成功');
                
                // 使用一个通用的测试价格ID（通常Paddle会提供）
                Paddle.Checkout.open({
                    items: [{
                        priceId: 'pri_01jxa2azwznm06dqbptzagtm0z', // 我们的价格ID
                        quantity: 1
                    }]
                });
                
                log('✅ 支付页面调用成功');
                
            } catch (error) {
                log(`❌ 官方示例测试失败: ${error.message}`);
            }
        }

        // 测试我们的配置
        function testWithOurToken() {
            log('🔄 开始测试我们的Token配置...');
            
            try {
                Paddle.Initialize({
                    token: 'test_8064b08184b64e6031ae8c7dd5a'
                });
                
                log('✅ 使用我们的Token初始化成功');
                
                // 尝试最简单的配置
                Paddle.Checkout.open({
                    items: [{
                        priceId: 'pri_01jxa2azwznm06dqbptzagtm0z',
                        quantity: 1
                    }],
                    customer: {
                        email: '<EMAIL>'
                    }
                });
                
                log('✅ 使用我们的配置调用成功');
                
            } catch (error) {
                log(`❌ 我们的配置测试失败: ${error.message}`);
            }
        }

        // 监听Paddle事件
        window.addEventListener('paddle-loaded', function() {
            log('🎯 Paddle SDK已加载');
        });

        window.addEventListener('paddle-checkout-error', function(event) {
            log(`🚨 Paddle结账错误: ${JSON.stringify(event.detail)}`);
        });

        // 页面加载完成
        window.onload = function() {
            log('📋 最简Paddle测试页面已加载');
            log('💡 请点击按钮进行测试');
            
            if (window.Paddle) {
                log('✅ Paddle SDK已可用');
            } else {
                log('❌ Paddle SDK未加载');
            }
        };
    </script>
</body>
</html>

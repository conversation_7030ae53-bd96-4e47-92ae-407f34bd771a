# 浏览器扩展集成指南

本文档介绍了如何将网站与浏览器扩展集成，实现登录状态共享。

## 概述

通过使用 chrome.storage API，我们可以在网站和浏览器扩展之间共享登录状态，使用户只需在一处登录，就能在两处都获得已登录的体验。

## 技术原理

1. **统一存储适配器**：网站使用 `storageAdapter.ts` 作为统一的存储接口，同时支持 localStorage 和 chrome.storage API。
2. **双向同步**：
   - 网站登录后，数据同时保存到 localStorage 和 chrome.storage（如果可用）
   - 浏览器扩展通过内容脚本监听网站的 localStorage 变化，并同步到扩展的 chrome.storage
   - 扩展的变化也会同步回网站的 localStorage

## 网站端集成

### 1. 添加存储适配器

创建 `src/services/storageAdapter.ts` 文件，提供统一的存储接口。该适配器会检测环境中是否存在 chrome.storage API，并相应地使用不同的存储方式。

### 2. 修改认证服务

将 `authService.ts` 中直接使用 localStorage 的代码替换为使用存储适配器的方法。

### 3. 修改用户上下文

更新 `AuthContext.tsx` 和 `UserContext.tsx`，使用存储适配器的方法和事件监听器。

## 浏览器扩展实现

### 1. 扩展结构

```
extension/
├── manifest.json        # 扩展清单
├── background.js        # 后台脚本
├── content-script.js    # 内容脚本
├── popup.html           # 弹出窗口
└── popup.js             # 弹出窗口脚本
```

### 2. 清单文件

在 `manifest.json` 中声明必要的权限：

```json
{
  "permissions": [
    "storage",
    "activeTab"
  ],
  "host_permissions": [
    "https://*.utilityapp.site/*"
  ]
}
```

### 3. 内容脚本

内容脚本负责监听网页中的 localStorage 变化，并将其同步到扩展的 chrome.storage。

### 4. 后台脚本

后台脚本负责监听 chrome.storage 变化，并更新扩展状态。

### 5. 弹出窗口

弹出窗口显示当前登录状态，并提供同步和登出功能。

## 用户体验

1. **无缝登录**：用户在网站登录后，浏览器扩展自动获取登录状态
2. **状态同步**：任何一方的登录状态变化都会同步到另一方
3. **降级处理**：在不支持 chrome.storage 的环境中，网站仍然可以正常使用 localStorage

## 安全考虑

1. **敏感数据处理**：确保敏感数据在传输和存储过程中得到适当保护
2. **权限最小化**：扩展只请求必要的权限
3. **域名限制**：限制内容脚本只在特定域名下运行

## 调试与测试

1. **检查存储状态**：
   - 网站：在开发者工具的 Application > Storage > Local Storage 中查看
   - 扩展：在扩展管理页面的检查视图中查看 chrome.storage

2. **测试场景**：
   - 网站登录 → 扩展应自动获取登录状态
   - 扩展登出 → 网站应自动登出
   - 网站登出 → 扩展应自动更新状态

## 常见问题

1. **Q: 为什么需要同时使用 localStorage 和 chrome.storage?**  
   A: 为了兼容性。在没有安装扩展的环境中，网站仍然可以使用 localStorage 正常工作。

2. **Q: 如何处理多个标签页之间的同步?**  
   A: localStorage 的 'storage' 事件只在不同标签页之间触发，而不在同一个标签页内触发。我们使用自定义事件 'storage-changed' 来解决这个问题。

3. **Q: 如何确保安全性?**  
   A: 敏感数据应使用 HTTPS 传输，并且扩展应该限制在特定域名下运行。 
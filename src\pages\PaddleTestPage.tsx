import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { log } from '../utils/logger';

const PaddleTestPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [logs, setLogs] = useState<string[]>([]);
  const [paddleLoaded, setPaddleLoaded] = useState(false);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    log.debug(message);
  };

  useEffect(() => {
    addLog('开始Paddle测试...');
    
    // 检查环境变量
    addLog(`环境: ${import.meta.env.VITE_PADDLE_ENVIRONMENT || 'undefined'}`);
    addLog(`Token: ${import.meta.env.VITE_PADDLE_CLIENT_TOKEN?.substring(0, 10) || 'undefined'}...`);
    addLog(`产品ID: ${import.meta.env.VITE_PADDLE_PRODUCT_YEARLY || 'undefined'}`);

    // 加载Paddle脚本
    const loadPaddle = async () => {
      try {
        addLog('加载Paddle脚本...');
        
        // 检查是否已经加载
        if (document.querySelector('script[src*="paddle.js"]')) {
          addLog('Paddle脚本已存在');
          checkPaddleObject();
          return;
        }

        const script = document.createElement('script');
        script.src = 'https://cdn.paddle.com/paddle/v2/paddle.js';
        script.onload = () => {
          addLog('✅ Paddle脚本加载成功');
          checkPaddleObject();
        };
        script.onerror = (error) => {
          addLog('❌ Paddle脚本加载失败: ' + error);
        };
        document.head.appendChild(script);
      } catch (error) {
        addLog('❌ 加载Paddle脚本时出错: ' + error);
      }
    };

    const checkPaddleObject = () => {
      let attempts = 0;
      const maxAttempts = 50;

      const check = () => {
        if ((window as any).Paddle) {
          addLog('✅ Paddle对象已可用');
          setPaddleLoaded(true);
          initializePaddle();
        } else if (attempts >= maxAttempts) {
          addLog('❌ Paddle对象加载超时');
        } else {
          attempts++;
          setTimeout(check, 100);
        }
      };

      check();
    };

    const initializePaddle = () => {
      try {
        const token = import.meta.env.VITE_PADDLE_CLIENT_TOKEN || 'test_default_token';
        const environment = import.meta.env.VITE_PADDLE_ENVIRONMENT || 'sandbox';
        
        addLog(`初始化Paddle - 环境: ${environment}, Token: ${token.substring(0, 10)}...`);

        // 设置环境
        if ((window as any).Paddle?.Environment?.set) {
          (window as any).Paddle.Environment.set(environment);
          addLog(`✅ 环境设置为: ${environment}`);
        }

        // 初始化Paddle
        (window as any).Paddle?.Initialize({
          token: token,
          checkout: {
            settings: {
              displayMode: "overlay",
              theme: "light",
              locale: "en"
            }
          },
          eventCallback: (data: any) => {
            addLog(`Paddle事件: ${JSON.stringify(data)}`);
          }
        });

        addLog('✅ Paddle初始化完成');
      } catch (error) {
        addLog('❌ Paddle初始化失败: ' + error);
      }
    };

    loadPaddle();
  }, []);

  const testTokenValidity = async () => {
    try {
      addLog('测试Token有效性...');
      const token = import.meta.env.VITE_PADDLE_CLIENT_TOKEN;

      if (!token) {
        addLog('❌ Token未设置');
        return;
      }

      // 测试API调用
      const response = await fetch('https://api.paddle.com/products', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      addLog(`Token测试响应状态: ${response.status}`);

      if (response.status === 401) {
        addLog('❌ Token无效或已过期');
      } else if (response.status === 403) {
        addLog('❌ Token权限不足');
      } else if (response.ok) {
        addLog('✅ Token有效');
      } else {
        addLog(`⚠️ 未知状态: ${response.status}`);
      }
    } catch (error) {
      addLog('❌ Token测试失败: ' + error);
    }
  };

  const testCheckout = () => {
    try {
      addLog('测试结账流程...');

      if (!(window as any).Paddle?.Checkout?.open) {
        addLog('❌ Paddle Checkout不可用');
        return;
      }

      const productId = import.meta.env.VITE_PADDLE_PRODUCT_YEARLY || 'pri_test_product';
      addLog(`使用产品ID: ${productId}`);

      (window as any).Paddle.Checkout.open({
        items: [{
          priceId: productId,
          quantity: 1
        }],
        customer: {
          email: currentUser?.email || '<EMAIL>'
        },
        customData: {
          userId: currentUser?.id || 'test_user',
          userEmail: currentUser?.email || '<EMAIL>',
          plan: 'yearly',
          timestamp: Date.now()
        },
        settings: {
          displayMode: "overlay",
          theme: "light",
          locale: "en"
        }
      });

      addLog('✅ 结账窗口已打开');
    } catch (error) {
      addLog('❌ 测试结账失败: ' + error);
    }
  };

  const testSimpleProduct = () => {
    try {
      addLog('测试简单产品结账...');

      if (!(window as any).Paddle?.Checkout?.open) {
        addLog('❌ Paddle Checkout不可用');
        return;
      }

      // 使用一个通用的测试产品ID
      const testProductId = 'pri_01gsz8x8sawmvhz1pv30nge1ke'; // Paddle官方测试产品ID
      addLog(`使用测试产品ID: ${testProductId}`);

      (window as any).Paddle.Checkout.open({
        items: [{
          priceId: testProductId,
          quantity: 1
        }],
        customer: {
          email: currentUser?.email || '<EMAIL>'
        },
        customData: {
          userId: currentUser?.id || 'test_user',
          userEmail: currentUser?.email || '<EMAIL>',
          plan: 'yearly',
          timestamp: Date.now()
        },
        settings: {
          displayMode: "overlay",
          theme: "light",
          locale: "en"
        }
      });

      addLog('✅ 测试结账窗口已打开');
    } catch (error) {
      addLog('❌ 测试简单产品失败: ' + error);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Paddle 支付测试</h1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 状态面板 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h2 className="text-lg font-semibold mb-4">状态</h2>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Paddle脚本:</span>
                  <span className={paddleLoaded ? 'text-green-600' : 'text-red-600'}>
                    {paddleLoaded ? '✅ 已加载' : '❌ 未加载'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>环境:</span>
                  <span>{import.meta.env.VITE_PADDLE_ENVIRONMENT || '未设置'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Token:</span>
                  <span>{import.meta.env.VITE_PADDLE_CLIENT_TOKEN ? '已设置' : '未设置'}</span>
                </div>
                <div className="flex justify-between">
                  <span>产品ID:</span>
                  <span>{import.meta.env.VITE_PADDLE_PRODUCT_YEARLY || '未设置'}</span>
                </div>
              </div>
            </div>

            {/* 操作面板 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h2 className="text-lg font-semibold mb-4">操作</h2>
              <div className="space-y-3">
                <button
                  type="button"
                  onClick={testTokenValidity}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700"
                >
                  测试Token有效性
                </button>
                <button
                  type="button"
                  onClick={testSimpleProduct}
                  disabled={!paddleLoaded}
                  className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  测试通用产品
                </button>
                <button
                  type="button"
                  onClick={testCheckout}
                  disabled={!paddleLoaded}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  测试您的产品
                </button>
                <button
                  type="button"
                  onClick={clearLogs}
                  className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700"
                >
                  清除日志
                </button>
              </div>
            </div>
          </div>

          {/* 日志面板 */}
          <div className="mt-6">
            <h2 className="text-lg font-semibold mb-4">实时日志</h2>
            <div className="bg-black text-green-400 rounded-lg p-4 h-64 overflow-y-auto font-mono text-sm">
              {logs.length === 0 ? (
                <div className="text-gray-500">等待日志...</div>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 环境变量显示 */}
          <div className="mt-6">
            <h2 className="text-lg font-semibold mb-4">环境变量</h2>
            <div className="bg-gray-100 rounded-lg p-4">
              <pre className="text-sm">
{`VITE_PADDLE_ENVIRONMENT: ${import.meta.env.VITE_PADDLE_ENVIRONMENT || 'undefined'}
VITE_PADDLE_CLIENT_TOKEN: ${import.meta.env.VITE_PADDLE_CLIENT_TOKEN ? import.meta.env.VITE_PADDLE_CLIENT_TOKEN.substring(0, 10) + '...' : 'undefined'}
VITE_PADDLE_PRODUCT_YEARLY: ${import.meta.env.VITE_PADDLE_PRODUCT_YEARLY || 'undefined'}
VITE_PADDLE_VENDOR_ID: ${import.meta.env.VITE_PADDLE_VENDOR_ID || 'undefined'}`}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaddleTestPage;

# Cloudflare Pages Functions 中间层

本项目使用 Cloudflare Pages Functions 作为中间层，安全地代理 API 请求，避免在前端暴露 API 密钥。

## 目录结构

```
/
├── functions/                     # Cloudflare Pages Functions 目录
│   ├── api/                       # API 代理函数
│   │   ├── auth.js                # 认证 API 代理
│   │   ├── logout.js              # 登出 API 代理
│   │   ├── refresh-token.js       # 刷新令牌 API 代理
│   │   └── user.js                # 用户 API 代理
│   └── _middleware.js             # 全局中间件（处理 CORS 等）
├── src/                           # 前端源代码
│   └── config/
│       └── api.ts                 # API 配置（使用中间层）
└── wrangler.toml                  # Wrangler 配置文件
```

## 工作原理

1. **API 密钥保护**：API 密钥存储在 Cloudflare 环境变量中，而不是前端代码
2. **请求转发**：中间层函数接收前端请求，添加 API 密钥，然后转发到实际 API
3. **CORS 处理**：统一处理 CORS 头，避免跨域问题
4. **错误处理**：捕获并处理请求错误，提供一致的错误响应

## 请求流程

```
前端 -> Cloudflare Pages Functions -> 实际 API (https://autoapi.utilityapp.site)
```

## 本地开发

安装依赖：

```bash
npm install
```

启动本地开发服务器（包含 Functions）：

```bash
npm run dev:functions
```

这将启动 Wrangler 开发服务器和 Vite 开发服务器，允许您在本地测试 Cloudflare Pages Functions。

## 生产部署

当您推送代码到 GitHub 仓库时，Cloudflare Pages 将自动构建和部署您的应用，包括 Functions。

确保在 Cloudflare Pages 控制台中设置以下环境变量：

- `API_KEY`：您的 API 密钥
- `API_BASE_URL`：API 基础 URL，设置为 `https://autoapi.utilityapp.site`

## 注意事项

- 请勿在前端代码中硬编码 API 密钥
- 使用相对路径 `/api/...` 进行 API 请求
- 所有 API 请求通过统一的基础 URL `https://autoapi.utilityapp.site` 处理

## 故障排除

如果遇到 API 请求问题，请检查：

1. Cloudflare Pages Functions 日志中的错误信息
2. 确认环境变量是否正确设置
3. 网络请求中的 CORS 头是否正确
4. 前端 API 配置是否正确使用中间层地址 
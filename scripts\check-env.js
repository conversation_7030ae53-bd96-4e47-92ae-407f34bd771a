#!/usr/bin/env node

/**
 * 环境变量检查脚本
 * 用于诊断正式环境配置问题
 */

console.log('🔍 环境变量检查工具');
console.log('====================');

// 检查关键环境变量
const requiredVars = [
  'VITE_PADDLE_ENVIRONMENT',
  'VITE_PADDLE_CLIENT_TOKEN',
  'VITE_PADDLE_PRODUCT_YEARLY',
  'VITE_API_URL',
  'VITE_API_KEY'
];

console.log('\n📋 环境变量状态:');
requiredVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    // 隐藏敏感信息
    const displayValue = varName.includes('TOKEN') || varName.includes('KEY') 
      ? `${value.substring(0, 8)}...` 
      : value;
    console.log(`✅ ${varName}: ${displayValue}`);
  } else {
    console.log(`❌ ${varName}: 未设置`);
  }
});

// 检查 Paddle 环境
const paddleEnv = process.env.VITE_PADDLE_ENVIRONMENT;
if (paddleEnv === 'sandbox') {
  console.log('\n⚠️  警告: 当前使用 sandbox 环境，正式环境应使用 production');
} else if (paddleEnv === 'production') {
  console.log('\n✅ Paddle 环境配置正确: production');
} else {
  console.log('\n❌ Paddle 环境配置错误:', paddleEnv);
}

// 检查域名
const currentUrl = process.env.VITE_API_URL || '';
if (currentUrl.includes('localhost') || currentUrl.includes('127.0.0.1')) {
  console.log('\n⚠️  警告: API URL 指向本地环境，正式环境应使用生产域名');
} else {
  console.log('\n✅ API URL 配置正确');
}

console.log('\n🔧 建议操作:');
console.log('1. 确保正式环境使用 VITE_PADDLE_ENVIRONMENT=production');
console.log('2. 检查 Paddle 控制台中的域名白名单设置');
console.log('3. 验证 API 密钥和 Token 是否正确');
console.log('4. 确保所有必需的环境变量都已设置'); 
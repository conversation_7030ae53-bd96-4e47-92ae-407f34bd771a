# 基于插件ID的访问控制实现指南

## 概述

本文档介绍如何配置和使用基于浏览器插件ID的访问控制系统。该系统允许您通过验证特定的浏览器插件ID来控制网站功能的访问权限。

## 功能特性

- ✅ 基于环境变量配置允许的插件ID列表
- ✅ 自动检测浏览器插件并验证ID
- ✅ 灵活的访问控制策略（阻止/警告/允许）
- ✅ 功能级别的访问控制
- ✅ React Hook和组件支持
- ✅ 开发环境调试工具

## 环境变量配置

### 1. 环境文件说明

项目使用不同的环境文件来管理配置：

- `.env.example` - 配置模板文件（不包含敏感信息）
- `.env.local` - 测试/开发环境配置
- `.env.production` - 生产环境配置

### 2. 测试环境配置 (.env.local)

测试环境建议使用较宽松的配置便于调试：

```bash
# 浏览器插件访问控制配置（测试环境）
VITE_ALLOWED_EXTENSION_IDS=your-test-extension-id-here
VITE_REQUIRE_EXTENSION=false
VITE_ALLOWED_FEATURES=profile_access,pricing_access,subscription_management
VITE_RESTRICTED_FEATURES=advanced_features,admin_panel,data_export,api_access
VITE_ACCESS_FALLBACK_MODE=warn
VITE_DEV_EXTENSION_ID=dev-test-extension-id
VITE_DEBUG=true
```

### 3. 生产环境配置 (.env.production)

生产环境建议使用严格的安全配置：

```bash
# 浏览器插件访问控制配置（生产环境）
VITE_ALLOWED_EXTENSION_IDS=your-production-extension-id-here
VITE_REQUIRE_EXTENSION=true
VITE_ALLOWED_FEATURES=profile_access,pricing_access,subscription_management
VITE_RESTRICTED_FEATURES=advanced_features,admin_panel,data_export,api_access
VITE_ACCESS_FALLBACK_MODE=block
```

### 4. 配置参数说明

| 参数 | 说明 | 测试环境建议 | 生产环境建议 |
|------|------|-------------|-------------|
| `VITE_ALLOWED_EXTENSION_IDS` | 允许的插件ID列表 | 测试插件ID | 正式插件ID |
| `VITE_REQUIRE_EXTENSION` | 是否要求插件验证 | `false` | `true` |
| `VITE_ACCESS_FALLBACK_MODE` | 无插件时的回退模式 | `warn` | `block` |
| `VITE_DEBUG` | 调试模式 | `true` | `false` |

## 获取插件ID

### 方法1：通过Chrome开发者工具

1. 安装并启用您的浏览器插件
2. 打开Chrome，进入 `chrome://extensions/`
3. 开启"开发者模式"
4. 找到您的插件，复制"ID"字段的值

### 方法2：通过插件代码

插件会自动将ID暴露给网页：

```javascript
// 在网页控制台中执行
console.log(window.extensionId);
```

### 方法3：通过测试页面

在开发环境中访问 `/extension-test` 页面查看插件信息。

## 使用方法

### 1. 在组件中使用访问控制Hook

```tsx
import { useAccessControl, FEATURES } from '../hooks/useAccessControl';

function MyComponent() {
  const { hasValidExtension, canAccess, checkAccess } = useAccessControl();
  
  // 检查是否有有效插件
  if (!hasValidExtension) {
    return <div>需要安装浏览器插件</div>;
  }
  
  // 检查特定功能访问权限
  if (!canAccess(FEATURES.PROFILE_ACCESS)) {
    return <div>无权访问此功能</div>;
  }
  
  return <div>功能内容</div>;
}
```

### 2. 使用访问守卫组件

```tsx
import AccessGuard from '../components/AccessControl/AccessGuard';
import { FEATURES } from '../services/AccessControl';

function ProtectedContent() {
  return (
    <AccessGuard 
      feature={FEATURES.PROFILE_ACCESS}
      fallback={<div>访问被拒绝</div>}
      showReason={true}
    >
      <div>受保护的内容</div>
    </AccessGuard>
  );
}
```

### 3. 显示插件状态

```tsx
import ExtensionStatus from '../components/AccessControl/ExtensionStatus';

function StatusDisplay() {
  return (
    <ExtensionStatus 
      showDetails={true}
      className="my-custom-class"
    />
  );
}
```

## 功能常量

系统预定义了以下功能常量：

```typescript
export const FEATURES = {
  PROFILE_ACCESS: 'profile_access',
  PRICING_ACCESS: 'pricing_access',
  SUBSCRIPTION_MANAGEMENT: 'subscription_management',
  ADVANCED_FEATURES: 'advanced_features',
  ADMIN_PANEL: 'admin_panel',
  DATA_EXPORT: 'data_export',
  API_ACCESS: 'api_access'
} as const;
```

## 访问控制模式

### 1. 阻止模式 (block)
- 没有有效插件时完全阻止访问
- 适用于高安全性要求的功能

### 2. 警告模式 (warn)
- 允许访问但显示警告信息
- 适用于推荐使用插件的功能

### 3. 允许模式 (allow)
- 不检查插件，始终允许访问
- 适用于公开功能

## 开发和调试

### 1. 开发环境测试页面

访问 `http://localhost:5173/extension-test` 查看：
- 当前插件状态
- 功能访问权限测试
- 手动设置插件ID
- 访问控制配置信息

### 2. 控制台调试

```javascript
// 获取插件验证器实例
import { extensionValidator } from './src/services/ExtensionValidator';

// 获取访问控制实例
import { accessControl } from './src/services/AccessControl';

// 查看状态
console.log(extensionValidator.getValidationSummary());
console.log(accessControl.getStatusSummary());
```

### 3. 手动设置插件ID（测试用）

```javascript
// 在控制台中设置测试插件ID
extensionValidator.setExtensionId('your-test-extension-id');
```

## 常见问题

### Q: 插件ID检测不到怎么办？
A: 
1. 确保插件已正确安装并启用
2. 检查插件的manifest.json中的host_permissions
3. 确保网站域名在插件的允许列表中
4. 查看浏览器控制台是否有错误信息

### Q: 如何添加新的功能控制？
A:
1. 在FEATURES常量中添加新功能名称
2. 在环境变量中配置功能权限
3. 在组件中使用checkAccess检查权限

### Q: 如何在生产环境中隐藏调试信息？
A: 调试组件和测试页面只在开发环境中显示，生产环境会自动隐藏。

## 安全注意事项

1. **插件ID验证**：插件ID可以被伪造，不应作为唯一的安全措施
2. **环境变量保护**：确保敏感的插件ID不会暴露在客户端代码中
3. **服务端验证**：重要功能应在服务端进行额外验证
4. **定期更新**：定期更新允许的插件ID列表

## 部署清单

- [ ] 配置生产环境的插件ID列表
- [ ] 设置正确的访问控制模式
- [ ] 测试所有受保护的功能
- [ ] 验证插件在目标浏览器中正常工作
- [ ] 确保错误处理和用户提示正确显示

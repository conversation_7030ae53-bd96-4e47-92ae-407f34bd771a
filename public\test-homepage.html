<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主页测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #efe; border-color: #cfc; }
        .error { background-color: #fee; border-color: #fcc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 主页问题诊断</h1>
        
        <div class="section info">
            <h2>📋 检查项目</h2>
            <ul id="check-list">
                <li>检查React应用是否正常加载...</li>
                <li>检查i18n翻译是否正常...</li>
                <li>检查Tailwind CSS是否正常...</li>
                <li>检查组件渲染是否正常...</li>
            </ul>
        </div>

        <div class="section" id="results">
            <h2>🧪 检查结果</h2>
            <div id="results-content">正在检查...</div>
        </div>

        <div class="section warning">
            <h2>🔧 可能的问题</h2>
            <ul>
                <li><strong>翻译文件问题：</strong>i18n配置或翻译文件缺失</li>
                <li><strong>CSS问题：</strong>Tailwind CSS未正确加载</li>
                <li><strong>组件错误：</strong>React组件渲染异常</li>
                <li><strong>路由问题：</strong>React Router配置问题</li>
                <li><strong>依赖问题：</strong>npm包缺失或版本冲突</li>
            </ul>
        </div>

        <div class="section">
            <h2>🚀 快速修复</h2>
            <button onclick="checkMainApp()">检查主应用</button>
            <button onclick="checkConsole()">检查控制台错误</button>
            <button onclick="checkNetwork()">检查网络请求</button>
        </div>

        <div class="section">
            <h2>📝 诊断日志</h2>
            <div id="log" style="background: #f9f9f9; padding: 10px; border: 1px solid #ddd; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '2px';
            
            if (type === 'success') {
                logEntry.style.color = '#28a745';
            } else if (type === 'error') {
                logEntry.style.color = '#dc3545';
            } else if (type === 'warning') {
                logEntry.style.color = '#ffc107';
            } else {
                logEntry.style.color = '#17a2b8';
            }
            
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateResults(content) {
            document.getElementById('results-content').innerHTML = content;
        }

        function checkMainApp() {
            log('开始检查主应用...', 'info');
            
            // 检查主应用是否在运行
            fetch('http://localhost:5173/')
                .then(response => {
                    if (response.ok) {
                        log('✅ 主应用服务器正常运行', 'success');
                        return response.text();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(html => {
                    if (html.includes('<div id="root">')) {
                        log('✅ HTML结构正常', 'success');
                    } else {
                        log('❌ HTML结构异常', 'error');
                    }
                    
                    if (html.includes('React')) {
                        log('✅ React相关资源已加载', 'success');
                    } else {
                        log('⚠️ 未检测到React相关资源', 'warning');
                    }
                })
                .catch(error => {
                    log(`❌ 主应用访问失败: ${error.message}`, 'error');
                    log('请确认开发服务器是否在 http://localhost:5173 运行', 'warning');
                });
        }

        function checkConsole() {
            log('检查控制台错误...', 'info');
            
            // 检查是否有全局错误
            const errors = [];
            
            // 重写console.error来捕获错误
            const originalError = console.error;
            console.error = function(...args) {
                errors.push(args.join(' '));
                originalError.apply(console, args);
            };
            
            // 检查是否有未捕获的异常
            window.addEventListener('error', function(e) {
                log(`❌ JavaScript错误: ${e.message} (${e.filename}:${e.lineno})`, 'error');
            });
            
            window.addEventListener('unhandledrejection', function(e) {
                log(`❌ Promise拒绝: ${e.reason}`, 'error');
            });
            
            setTimeout(() => {
                if (errors.length === 0) {
                    log('✅ 暂无控制台错误', 'success');
                } else {
                    log(`❌ 发现 ${errors.length} 个控制台错误`, 'error');
                    errors.forEach(error => log(`  - ${error}`, 'error'));
                }
            }, 1000);
        }

        function checkNetwork() {
            log('检查网络请求...', 'info');
            
            // 检查关键资源
            const resources = [
                '/src/main.tsx',
                '/src/App.tsx',
                '/src/pages/HomePage.tsx',
                '/src/i18n/index.ts'
            ];
            
            Promise.all(resources.map(resource => 
                fetch(`http://localhost:5173${resource}`)
                    .then(response => ({
                        resource,
                        status: response.status,
                        ok: response.ok
                    }))
                    .catch(error => ({
                        resource,
                        status: 'ERROR',
                        ok: false,
                        error: error.message
                    }))
            )).then(results => {
                results.forEach(result => {
                    if (result.ok) {
                        log(`✅ ${result.resource} - 状态: ${result.status}`, 'success');
                    } else {
                        log(`❌ ${result.resource} - 状态: ${result.status} ${result.error || ''}`, 'error');
                    }
                });
            });
        }

        // 页面加载时自动检查
        window.onload = function() {
            log('🔍 主页问题诊断工具已加载', 'info');
            log('开始自动诊断...', 'info');
            
            setTimeout(() => {
                checkMainApp();
                checkConsole();
                checkNetwork();
            }, 500);
        };

        // 定期检查主应用状态
        setInterval(() => {
            fetch('http://localhost:5173/')
                .then(response => {
                    if (response.ok) {
                        updateResults(`
                            <div style="color: #28a745;">
                                ✅ 主应用正常运行<br>
                                📍 地址: <a href="http://localhost:5173/" target="_blank">http://localhost:5173/</a><br>
                                🕒 最后检查: ${new Date().toLocaleTimeString()}
                            </div>
                        `);
                    } else {
                        updateResults(`
                            <div style="color: #dc3545;">
                                ❌ 主应用响应异常 (${response.status})<br>
                                🕒 最后检查: ${new Date().toLocaleTimeString()}
                            </div>
                        `);
                    }
                })
                .catch(error => {
                    updateResults(`
                        <div style="color: #dc3545;">
                            ❌ 主应用无法访问<br>
                            📝 错误: ${error.message}<br>
                            🕒 最后检查: ${new Date().toLocaleTimeString()}
                        </div>
                    `);
                });
        }, 5000);
    </script>
</body>
</html>

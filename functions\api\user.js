/**
 * 用户 API 代理函数
 * 处理所有 /api/user/* 路径的请求，并转发到实际的用户 API
 */
export async function onRequest(context) {
  // 从环境变量获取基础 URL 和 API Key
  const apiKey = context.env.API_KEY || "";
  const apiBaseUrl = context.env.API_BASE_URL || "https://autoapi.utilityapp.site";
  
  // 获取请求信息
  const { request } = context;
  const url = new URL(request.url);
  
  // 提取请求路径，移除 /api/user 前缀，添加 /user 前缀
  const path = url.pathname.replace('/api/user', '');
  
  // 构建目标 URL
  const targetUrl = `${apiBaseUrl}/user${path}`;
  console.log(`转发用户请求到: ${targetUrl}`);
  
  try {
    // 克隆原始请求
    const requestInit = {
      method: request.method,
      headers: new Headers(request.headers),
    };
    
    // 记录授权头信息
    const authHeader = request.headers.get("Authorization");
    console.log(`收到的授权头: ${authHeader ? "已设置" : "未设置"}`);
    
    // 如果是 POST/PUT 请求，需要包含请求体
    if (["POST", "PUT", "PATCH"].includes(request.method)) {
      const contentType = request.headers.get("Content-Type");
      if (contentType && contentType.includes("application/json")) {
        // 克隆 JSON 请求体
        const body = await request.json();
        requestInit.body = JSON.stringify(body);
        requestInit.headers.set("Content-Type", "application/json");
      } else {
        // 其他类型请求体直接传递
        requestInit.body = await request.arrayBuffer();
      }
    }
    
    // 移除可能导致问题的头信息
    requestInit.headers.delete("host");
    
    // 确保设置 API Key
    if (apiKey) {
      requestInit.headers.set("x-api-key", apiKey);
      console.log("已设置 API Key 到请求头");
    }
    
    // 记录最终请求头
    console.log("最终请求头:", {
      "Content-Type": requestInit.headers.get("Content-Type"),
      "Authorization": requestInit.headers.get("Authorization") ? "已设置" : "未设置",
      "x-api-key": requestInit.headers.get("x-api-key") ? "已设置" : "未设置"
    });
    
    // 发送请求到实际 API
    const response = await fetch(targetUrl, requestInit);
    
    // 记录响应状态
    console.log(`用户API响应状态: ${response.status}`);
    
    // 构建响应
    const responseInit = {
      status: response.status,
      statusText: response.statusText,
      headers: new Headers(response.headers)
    };
    
    // 获取响应体
    const responseBody = await response.arrayBuffer();
    
    // 尝试解析并记录响应内容（如果是JSON）
    try {
      const responseText = new TextDecoder().decode(responseBody);
      if (response.headers.get("Content-Type")?.includes("application/json")) {
        const responseJson = JSON.parse(responseText);
        console.log("用户API响应内容:", JSON.stringify(responseJson));
      }
    } catch (parseError) {
      console.log("无法解析响应内容为JSON");
    }
    
    // 返回响应
    return new Response(responseBody, responseInit);
  } catch (error) {
    console.error(`处理用户请求出错: ${error.message}`);
    return new Response(JSON.stringify({ 
      error: "中间层处理请求失败", 
      message: error.message 
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json"
      }
    });
  }
} 
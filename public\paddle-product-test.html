<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle产品配置测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .log { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛍️ Paddle产品配置测试</h1>
        
        <div class="section warning">
            <h2>⚠️ 当前问题</h2>
            <p>checkout-service.paddle.com 返回403错误，可能原因：</p>
            <ul>
                <li><strong>产品ID无效或不存在</strong></li>
                <li><strong>价格ID无效或已禁用</strong></li>
                <li><strong>产品状态不是Active</strong></li>
                <li><strong>Token权限不足</strong></li>
                <li><strong>账户配置问题</strong></li>
            </ul>
        </div>

        <div class="section info">
            <h2>📋 当前配置</h2>
            <pre id="config-display"></pre>
        </div>

        <div class="section">
            <h2>🧪 产品验证测试</h2>
            <button onclick="testProductExists()">1. 验证产品是否存在</button>
            <button onclick="testPriceExists()">2. 验证价格是否有效</button>
            <button onclick="testTokenPermissions()">3. 测试Token权限</button>
            <button onclick="testMinimalCheckout()">4. 测试最简支付配置</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section info">
            <h2>💡 解决建议</h2>
            <div id="solutions"></div>
        </div>
    </div>

    <script>
        const config = {
            token: 'test_459728092d35d36bc173c60e52d',
            productId: 'pri_01jxa2azwznm06dqbptzagtm0z',
            vendorId: '32634',
            environment: 'sandbox'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('solutions').innerHTML = '';
        }

        function addSolution(solution) {
            const solutionsDiv = document.getElementById('solutions');
            const solutionDiv = document.createElement('div');
            solutionDiv.className = 'step';
            solutionDiv.style.cssText = 'margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007cba;';
            solutionDiv.innerHTML = solution;
            solutionsDiv.appendChild(solutionDiv);
        }

        function showConfig() {
            document.getElementById('config-display').textContent = JSON.stringify(config, null, 2);
        }

        // 1. 验证产品是否存在
        async function testProductExists() {
            log('🛍️ 测试1: 验证产品是否存在...', 'info');
            
            try {
                const response = await fetch('https://sandbox-api.paddle.com/products', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${config.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log(`产品列表API响应: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`找到 ${data.data?.length || 0} 个产品`, 'success');
                    
                    if (data.data && data.data.length > 0) {
                        log('产品列表:', 'info');
                        data.data.forEach(product => {
                            log(`- ${product.name} (ID: ${product.id})`, 'info');
                        });
                    } else {
                        log('❌ 没有找到任何产品', 'error');
                        addSolution('🔧 <strong>解决方案1:</strong> 请在Paddle Dashboard中创建产品');
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ 产品API错误: ${errorText}`, 'error');
                    
                    if (response.status === 401) {
                        addSolution('🔧 <strong>解决方案2:</strong> Token无效，请检查token配置');
                    } else if (response.status === 403) {
                        addSolution('🔧 <strong>解决方案3:</strong> Token权限不足，请检查token权限设置');
                    }
                }
            } catch (error) {
                log(`❌ 产品API请求失败: ${error.message}`, 'error');
            }
        }

        // 2. 验证价格是否有效
        async function testPriceExists() {
            log('💰 测试2: 验证价格是否有效...', 'info');
            
            try {
                const response = await fetch(`https://sandbox-api.paddle.com/prices/${config.productId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${config.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log(`价格API响应: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ 价格配置有效', 'success');
                    log(`价格信息: ${JSON.stringify(data, null, 2)}`, 'info');
                    
                    if (data.data?.status !== 'active') {
                        log('⚠️ 价格状态不是active', 'warning');
                        addSolution('🔧 <strong>解决方案4:</strong> 请在Paddle Dashboard中激活价格');
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ 价格API错误: ${errorText}`, 'error');
                    
                    if (response.status === 404) {
                        log('❌ 价格ID不存在', 'error');
                        addSolution('🔧 <strong>解决方案5:</strong> 价格ID无效，请检查配置中的产品ID');
                    }
                }
            } catch (error) {
                log(`❌ 价格API请求失败: ${error.message}`, 'error');
            }
        }

        // 3. 测试Token权限
        async function testTokenPermissions() {
            log('🔑 测试3: 测试Token权限...', 'info');
            
            try {
                // 测试不同的API端点来验证权限
                const endpoints = [
                    { name: '产品列表', url: 'https://sandbox-api.paddle.com/products' },
                    { name: '价格列表', url: 'https://sandbox-api.paddle.com/prices' },
                    { name: '客户列表', url: 'https://sandbox-api.paddle.com/customers' }
                ];

                for (const endpoint of endpoints) {
                    try {
                        const response = await fetch(endpoint.url, {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${config.token}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        log(`${endpoint.name}: ${response.status}`, response.ok ? 'success' : 'warning');
                    } catch (error) {
                        log(`${endpoint.name}: 请求失败`, 'error');
                    }
                }
            } catch (error) {
                log(`❌ 权限测试失败: ${error.message}`, 'error');
            }
        }

        // 4. 测试最简支付配置
        async function testMinimalCheckout() {
            log('💳 测试4: 测试最简支付配置...', 'info');
            
            // 首先加载Paddle SDK
            if (!window.Paddle) {
                log('📦 正在加载Paddle SDK...', 'info');
                const script = document.createElement('script');
                script.src = 'https://cdn.paddle.com/paddle/v2/paddle.js';
                script.onload = () => {
                    log('✅ Paddle SDK加载成功', 'success');
                    setTimeout(performCheckoutTest, 1000);
                };
                script.onerror = () => {
                    log('❌ Paddle SDK加载失败', 'error');
                };
                document.head.appendChild(script);
            } else {
                performCheckoutTest();
            }
        }

        function performCheckoutTest() {
            try {
                log('🚀 初始化Paddle...', 'info');
                
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.event === 'checkout.error') {
                            log(`❌ Checkout错误: ${JSON.stringify(data.data)}`, 'error');
                            
                            if (data.data?.error?.code === 'forbidden') {
                                addSolution('🔧 <strong>解决方案6:</strong> Checkout服务403错误，可能是产品配置或账户权限问题');
                            }
                        }
                    }
                });

                log('✅ Paddle初始化成功', 'success');
                
                setTimeout(() => {
                    log('💳 尝试打开支付页面...', 'info');
                    
                    window.Paddle.Checkout.open({
                        items: [{
                            priceId: config.productId,
                            quantity: 1
                        }]
                    });
                }, 1000);
                
            } catch (error) {
                log(`❌ Checkout测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示配置
        window.onload = function() {
            showConfig();
            log('🎯 Paddle产品配置测试工具已加载', 'info');
            log('💡 请按顺序点击测试按钮进行诊断', 'info');
        };
    </script>
</body>
</html>

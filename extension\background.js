/**
 * 浏览器扩展的后台脚本
 * 用于监听 chrome.storage 变化，同步登录状态
 * 支持安全存储加密数据
 */

// 导入安全存储工具和订阅限制工具
importScripts('secure-storage.js');
importScripts('subscription-limits.js');

// 监听存储变化
chrome.storage.onChanged.addListener((changes, area) => {
  if (area === 'local') {
    // 检查是否有认证相关的数据变更
    if (changes.access_token || changes.user_id || changes.user_email) {
      console.log('检测到认证数据变化，更新扩展状态');
      updateExtensionState();
    }

    // 检查是否有订阅信息或快速填充数据变更
    if (changes.subscription_info || changes.quickFillData) {
      console.log('检测到订阅或数据变化，刷新右键菜单');
      createContextMenus();
    }
  }
});

// 更新扩展状态
function updateExtensionState() {
  // 使用安全存储适配器获取数据
  SecureStorageAdapter.getItems(['access_token', 'user_id', 'user_email'], (result) => {
    const isLoggedIn = Boolean(result.access_token) && Boolean(result.user_id);

    // 更新扩展图标状态
    updateExtensionIcon(isLoggedIn);

    // 更新扩展的登录状态
    if (isLoggedIn) {
      console.log(`用户已登录: ${result.user_email}`);
      console.log('[SecureStorage] 敏感数据已安全解密');
    } else {
      console.log('用户未登录');
    }
  });
}

// 更新扩展图标
function updateExtensionIcon(isLoggedIn) {
  const iconPath = isLoggedIn 
    ? '/images/icon-logged-in.png' 
    : '/images/icon-logged-out.png';
  
  chrome.action.setIcon({ path: iconPath });
}

// 初始化时检查登录状态和创建右键菜单
updateExtensionState();
createContextMenus();

// 创建右键菜单
function createContextMenus() {
  // 清除现有菜单
  chrome.contextMenus.removeAll(() => {
    checkSubscriptionStatus((isPremium, subscriptionInfo) => {
      getUserDataStats((stats) => {
        // 获取快速填充数据
        chrome.storage.local.get(['quickFillData', 'language'], (result) => {
          const language = result.language || 'zh';
          const messages = getUpgradeMessages(language);

          let quickFillData = null;
          if (result.quickFillData) {
            try {
              quickFillData = typeof result.quickFillData === 'string'
                ? JSON.parse(result.quickFillData)
                : result.quickFillData;
            } catch (error) {
              console.error('解析快速填充数据失败:', error);
            }
          }

          if (!quickFillData || !quickFillData.groups) {
            return;
          }

          // 如果不是付费用户，过滤数据
          const dataToShow = isPremium ? quickFillData : filterQuickFillData(quickFillData);

          // 创建主菜单
          chrome.contextMenus.create({
            id: 'autofill-main',
            title: language === 'zh' ? 'AutoFill 快速填充' : 'AutoFill Quick Fill',
            contexts: ['editable']
          });

          // 为每个分组创建子菜单
          dataToShow.groups.forEach((group, groupIndex) => {
            if (group.presets && group.presets.length > 0) {
              // 创建分组菜单
              const groupId = `group-${groupIndex}`;
              chrome.contextMenus.create({
                id: groupId,
                parentId: 'autofill-main',
                title: group.name || `${language === 'zh' ? '分组' : 'Group'} ${groupIndex + 1}`,
                contexts: ['editable']
              });

              // 为每个预设创建菜单项
              group.presets.forEach((preset, presetIndex) => {
                chrome.contextMenus.create({
                  id: `preset-${groupIndex}-${presetIndex}`,
                  parentId: groupId,
                  title: preset.name || preset.content?.substring(0, 30) || `${language === 'zh' ? '预设' : 'Preset'} ${presetIndex + 1}`,
                  contexts: ['editable']
                });
              });
            }
          });

          // 如果有被限制的内容，添加升级提示
          if (!isPremium && (stats.groupCount > FREE_LIMITS.maxGroups ||
              stats.totalPresets > FREE_LIMITS.maxGroups * FREE_LIMITS.maxPresetsPerGroup)) {
            chrome.contextMenus.create({
              id: 'upgrade-prompt',
              parentId: 'autofill-main',
              title: `🔒 ${messages.upgradePrompt}`,
              contexts: ['editable']
            });
          }
        });
      });
    });
  });
}

// 处理右键菜单点击
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'upgrade-prompt') {
    // 打开升级页面
    chrome.tabs.create({ url: 'https://autofill.utilityapp.site/pricing' });
    return;
  }

  if (info.menuItemId.startsWith('preset-')) {
    // 解析预设ID
    const [, groupIndex, presetIndex] = info.menuItemId.split('-').map(Number);

    // 获取预设内容并填充
    chrome.storage.local.get(['quickFillData'], (result) => {
      if (result.quickFillData) {
        try {
          const quickFillData = typeof result.quickFillData === 'string'
            ? JSON.parse(result.quickFillData)
            : result.quickFillData;

          checkSubscriptionStatus((isPremium) => {
            const dataToUse = isPremium ? quickFillData : filterQuickFillData(quickFillData);

            if (dataToUse.groups &&
                dataToUse.groups[groupIndex] &&
                dataToUse.groups[groupIndex].presets &&
                dataToUse.groups[groupIndex].presets[presetIndex]) {

              const preset = dataToUse.groups[groupIndex].presets[presetIndex];

              // 向内容脚本发送填充指令
              chrome.tabs.sendMessage(tab.id, {
                action: 'fillContent',
                content: preset.content
              });
            }
          });
        } catch (error) {
          console.error('处理预设填充失败:', error);
        }
      }
    });
  }
});

// 处理来自内容脚本或弹出窗口的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'checkLoginStatus') {
    // 检查登录状态并响应（使用安全存储）
    SecureStorageAdapter.getItems(['access_token', 'user_id'], (result) => {
      const isLoggedIn = Boolean(result.access_token) && Boolean(result.user_id);
      sendResponse({ isLoggedIn });
    });
    return true; // 表示将异步发送响应
  } else if (message.action === 'checkWebsiteParsing') {
    // 检查网站解析权限
    checkSubscriptionStatus((isPremium, subscriptionInfo) => {
      if (isPremium) {
        sendResponse({ allowed: true });
      } else {
        getUserDataStats((stats) => {
          const limits = checkFreeLimits(stats);
          const language = message.language || 'zh';
          const messages = getUpgradeMessages(language);

          sendResponse({
            allowed: false,
            message: messages.websiteParseLimited,
            upgradeUrl: 'https://autofill.utilityapp.site/pricing'
          });
        });
      }
    });
    return true;
  } else if (message.action === 'refreshContextMenus') {
    // 刷新右键菜单
    createContextMenus();
    sendResponse({ success: true });
    return false;
  }
});
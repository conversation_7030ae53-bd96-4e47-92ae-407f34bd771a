<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle参数格式验证</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 11px; }
        .log { max-height: 400px; overflow-y: auto; }
        .param-comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0; }
        .param-box { padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .correct { border-color: #28a745; background-color: #f8fff9; }
        .incorrect { border-color: #dc3545; background-color: #fff8f8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Paddle参数格式验证</h1>
        
        <div class="section info">
            <h2>📋 官方文档要求的参数格式</h2>
            <p>根据Paddle.js官方文档，<code>Paddle.Checkout.open()</code>的正确格式：</p>
            
            <div class="param-comparison">
                <div class="param-box correct">
                    <h3>✅ 正确格式</h3>
                    <pre>{
  items: [{
    priceId: "pri_01gm81eqze2vmmvhpjg13bfeqg",
    quantity: 1
  }],
  settings: {
    displayMode: "overlay",
    theme: "light",
    locale: "en"
  },
  customer: {
    email: "<EMAIL>"
  }
}</pre>
                </div>
                
                <div class="param-box incorrect">
                    <h3>❌ 我们当前使用的格式</h3>
                    <pre>{
  items: [{
    priceId: "pri_01k0xttmf9wekbg957y66xawj",
    quantity: 1
  }],
  customer: {
    email: "<EMAIL>"
  },
  customData: {
    userId: "123",
    plan: "yearly"
  },
  settings: {
    displayMode: "overlay",
    theme: "light",
    locale: "zh"
  }
}</pre>
                </div>
            </div>
        </div>

        <div class="section warning">
            <h2>⚠️ 发现的问题</h2>
            <ol>
                <li><strong>参数结构问题：</strong> 我们把<code>settings</code>放在了错误的位置</li>
                <li><strong>locale值问题：</strong> 使用了"zh"，但文档显示应该用完整的locale代码</li>
                <li><strong>customData位置：</strong> 可能需要调整位置</li>
            </ol>
        </div>

        <div class="section">
            <h2>🧪 参数格式测试</h2>
            <button onclick="testOfficialFormat()">1. 测试官方标准格式</button>
            <button onclick="testMinimalFormat()">2. 测试最简格式</button>
            <button onclick="testCorrectStructure()">3. 测试修正后的结构</button>
            <button onclick="testDifferentLocales()">4. 测试不同locale</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section success" id="success-section" style="display: none;">
            <h2>🎉 找到正确格式！</h2>
            <div id="success-format"></div>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'test_459728092d35d36bc173c60e52d',
            productId: 'pri_01k0xttmf9wekbg957y66xawj'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('success-section').style.display = 'none';
        }

        function showSuccess(format) {
            document.getElementById('success-format').innerHTML = `<pre>${JSON.stringify(format, null, 2)}</pre>`;
            document.getElementById('success-section').style.display = 'block';
        }

        async function initializePaddle() {
            return new Promise((resolve) => {
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            log(`❌ Checkout错误: ${data.detail}`, 'error');
                        } else if (data.type === 'checkout.loaded') {
                            log('✅ Checkout加载成功！', 'success');
                        } else if (data.name === 'checkout.closed' && data.data) {
                            const item = data.data.items?.[0];
                            if (item?.price_id && item?.product?.id && item?.product?.name) {
                                log('🎉 产品数据完整！找到正确格式！', 'success');
                                showSuccess(lastTestedFormat);
                            } else {
                                log('❌ 产品数据仍然为空', 'error');
                                log(`- price_id: ${item?.price_id || 'null'}`, 'error');
                                log(`- product.id: ${item?.product?.id || 'null'}`, 'error');
                                log(`- product.name: "${item?.product?.name || ''}"`, 'error');
                            }
                        }
                    }
                });
                resolve();
            });
        }

        let lastTestedFormat = null;

        // 1. 测试官方标准格式
        async function testOfficialFormat() {
            log('🧪 测试1: 官方标准格式...', 'info');
            
            try {
                await initializePaddle();
                
                // 完全按照官方文档的格式
                const officialFormat = {
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    },
                    customer: {
                        email: "<EMAIL>"
                    }
                };
                
                lastTestedFormat = officialFormat;
                log(`格式: ${JSON.stringify(officialFormat, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(officialFormat);
                log('✅ 官方格式请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 官方格式测试失败: ${error.message}`, 'error');
            }
        }

        // 2. 测试最简格式
        async function testMinimalFormat() {
            log('🧪 测试2: 最简格式（仅必需参数）...', 'info');
            
            try {
                await initializePaddle();
                
                // 只包含绝对必需的参数
                const minimalFormat = {
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }]
                };
                
                lastTestedFormat = minimalFormat;
                log(`格式: ${JSON.stringify(minimalFormat, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(minimalFormat);
                log('✅ 最简格式请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 最简格式测试失败: ${error.message}`, 'error');
            }
        }

        // 3. 测试修正后的结构
        async function testCorrectStructure() {
            log('🧪 测试3: 修正后的结构...', 'info');
            
            try {
                await initializePaddle();
                
                // 修正参数结构
                const correctedFormat = {
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }],
                    customer: {
                        email: "<EMAIL>"
                    },
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en-US"
                    },
                    customData: {
                        userId: "test123",
                        plan: "yearly",
                        source: "website"
                    }
                };
                
                lastTestedFormat = correctedFormat;
                log(`格式: ${JSON.stringify(correctedFormat, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(correctedFormat);
                log('✅ 修正结构请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 修正结构测试失败: ${error.message}`, 'error');
            }
        }

        // 4. 测试不同locale
        async function testDifferentLocales() {
            log('🧪 测试4: 不同locale设置...', 'info');
            
            const locales = ['en', 'en-US', 'zh-CN', 'zh'];
            
            for (let i = 0; i < locales.length; i++) {
                const locale = locales[i];
                
                setTimeout(async () => {
                    try {
                        log(`🌐 测试locale: ${locale}`, 'info');
                        
                        const localeFormat = {
                            items: [{
                                priceId: config.productId,
                                quantity: 1
                            }],
                            settings: {
                                displayMode: "overlay",
                                theme: "light",
                                locale: locale
                            }
                        };
                        
                        lastTestedFormat = localeFormat;
                        log(`格式: ${JSON.stringify(localeFormat, null, 2)}`, 'info');
                        
                        window.Paddle.Checkout.open(localeFormat);
                        log(`✅ Locale ${locale} 请求已发送`, 'success');
                        
                    } catch (error) {
                        log(`❌ Locale ${locale} 测试失败: ${error.message}`, 'error');
                    }
                }, i * 3000);
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            log('🔍 Paddle参数格式验证工具已加载', 'info');
            log('💡 根据官方文档检查我们的参数格式是否正确', 'info');
            log('🎯 目标：找到能返回完整产品数据的正确格式', 'warning');
        };
    </script>
</body>
</html>

/**
 * 浏览器扩展的弹出窗口脚本
 * 支持安全存储加密数据
 */

document.addEventListener('DOMContentLoaded', function() {
  const statusContainer = document.getElementById('status-container');
  const statusMessage = document.getElementById('status-message');
  const userInfoContainer = document.getElementById('user-info');
  const userIdElement = document.getElementById('user-id');
  const userEmailElement = document.getElementById('user-email');
  const syncButton = document.getElementById('sync-button');
  const logoutButton = document.getElementById('logout-button');
  
  // 检查登录状态（使用安全存储）
  function checkLoginStatus() {
    // 使用安全存储适配器获取数据
    if (typeof SecureStorageAdapter !== 'undefined') {
      SecureStorageAdapter.getItems([
        'access_token',
        'user_id',
        'user_email'
      ], function(result) {
        const isLoggedIn = Boolean(result.access_token) && Boolean(result.user_id);

        if (isLoggedIn) {
          // 已登录状态
          statusContainer.className = 'status logged-in';
          statusMessage.textContent = '已登录';

          // 显示用户信息
          userInfoContainer.style.display = 'block';
          userIdElement.textContent = result.user_id || '-';
          userEmailElement.textContent = result.user_email || '-';

          // 启用按钮
          syncButton.disabled = false;
          logoutButton.disabled = false;

          console.log('[SecureStorage] 用户数据已安全解密显示');
        } else {
          // 未登录状态
          statusContainer.className = 'status logged-out';
          statusMessage.textContent = '未登录';

          // 隐藏用户信息
          userInfoContainer.style.display = 'none';

          // 启用同步按钮，禁用登出按钮
          syncButton.disabled = false;
          logoutButton.disabled = true;
        }
      });
    } else {
      // 降级到原始方式
      chrome.storage.local.get([
        'access_token',
        'user_id',
        'user_email'
      ], function(result) {
        const isLoggedIn = Boolean(result.access_token) && Boolean(result.user_id);

        if (isLoggedIn) {
          statusContainer.className = 'status logged-in';
          statusMessage.textContent = '已登录';
          userInfoContainer.style.display = 'block';
          userIdElement.textContent = result.user_id || '-';
          userEmailElement.textContent = result.user_email || '-';
          syncButton.disabled = false;
          logoutButton.disabled = false;
        } else {
          statusContainer.className = 'status logged-out';
          statusMessage.textContent = '未登录';
          userInfoContainer.style.display = 'none';
          syncButton.disabled = false;
          logoutButton.disabled = true;
        }
      });
    }
  }
  
  // 同步登录状态
  syncButton.addEventListener('click', function() {
    syncButton.disabled = true;
    syncButton.textContent = '同步中...';
    
    // 获取当前活动标签页
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs.length === 0) {
        syncButton.textContent = '同步状态';
        syncButton.disabled = false;
        return;
      }
      
      const activeTab = tabs[0];
      
      // 发送消息到内容脚本
      chrome.tabs.sendMessage(activeTab.id, {action: 'syncFromExtension'}, function(response) {
        if (response && response.success) {
          statusMessage.textContent = '同步成功';
          setTimeout(function() {
            checkLoginStatus();
            syncButton.textContent = '同步状态';
            syncButton.disabled = false;
          }, 1000);
        } else {
          statusMessage.textContent = '同步失败，请刷新页面';
          syncButton.textContent = '同步状态';
          syncButton.disabled = false;
        }
      });
    });
  });
  
  // 退出登录
  logoutButton.addEventListener('click', function() {
    logoutButton.disabled = true;
    logoutButton.textContent = '退出中...';
    
    // 清除存储中的认证数据（使用安全存储适配器）
    const keysToRemove = [
      'access_token',
      'refresh_token',
      'token_type',
      'access_token_expires_at',
      'user_id',
      'user_email',
      'subscription_info'
    ];

    if (typeof SecureStorageAdapter !== 'undefined') {
      SecureStorageAdapter.removeItems(keysToRemove, function() {
        console.log('[SecureStorage] 敏感数据已安全清除');
        handleLogoutComplete();
      });
    } else {
      // 降级到原始方式
      chrome.storage.local.remove(keysToRemove, function() {
        handleLogoutComplete();
      });
    }

    function handleLogoutComplete() {
        // 获取当前活动标签页
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
          if (tabs.length > 0) {
            const activeTab = tabs[0];

            // 发送消息到内容脚本，同步登出状态到网页
            chrome.tabs.sendMessage(activeTab.id, {action: 'syncFromExtension'}, function() {
              // 无论成功与否，都更新UI
              logoutButton.textContent = '退出登录';
              checkLoginStatus();
            });
          } else {
            logoutButton.textContent = '退出登录';
            checkLoginStatus();
          }
        });
      }
    }
  });
  
  // 初始化检查登录状态
  checkLoginStatus();
  
  // 监听存储变化
  chrome.storage.onChanged.addListener(function(changes, area) {
    if (area === 'local') {
      if (changes.access_token || changes.user_id || changes.user_email) {
        checkLoginStatus();
      }
    }
  });
}); 
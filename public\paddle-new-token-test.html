<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle新Token验证</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #efe; border-color: #cfc; }
        .error { background-color: #fee; border-color: #fcc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 15px 25px; margin: 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background: #005a87; }
        input { padding: 10px; width: 400px; border: 1px solid #ddd; border-radius: 3px; font-family: monospace; }
        .log { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; }
        .steps { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .token-input { margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Paddle新Token验证</h1>
        
        <div class="section error">
            <h2>❌ 当前问题确认</h2>
            <p><strong>JWT错误持续出现</strong> - 即使使用Dashboard中显示为Active的Token</p>
            <p>这说明Token需要重新生成</p>
        </div>

        <div class="section warning">
            <h2>🔧 重新生成Token步骤</h2>
            <div class="steps">
                <ol>
                    <li><strong>登录Sandbox Dashboard:</strong> <a href="https://sandbox-vendors.paddle.com/" target="_blank">https://sandbox-vendors.paddle.com/</a></li>
                    <li><strong>进入:</strong> Developer tools > Authentication > Client-side tokens</li>
                    <li><strong>删除旧Token:</strong> 删除 "test_27fb172b17e19acd4fbad97de75"</li>
                    <li><strong>创建新Token:</strong> 点击 "+ New Client-side token"</li>
                    <li><strong>复制新Token:</strong> 应该以 "test_" 开头</li>
                    <li><strong>在下方输入新Token进行测试</strong></li>
                </ol>
            </div>
        </div>

        <div class="section info">
            <h2>🧪 新Token测试</h2>
            <div class="token-input">
                <label for="newToken"><strong>输入新生成的Token:</strong></label><br>
                <input type="text" id="newToken" placeholder="test_xxxxxxxxxxxxxxxxxxxxxxxxx" value="">
                <br><small>格式: test_ + 27个字符</small>
            </div>
            <button onclick="testNewToken()">🚀 测试新Token</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section success" id="success-section" style="display: none;">
            <h2>🎉 新Token测试成功！</h2>
            <div id="success-info"></div>
        </div>

        <div class="section info">
            <h2>📋 产品配置信息</h2>
            <p><strong>价格ID:</strong> <code>pri_01k1aenmy30gyzrq9wayftn5z4</code></p>
            <p><strong>产品名称:</strong> niandu</p>
            <p><strong>价格:</strong> $1.50/month (30 day trial)</p>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const productConfig = {
            priceId: 'pri_01k1aenmy30gyzrq9wayftn5z4'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '5px';
            logEntry.style.padding = '5px';
            logEntry.style.borderRadius = '3px';
            
            if (type === 'success') {
                logEntry.style.backgroundColor = '#d4edda';
                logEntry.style.color = '#155724';
            } else if (type === 'error') {
                logEntry.style.backgroundColor = '#f8d7da';
                logEntry.style.color = '#721c24';
            } else {
                logEntry.style.backgroundColor = '#d1ecf1';
                logEntry.style.color = '#0c5460';
            }
            
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('success-section').style.display = 'none';
        }

        function showSuccess(token) {
            const infoDiv = document.getElementById('success-info');
            infoDiv.innerHTML = `
                <h3>✅ 新Token验证成功！</h3>
                <p><strong>有效Token:</strong> <code>${token}</code></p>
                <p><strong>下一步:</strong></p>
                <ol>
                    <li>更新 .env.local 中的 VITE_PADDLE_CLIENT_TOKEN</li>
                    <li>重启开发服务器: npm run dev</li>
                    <li>测试主应用的支付功能</li>
                </ol>
                <p><strong>更新命令:</strong></p>
                <pre>VITE_PADDLE_CLIENT_TOKEN=${token}</pre>
            `;
            document.getElementById('success-section').style.display = 'block';
        }

        function validateToken(token) {
            if (!token) {
                log('❌ 请输入Token', 'error');
                return false;
            }
            
            if (!token.startsWith('test_')) {
                log('❌ Token格式错误：应该以 "test_" 开头', 'error');
                return false;
            }
            
            if (token.length !== 32) {
                log(`❌ Token长度错误：应该是32个字符，当前是${token.length}个字符`, 'error');
                return false;
            }
            
            log('✅ Token格式验证通过', 'success');
            return true;
        }

        async function testNewToken() {
            const newToken = document.getElementById('newToken').value.trim();
            
            log('🔄 开始测试新Token...', 'info');
            log(`输入的Token: ${newToken}`, 'info');
            
            if (!validateToken(newToken)) {
                return;
            }
            
            try {
                log('🔧 初始化Paddle...', 'info');
                
                window.Paddle.Initialize({
                    token: newToken,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            log(`❌ Checkout错误: ${data.detail}`, 'error');
                            
                            if (data.detail && data.detail.includes('JWT')) {
                                log('❌ 仍然是JWT错误 - Token可能仍有问题', 'error');
                            } else if (data.detail && data.detail.includes('authentication')) {
                                log('❌ 认证错误 - Token无效', 'error');
                            }
                        } else if (data.type === 'checkout.loaded') {
                            log('🎉 支付页面加载成功！新Token有效！', 'success');
                        } else if (data.name === 'checkout.closed' && data.data) {
                            const item = data.data.items?.[0];
                            if (item?.price_id && item?.product?.id && item?.product?.name) {
                                log('🎉 产品数据完整！新Token完全有效！', 'success');
                                log(`产品名称: "${item.product.name}"`, 'success');
                                log(`价格ID: ${item.price_id}`, 'success');
                                log(`产品ID: ${item.product.id}`, 'success');
                                
                                showSuccess(newToken);
                            } else {
                                log('⚠️ 支付页面能打开，但产品数据为空', 'warning');
                                log('这可能是产品配置问题，但Token是有效的', 'warning');
                            }
                        }
                    }
                });
                
                log('✅ Paddle初始化成功', 'success');
                
                // 测试支付
                log('💳 打开支付页面...', 'info');
                
                const testConfig = {
                    items: [{
                        priceId: productConfig.priceId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                log(`配置: ${JSON.stringify(testConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(testConfig);
                log('✅ 新Token测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 新Token测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示信息
        window.onload = function() {
            log('🔄 新Token验证工具已加载', 'info');
            log('🎯 请先在Paddle Dashboard中重新生成Token', 'warning');
            log('💡 然后在上方输入新Token进行测试', 'info');
        };
    </script>
</body>
</html>

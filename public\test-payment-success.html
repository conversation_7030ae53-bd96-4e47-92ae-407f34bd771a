<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试支付成功流程 - AutoFill</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>🧪 AutoFill 支付成功流程测试</h1>
    
    <div class="test-section">
        <h2>1. 测试支付成功页面</h2>
        <p>点击下面的按钮测试不同的支付成功场景：</p>
        
        <button class="btn" onclick="testSuccessPageDirect()">
            测试直接访问成功页面
        </button>
        
        <button class="btn" onclick="testSuccessPageInOverlay()">
            测试在Overlay中的成功页面
        </button>
        
        <button class="btn" onclick="testProfilePageWithParams()">
            测试带参数的个人中心页面
        </button>
    </div>
    
    <div class="test-section">
        <h2>2. 消息监听测试</h2>
        <p>测试窗口间消息传递：</p>
        
        <button class="btn btn-secondary" onclick="sendTestMessage()">
            发送测试消息
        </button>
        
        <button class="btn btn-secondary" onclick="clearLog()">
            清空日志
        </button>
        
        <div id="messageStatus" class="status info">
            等待消息...
        </div>
    </div>
    
    <div class="test-section">
        <h2>3. 实时日志</h2>
        <div id="log" class="log">
            [等待测试开始...]
        </div>
    </div>

    <script>
        const log = document.getElementById('log');
        const messageStatus = document.getElementById('messageStatus');
        
        // 日志函数
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            log.textContent += logEntry;
            log.scrollTop = log.scrollHeight;
            console.log(logEntry);
        }
        
        // 清空日志
        function clearLog() {
            log.textContent = '[日志已清空]\n';
        }
        
        // 测试直接访问成功页面
        function testSuccessPageDirect() {
            addLog('🚀 测试直接访问支付成功页面');
            const successUrl = window.location.origin + '/paddle-success.html';
            addLog(`打开URL: ${successUrl}`);
            window.open(successUrl, '_blank', 'width=600,height=500');
        }
        
        // 测试在Overlay中的成功页面
        function testSuccessPageInOverlay() {
            addLog('🎭 测试在Overlay中的支付成功页面');
            
            // 创建一个模拟的overlay iframe
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            
            const iframe = document.createElement('iframe');
            iframe.src = window.location.origin + '/paddle-success.html';
            iframe.style.cssText = `
                width: 500px;
                height: 400px;
                border: none;
                border-radius: 12px;
                background: white;
            `;
            
            overlay.appendChild(iframe);
            document.body.appendChild(overlay);
            
            // 5秒后自动关闭
            setTimeout(() => {
                document.body.removeChild(overlay);
                addLog('🔒 模拟overlay已关闭');
            }, 10000);
            
            addLog('✅ 模拟overlay已创建，将在10秒后自动关闭');
        }
        
        // 测试带参数的个人中心页面
        function testProfilePageWithParams() {
            addLog('📄 测试带参数的个人中心页面');
            const profileUrl = window.location.origin + '/profile?paddle_payment_success=true&test=true';
            addLog(`打开URL: ${profileUrl}`);
            window.open(profileUrl, '_blank');
        }
        
        // 发送测试消息
        function sendTestMessage() {
            const message = {
                type: 'paddle_payment_success',
                source: 'test_page',
                timestamp: Date.now()
            };
            
            addLog('📤 发送测试消息', 'test');
            addLog(JSON.stringify(message, null, 2));
            
            window.postMessage(message, '*');
            
            // 也尝试发送自定义事件
            const customEvent = new CustomEvent('paddle_payment_success', {
                detail: message
            });
            window.dispatchEvent(customEvent);
        }
        
        // 监听消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'paddle_payment_success') {
                const source = event.data.source || 'unknown';
                addLog(`📥 收到支付成功消息 (来源: ${source})`, 'success');
                addLog(JSON.stringify(event.data, null, 2));
                
                messageStatus.className = 'status success';
                messageStatus.textContent = `✅ 收到来自 ${source} 的支付成功消息`;
                
                setTimeout(() => {
                    messageStatus.className = 'status info';
                    messageStatus.textContent = '等待消息...';
                }, 3000);
            } else {
                addLog(`📥 收到其他消息: ${JSON.stringify(event.data)}`, 'info');
            }
        });
        
        // 监听自定义事件
        window.addEventListener('paddle_payment_success', function(event) {
            addLog('🎯 收到自定义事件: paddle_payment_success', 'success');
            addLog(JSON.stringify(event.detail, null, 2));
        });
        
        // 页面加载完成
        addLog('🎉 测试页面加载完成');
        addLog('📋 可以开始测试支付成功流程');
        
        // 检测URL参数
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('paddle_payment_success') === 'true') {
            addLog('🔍 检测到URL参数: paddle_payment_success=true', 'success');
        }
    </script>
</body>
</html>
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

// Placeholder SVG icons - replace with actual icons or images
const PlaceholderIcon = ({ className = "h-6 w-6 text-gray-500" }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M4 6h16M4 12h16M4 18h16" />
  </svg>
);

const StarIcon = ({ className = "h-5 w-5" }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
);

const ShieldIcon = ({ className = "h-6 w-6" }: { className?: string }) => (
    <svg className={className} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path></svg>
);

const LandscapeIcon = ({className = "h-16 w-16 text-gray-400"} : {className?: string}) => (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1">
        <path strokeLinecap="round" strokeLinejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
);

// 创建一个动画效果组件
const AnimatedSubtitle: React.FC<{ text: string }> = ({ text }) => {
  return (
    <div className="relative inline-block whitespace-nowrap">
      <p className="text-lg font-semibold tracking-tight text-transparent bg-clip-text sm:text-xl md:text-2xl opacity-0">
        {text}
      </p>
      <div className="absolute top-0 left-0 right-0 overflow-hidden">
        {text.split('').map((char, index) => {
          // 计算字符在文本中的位置比例（0到1之间）
          const position = text.length > 1 ? index / (text.length - 1) : 0;
          // 为两侧到中间的动画设置延迟
          const delayToMiddle = Math.abs(0.5 - position) * 2; // 0到1之间的值，两侧为1，中间为0
          
          return (
            <span 
              key={index} 
              className="inline-block text-lg font-semibold tracking-tight text-gray-700 dark:text-gray-300 sm:text-xl md:text-2xl animate-transparency-wave" 
              style={{ 
                animationDelay: `${delayToMiddle * 1.5}s`,
                animationDuration: '4s'
              }}
            >
              {char}
            </span>
          );
        })}
      </div>
    </div>
  );
};

const HomePage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [browserMessage, setBrowserMessage] = useState<string | null>(null);
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);
  
  // 检测浏览器类型
  const detectBrowser = (): string => {
    const userAgent = navigator.userAgent;
    
    // 检测主要浏览器
    if (userAgent.indexOf("Chrome") > -1 && userAgent.indexOf("Edg") === -1) {
      return "Chrome";
    } else if (userAgent.indexOf("Firefox") > -1) {
      return "Firefox";
    } else if (userAgent.indexOf("Safari") > -1 && userAgent.indexOf("Chrome") === -1) {
      return "Safari";
    } else if (userAgent.indexOf("Edg") > -1) {
      return "Edge";
    } else if (userAgent.indexOf("OPR") > -1 || userAgent.indexOf("Opera") > -1) {
      return "Opera";
    } else {
      return "Unknown";
    }
  };

  // 获取对应浏览器的插件URL
  const getExtensionUrl = (browser: string): string | null => {
    // 这里替换为实际的插件URL
    const extensionUrls: Record<string, string> = {
      "Chrome": "https://chrome.google.com/webstore/detail/your-extension-id",
      "Firefox": "https://addons.mozilla.org/firefox/addon/your-addon-id",
      "Edge": "https://microsoftedge.microsoft.com/addons/detail/your-addon-id",
      // Safari需要单独处理，因为其扩展通常通过App Store安装
      // "Safari": "https://apps.apple.com/app/your-app-id",
      // "Opera": "https://addons.opera.com/extensions/details/your-extension-id"
    };
    
    return extensionUrls[browser] || null;
  };

  // 处理按钮点击
  const handleGetExtension = () => {
    const browser = detectBrowser();
    const extensionUrl = getExtensionUrl(browser);
    
    if (extensionUrl) {
      // 如果有对应浏览器的URL，跳转到插件商店
      window.open(extensionUrl, '_blank');
    } else {
      // 否则显示提示信息
      const message = i18n.language.startsWith('zh') 
        ? `${browser}浏览器适配中，敬请期待！` 
        : `${browser} browser support coming soon!`;
      
      setBrowserMessage(message);
      
      // 3秒后自动清除消息
      setTimeout(() => {
        setBrowserMessage(null);
      }, 3000);
    }
  };

  // 动画已移至tailwind配置中

  // 切换FAQ展开状态
  const toggleFaq = (index: number) => {
    setExpandedFaq(expandedFaq === index ? null : index);
  };

  return (
    <div className="bg-slate-50 dark:bg-slate-900 py-12 md:py-20">
      <div className="mx-auto max-w-5xl px-4">
        {/* Browser message notification */}
        {browserMessage && (
          <div className="fixed top-4 right-4 z-50 bg-orange-100 border-l-4 border-orange-500 text-orange-700 p-4 rounded shadow-md">
            <p>{browserMessage}</p>
          </div>
        )}
        
        <div className="flex flex-col items-center gap-12 md:flex-row md:gap-16 lg:gap-24">
          <div className="md:w-1/2 text-center md:text-left">
            <div className="flex flex-col space-y-2">
              <div className="px-0 py-1">
                <h1 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-3xl md:text-4xl whitespace-nowrap">
                  {t('home.title')}
                </h1>
              </div>
              <div className="px-0 py-1">
                <AnimatedSubtitle text={t('home.subtitle')} />
              </div>
            </div>
            
            <p className="mt-6 text-lg leading-7 text-gray-600 dark:text-gray-300">
              {t('home.description')}
            </p>
            <div className="mt-8 flex items-center justify-center space-x-3 md:justify-start">
              <PlaceholderIcon className="h-7 w-7 text-blue-500" />
              <PlaceholderIcon className="h-7 w-7 text-purple-500" />
              <PlaceholderIcon className="h-7 w-7 text-pink-500" />
              <PlaceholderIcon className="h-7 w-7 text-green-500" />
              <PlaceholderIcon className="h-7 w-7 text-yellow-500" />
              <PlaceholderIcon className="h-7 w-7 text-red-500" />
            </div>
            <div className="mt-10 flex justify-center md:justify-start">
              <button
                onClick={handleGetExtension}
                className="rounded-md bg-orange-500 px-8 py-3 text-lg font-semibold text-white shadow-md hover:bg-orange-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-orange-500"
              >
                {t('home.getStarted')}
              </button>
            </div>
          </div>

          <div className="md:w-1/2 flex justify-center">
            <div className="relative w-full max-w-sm rounded-xl bg-white dark:bg-gray-800 p-6 shadow-xl">
              <div className="absolute top-6 left-[-20px] z-10 flex h-10 w-20 items-center justify-start rounded-full bg-white p-1 shadow-lg dark:bg-gray-700">
                <span className="block h-8 w-8 rounded-full bg-orange-400"></span>
              </div>

              <div className="absolute top-6 right-6 z-10 flex items-center space-x-1 rounded-full bg-white px-3 py-1 text-sm font-medium text-gray-700 shadow-md dark:bg-gray-700 dark:text-gray-200">
                <StarIcon className="h-4 w-4 text-yellow-400" />
                <span>4.9</span>
              </div>

              <div className="mt-12 flex flex-col items-center text-center">
                <LandscapeIcon />
                <p className="mt-4 text-lg font-semibold text-gray-800 dark:text-gray-100">
                  autofill input
                </p>
                <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                  Rating star & user avatars would be here. Server list (US,
                  France, Germany, etc.) Connection speed gauge
                </p>
              </div>

              <button 
                aria-label="Security feature"
                className="absolute bottom-4 right-4 z-10 flex h-10 w-10 items-center justify-center rounded-full bg-blue-500 text-white shadow-lg hover:bg-blue-600"
              >
                <ShieldIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* New Features Grid Section - Added py-12 md:py-20 for spacing */}
      <section className="py-12 md:py-20">
        <div className="mx-auto max-w-5xl px-4">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {/* Card 1: Fast */}
            <div className="rounded-xl bg-purple-100 p-6 shadow-lg dark:bg-purple-800/30 relative overflow-hidden">
              {/* Subtle pattern - very simplified, can be replaced with an SVG or pseudo-element */}
              <div className="absolute inset-0 opacity-20 dark:opacity-10">
                <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="2" cy="2" r="1" fill="rgb(168,85,247)"/></pattern></defs><rect width="100%" height="100%" fill="url(#p1)"/></svg>
              </div>
              <div className="relative z-10">
                <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-purple-600 text-white">
                  {/* Fast Icon */}
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{t('home.features.fast')}</h3>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">{t('home.features.fastDesc')}</p>
              </div>
            </div>

            {/* Card 2: Accurate */}
            <div className="rounded-xl bg-orange-50 p-6 shadow-lg dark:bg-orange-800/30 relative overflow-hidden">
              <div className="absolute inset-0 opacity-20 dark:opacity-10">
                <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p2" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="2" cy="2" r="1" fill="rgb(249,115,22)"/></pattern></defs><rect width="100%" height="100%" fill="url(#p2)"/></svg>
              </div>
              <div className="relative z-10">
                <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500 text-white">
                  {/* Accurate Icon */}
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{t('home.features.accurate')}</h3>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">{t('home.features.accurateDesc')}</p>
              </div>
            </div>

            {/* Card 3: Flexible */}
            <div className="rounded-xl bg-blue-100 p-6 shadow-lg dark:bg-blue-800/30 relative overflow-hidden">
              <div className="absolute inset-0 opacity-20 dark:opacity-10">
                <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p3" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="2" cy="2" r="1" fill="rgb(59,130,246)"/></pattern></defs><rect width="100%" height="100%" fill="url(#p3)"/></svg>
              </div>
              <div className="relative z-10">
                <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500 text-white">
                  {/* Flexible Icon */}
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{t('home.features.flexible')}</h3>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">{t('home.features.flexibleDesc')}</p>
              </div>
            </div>

            {/* Card 4: Safe */}
            <div className="rounded-xl bg-red-50 p-6 shadow-lg dark:bg-red-800/30 relative overflow-hidden">
              <div className="absolute inset-0 opacity-20 dark:opacity-10">
                <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p4" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="2" cy="2" r="1" fill="rgb(239,68,68)"/></pattern></defs><rect width="100%" height="100%" fill="url(#p4)"/></svg>
              </div>
              <div className="relative z-10">
                <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-red-500 text-white">
                  {/* Safe Icon */}
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{t('home.features.safe')}</h3>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">{t('home.features.safeDesc')}</p>
              </div>
              {/* Faint arrow on the right for the last card - simplified */}
              <div className="absolute top-1/2 right-2 -translate-y-1/2 text-gray-300 dark:text-gray-600 opacity-50">
                <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5"><path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" /></svg>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-12 md:py-16 bg-white dark:bg-gray-800">
        <div className="mx-auto max-w-5xl px-4">
          <h2 className="text-2xl font-bold text-center text-gray-900 dark:text-white mb-12">
            {t('home.faq.title')}
          </h2>
          
          <div className="space-y-6">
            {(t('home.faq.questions', { returnObjects: true }) as any[]).map((faq: any, index: number) => (
              <div 
                key={index} 
                className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
              >
                <button
                  onClick={() => toggleFaq(index)}
                  className="flex justify-between items-center w-full px-6 py-4 text-left bg-gray-50 dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-950 transition-colors"
                >
                  <span className="font-medium text-gray-900 dark:text-white">{faq.question}</span>
                  <svg 
                    className={`w-5 h-5 text-gray-500 transform transition-transform ${expandedFaq === index ? 'rotate-180' : ''}`} 
                    fill="none" 
                    viewBox="0 0 24 24" 
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                
                {expandedFaq === index && (
                  <div className="px-6 py-4 bg-white dark:bg-gray-800">
                    <p className="text-gray-600 dark:text-gray-300">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage; 
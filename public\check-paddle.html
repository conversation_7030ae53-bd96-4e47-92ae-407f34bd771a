<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle 配置检查工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .config-item {
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .config-label {
            font-weight: bold;
            color: #495057;
        }
        .config-value {
            font-family: monospace;
            color: #6c757d;
            margin-top: 5px;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Paddle 配置检查工具</h1>
        
        <div id="result"></div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="checkPaddleConfig()">🔍 检查 Paddle 配置</button>
            <button onclick="testPaddleLoad()">📦 测试 Paddle.js 加载</button>
            <button onclick="testCheckout()">💳 测试支付页面</button>
        </div>
        
        <div id="logs" style="margin-top: 30px;"></div>
    </div>

    <script>
        // 获取环境变量 (模拟)
        const config = {
            PADDLE_CLIENT_TOKEN: 'test_459728092d35d36bc173c60e52d',
            PADDLE_VENDOR_ID: '32634',
            PADDLE_PRODUCT_YEARLY: 'pri_01jxa2azwznm06dqbptzagtm0z',
            PADDLE_ENVIRONMENT: 'sandbox'
        };

        // Paddle 服务状态
        let paddleServiceStatus = {
            loaded: false,
            initialized: false,
            error: null
        };

        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = `status ${type}`;
            logEntry.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function checkPaddleConfig() {
            const resultDiv = document.getElementById('result');
            let html = '<h2>📋 配置检查结果</h2>';
            let allValid = true;

            // 检查 Client Token
            const tokenPattern = /^(test|live)_[a-zA-Z0-9]{27}$/;
            if (!config.PADDLE_CLIENT_TOKEN) {
                html += '<div class="status error">❌ PADDLE_CLIENT_TOKEN 未设置</div>';
                allValid = false;
            } else if (!tokenPattern.test(config.PADDLE_CLIENT_TOKEN)) {
                html += '<div class="status error">❌ PADDLE_CLIENT_TOKEN 格式无效</div>';
                html += `<div class="config-item">
                    <div class="config-label">当前值:</div>
                    <div class="config-value">${config.PADDLE_CLIENT_TOKEN}</div>
                    <div style="margin-top: 10px; color: #6c757d;">
                        正确格式应为: test_xxxxxxxxxxxxxxxxxxxxxxxxx (27个字符)
                    </div>
                </div>`;
                allValid = false;
            } else {
                const environment = config.PADDLE_CLIENT_TOKEN.startsWith('test_') ? 'sandbox' : 'live';
                html += `<div class="status success">✅ PADDLE_CLIENT_TOKEN 格式正确 (${environment})</div>`;
                html += `<div class="config-item">
                    <div class="config-label">Token:</div>
                    <div class="config-value">${config.PADDLE_CLIENT_TOKEN.substring(0, 10)}...</div>
                </div>`;
            }

            // 检查 Price ID
            if (!config.PADDLE_PRODUCT_YEARLY) {
                html += '<div class="status error">❌ PADDLE_PRODUCT_YEARLY 未设置</div>';
                allValid = false;
            } else if (!config.PADDLE_PRODUCT_YEARLY.startsWith('pri_')) {
                html += '<div class="status warning">⚠️ PADDLE_PRODUCT_YEARLY 格式可能无效 (应以 pri_ 开头)</div>';
            } else {
                html += '<div class="status success">✅ PADDLE_PRODUCT_YEARLY 格式正确</div>';
                html += `<div class="config-item">
                    <div class="config-label">Price ID:</div>
                    <div class="config-value">${config.PADDLE_PRODUCT_YEARLY}</div>
                </div>`;
            }

            // 检查 Vendor ID
            if (!config.PADDLE_VENDOR_ID) {
                html += '<div class="status warning">⚠️ PADDLE_VENDOR_ID 未设置 (Paddle v2 主要使用 Client Token)</div>';
            } else {
                html += '<div class="status info">ℹ️ PADDLE_VENDOR_ID 已设置 (向后兼容)</div>';
                html += `<div class="config-item">
                    <div class="config-label">Vendor ID:</div>
                    <div class="config-value">${config.PADDLE_VENDOR_ID}</div>
                </div>`;
            }

            // 总结
            if (allValid) {
                html += '<div class="status success">🎉 所有必需配置都正确！</div>';
            } else {
                html += '<div class="status error">❌ 存在配置问题，请修复后重试</div>';
            }

            resultDiv.innerHTML = html;
            log('配置检查完成', allValid ? 'success' : 'error');
        }

        async function testPaddleLoad() {
            log('开始测试 Paddle.js 加载...', 'info');
            
            try {
                // 加载 Paddle.js
                const script = document.createElement('script');
                script.src = 'https://cdn.paddle.com/paddle/v2/paddle.js';
                script.async = true;
                
                script.onload = () => {
                    log('✅ Paddle.js 加载成功', 'success');
                    
                    // 测试初始化
                    if (window.Paddle && window.Paddle.Initialize) {
                        try {
                            window.Paddle.Initialize({
                                token: config.PADDLE_CLIENT_TOKEN
                            });
                            log('✅ Paddle 初始化成功', 'success');
                            
                            // 检查 Paddle 版本
                            if (window.Paddle.Status && window.Paddle.Status.libraryVersion) {
                                log(`📦 Paddle 版本: ${window.Paddle.Status.libraryVersion}`, 'info');
                            }
                        } catch (error) {
                            log(`❌ Paddle 初始化失败: ${error.message}`, 'error');
                        }
                    } else {
                        log('❌ Paddle.Initialize 方法不可用', 'error');
                    }
                };
                
                script.onerror = () => {
                    log('❌ Paddle.js 加载失败', 'error');
                };
                
                document.head.appendChild(script);
                
            } catch (error) {
                log(`❌ 测试过程中出错: ${error.message}`, 'error');
            }
        }

        function testCheckout() {
            log('开始测试 Paddle v2 支付页面...', 'info');

            if (!window.Paddle) {
                log('❌ Paddle.js 未加载，请先测试加载', 'error');
                return;
            }

            if (!window.Paddle.Checkout || !window.Paddle.Checkout.open) {
                log('❌ Paddle.Checkout.open 方法不可用', 'error');
                return;
            }

            try {
                // 按照 Paddle v2 官方文档格式构建参数
                const checkoutOptions = {
                    // 必需参数
                    items: [{
                        priceId: config.PADDLE_PRODUCT_YEARLY,
                        quantity: 1
                    }],

                    // 可选参数 (直接在根级别，不在 settings 对象中)
                    displayMode: 'overlay',
                    theme: 'light',
                    locale: 'en',
                    successUrl: window.location.origin + '/profile?subscription_success=true',
                    cancelUrl: window.location.origin + '/pricing?subscription_cancelled=true',

                    // 测试客户信息
                    customer: {
                        email: '<EMAIL>'
                    },

                    // 测试自定义数据
                    customData: {
                        testMode: true,
                        timestamp: Date.now()
                    }
                };

                log('📋 支付配置:', 'info');
                log(JSON.stringify(checkoutOptions, null, 2), 'info');

                window.Paddle.Checkout.open(checkoutOptions);
                log('✅ 支付页面打开成功', 'success');
                log('💡 如果看到支付页面，说明配置正确', 'info');

            } catch (error) {
                log(`❌ 支付页面测试失败: ${error.message}`, 'error');
                log('🔍 请检查:', 'info');
                log('1. Client Token 是否正确', 'info');
                log('2. Price ID 是否有效', 'info');
                log('3. Paddle 账号是否已验证', 'info');
                log('4. 产品和价格是否为 Active 状态', 'info');
            }
        }

        // 页面加载时自动检查配置
        window.addEventListener('load', function() {
            log('🔧 Paddle 配置检查工具已加载', 'info');
            checkPaddleConfig();
        });
    </script>
</body>
</html>

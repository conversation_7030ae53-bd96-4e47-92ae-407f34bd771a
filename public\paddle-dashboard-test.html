<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle Dashboard配置测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #efe; border-color: #cfc; }
        .info { background-color: #eef; border-color: #ccf; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button { padding: 15px 25px; margin: 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background: #005a87; }
        .log { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; }
        .config-verified { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .price-info { background: #f0f8ff; padding: 10px; border-left: 4px solid #007cba; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Paddle Dashboard配置验证</h1>
        
        <div class="section success">
            <h2>✅ Dashboard配置确认</h2>
            <div class="config-verified">
                <p><strong>Token:</strong> <code>test_27fb172b17e19acd4fbad97de75</code> ✅ Active</p>
                <p><strong>域名白名单:</strong> <code>https://localhost:5173/</code> ✅ 已配置</p>
                <p><strong>产品:</strong> "niandu" ✅ 存在</p>
            </div>
        </div>

        <div class="section info">
            <h2>📦 产品价格信息</h2>
            <div class="price-info">
                <p><strong>产品名称:</strong> niandu</p>
                <p><strong>价格:</strong> $1.50/month (30 day trial)</p>
                <p><strong>价格ID:</strong> <code>pri_01k1aenmy30gyzrq9wayftn5z4</code></p>
                <p><strong>产品ID:</strong> <code>pro_01k1aemxhbrkgxan0jty3yw8sf</code></p>
            </div>
        </div>

        <div class="section warning">
            <h2>🔍 测试目标</h2>
            <p>使用Dashboard中确认的配置信息，验证JWT错误是否解决</p>
        </div>

        <div class="section">
            <h2>🧪 Dashboard配置测试</h2>
            <button onclick="testDashboardConfig()">🚀 测试Dashboard配置</button>
            <button onclick="testWithProductId()">🔄 测试产品ID</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section success" id="success-result" style="display: none;">
            <h2>🎉 测试成功！</h2>
            <div id="success-info"></div>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        // 使用Dashboard中确认的配置
        const dashboardConfig = {
            token: 'test_27fb172b17e19acd4fbad97de75',  // Dashboard中Active的Token
            priceId: 'pri_01k1aenmy30gyzrq9wayftn5z4',   // Dashboard中显示的价格ID
            productId: 'pro_01k1aemxhbrkgxan0jty3yw8sf'  // Dashboard中显示的产品ID
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '5px';
            logEntry.style.padding = '5px';
            logEntry.style.borderRadius = '3px';
            
            if (type === 'success') {
                logEntry.style.backgroundColor = '#d4edda';
                logEntry.style.color = '#155724';
            } else if (type === 'error') {
                logEntry.style.backgroundColor = '#f8d7da';
                logEntry.style.color = '#721c24';
            } else {
                logEntry.style.backgroundColor = '#d1ecf1';
                logEntry.style.color = '#0c5460';
            }
            
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('success-result').style.display = 'none';
        }

        function showSuccess(config) {
            const infoDiv = document.getElementById('success-info');
            infoDiv.innerHTML = `
                <h3>✅ 有效配置找到！</h3>
                <p><strong>Token:</strong> ${config.token}</p>
                <p><strong>有效ID:</strong> ${config.workingId}</p>
                <p><strong>类型:</strong> ${config.type}</p>
                <p><strong>下一步:</strong> 更新.env.local配置</p>
            `;
            document.getElementById('success-result').style.display = 'block';
        }

        async function initializePaddle() {
            return new Promise((resolve) => {
                window.Paddle.Initialize({
                    token: dashboardConfig.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            log(`❌ Checkout错误: ${data.detail}`, 'error');
                            
                            if (data.detail && data.detail.includes('JWT')) {
                                log('🔍 仍然是JWT错误 - 可能需要重新生成Token', 'error');
                            }
                        } else if (data.type === 'checkout.loaded') {
                            log('✅ 支付页面加载成功！', 'success');
                        } else if (data.name === 'checkout.closed' && data.data) {
                            const item = data.data.items?.[0];
                            if (item?.price_id && item?.product?.id && item?.product?.name) {
                                log('🎉 产品数据完整！配置正确！', 'success');
                                log(`产品名称: "${item.product.name}"`, 'success');
                                log(`价格ID: ${item.price_id}`, 'success');
                                log(`产品ID: ${item.product.id}`, 'success');
                                
                                showSuccess({
                                    token: dashboardConfig.token,
                                    workingId: item.price_id,
                                    type: '价格ID',
                                    productName: item.product.name
                                });
                            } else {
                                log('❌ 产品数据仍然为空', 'error');
                                log(`price_id: ${item?.price_id || 'null'}`, 'error');
                                log(`product.id: ${item?.product?.id || 'null'}`, 'error');
                                log(`product.name: ${item?.product?.name || 'null'}`, 'error');
                            }
                        }
                    }
                });
                resolve();
            });
        }

        async function testDashboardConfig() {
            log('🎯 测试Dashboard确认配置...', 'info');
            log(`Token: ${dashboardConfig.token}`, 'info');
            log(`价格ID: ${dashboardConfig.priceId}`, 'info');
            
            try {
                await initializePaddle();
                
                const testConfig = {
                    items: [{
                        priceId: dashboardConfig.priceId,
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                log(`配置: ${JSON.stringify(testConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(testConfig);
                log('✅ Dashboard配置测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ Dashboard配置测试失败: ${error.message}`, 'error');
            }
        }

        async function testWithProductId() {
            log('🔄 测试使用产品ID而非价格ID...', 'info');
            log(`产品ID: ${dashboardConfig.productId}`, 'info');
            
            try {
                await initializePaddle();
                
                const testConfig = {
                    items: [{
                        priceId: dashboardConfig.productId,  // 尝试使用产品ID
                        quantity: 1
                    }],
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                log(`配置: ${JSON.stringify(testConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(testConfig);
                log('✅ 产品ID测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 产品ID测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示信息
        window.onload = function() {
            log('🎯 Dashboard配置验证工具已加载', 'info');
            log('✅ 使用Dashboard中确认的Active Token和产品信息', 'info');
            log('🔍 如果仍有JWT错误，说明需要重新生成Token', 'warning');
        };
    </script>
</body>
</html>

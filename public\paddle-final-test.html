<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle最终测试 - AutoInput产品</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { background-color: #fee; border-color: #fcc; }
        .success { background-color: #efe; border-color: #cfc; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .log { max-height: 400px; overflow-y: auto; }
        .product-card { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Paddle最终测试 - AutoInput产品</h1>
        
        <div class="section info">
            <h2>📋 确认的产品信息</h2>
            <div class="product-card">
                <h3>🔧 AutoInput 产品</h3>
                <p><strong>产品ID:</strong> pro_01jxa297zq715r2ekp3bs793px</p>
                <p><strong>描述:</strong> 自动输入的产品</p>
                
                <h4>💰 价格选项:</h4>
                <div style="margin-left: 20px;">
                    <p><strong>选项1:</strong> $9.99/year (30天试用) - <code>pri_01k0xttmf9wekbg957y66xawj</code> ⭐</p>
                    <p><strong>选项2:</strong> $6.66/year (30天试用) - <code>pri_01jxa2azwznm06dqbptzagtm0z</code></p>
                </div>
            </div>
        </div>

        <div class="section warning">
            <h2>⚠️ 可能的问题</h2>
            <p><strong>30天试用配置</strong>可能导致JWT生成问题。试用期产品在某些情况下需要特殊处理。</p>
        </div>

        <div class="section">
            <h2>🧪 最终测试</h2>
            <button onclick="testPrimaryPrice()">测试主要价格 ($9.99)</button>
            <button onclick="testSecondaryPrice()">测试备用价格 ($6.66)</button>
            <button onclick="testWithoutTrial()">测试无试用配置</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section success" id="success-section" style="display: none;">
            <h2>🎉 测试成功！</h2>
            <p>如果支付页面能正常打开，说明配置正确。您可以继续集成到主应用中。</p>
        </div>

        <div class="section error" id="error-section" style="display: none;">
            <h2>❌ 仍有问题</h2>
            <div id="error-solutions"></div>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'test_459728092d35d36bc173c60e52d',
            prices: {
                primary: 'pri_01k0xttmf9wekbg957y66xawj',    // $9.99/year
                secondary: 'pri_01jxa2azwznm06dqbptzagtm0z'   // $6.66/year
            }
        };

        let paddleInitialized = false;
        let testResults = {
            success: 0,
            failed: 0
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('success-section').style.display = 'none';
            document.getElementById('error-section').style.display = 'none';
            testResults = { success: 0, failed: 0 };
        }

        function initializePaddle() {
            if (paddleInitialized) return Promise.resolve();

            return new Promise((resolve, reject) => {
                try {
                    window.Paddle.Initialize({
                        token: config.token,
                        eventCallback: (data) => {
                            log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                            
                            if (data.type === 'checkout.error') {
                                testResults.failed++;
                                log(`❌ Checkout错误: ${data.detail}`, 'error');
                                
                                if (data.detail && data.detail.includes('Failed to retrieve JWT')) {
                                    showErrorSolutions();
                                }
                            } else if (data.type === 'checkout.loaded') {
                                testResults.success++;
                                log('✅ Checkout页面加载成功！', 'success');
                                showSuccessMessage();
                            } else if (data.name === 'checkout.completed') {
                                log('🎉 支付完成', 'success');
                            } else if (data.name === 'checkout.closed') {
                                log('📝 支付页面关闭', 'info');
                            }
                        }
                    });

                    paddleInitialized = true;
                    log('✅ Paddle SDK初始化成功', 'success');
                    resolve();
                    
                } catch (error) {
                    log(`❌ Paddle初始化失败: ${error.message}`, 'error');
                    reject(error);
                }
            });
        }

        async function testPrimaryPrice() {
            log('💳 测试主要价格: $9.99/year (30天试用)...', 'info');
            await testPrice(config.prices.primary, '$9.99/year');
        }

        async function testSecondaryPrice() {
            log('💳 测试备用价格: $6.66/year (30天试用)...', 'info');
            await testPrice(config.prices.secondary, '$6.66/year');
        }

        async function testWithoutTrial() {
            log('💳 测试无试用配置...', 'info');
            
            try {
                await initializePaddle();
                
                // 尝试不包含试用的配置
                window.Paddle.Checkout.open({
                    items: [{
                        priceId: config.prices.primary,
                        quantity: 1
                    }],
                    customer: {
                        email: '<EMAIL>'
                    },
                    settings: {
                        displayMode: 'overlay',
                        theme: 'light',
                        // 不设置试用相关参数
                    }
                });
                
                log('✅ 无试用配置测试请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 无试用配置测试失败: ${error.message}`, 'error');
            }
        }

        async function testPrice(priceId, description) {
            try {
                await initializePaddle();
                
                log(`🛍️ 使用价格ID: ${priceId}`, 'info');
                
                window.Paddle.Checkout.open({
                    items: [{
                        priceId: priceId,
                        quantity: 1
                    }],
                    customer: {
                        email: '<EMAIL>'
                    },
                    settings: {
                        displayMode: 'overlay',
                        theme: 'light',
                        locale: 'en'
                    }
                });
                
                log(`✅ ${description} 支付页面请求已发送`, 'success');
                log('💡 请观察是否出现支付弹窗', 'info');
                
            } catch (error) {
                testResults.failed++;
                log(`❌ ${description} 测试失败: ${error.message}`, 'error');
            }
        }

        function showSuccessMessage() {
            document.getElementById('success-section').style.display = 'block';
        }

        function showErrorSolutions() {
            const errorSection = document.getElementById('error-section');
            const solutionsDiv = document.getElementById('error-solutions');
            
            solutionsDiv.innerHTML = `
                <h3>🔧 可能的解决方案：</h3>
                <ol>
                    <li><strong>试用期配置问题：</strong> 在Paddle Dashboard中检查试用期设置</li>
                    <li><strong>产品状态：</strong> 确认产品和价格都是Active状态</li>
                    <li><strong>账户验证：</strong> 确认Paddle账户完全验证</li>
                    <li><strong>创建新产品：</strong> 尝试创建一个没有试用期的简单产品</li>
                    <li><strong>联系支持：</strong> 如果问题持续，联系Paddle技术支持</li>
                </ol>
                <p><strong>建议：</strong> 先在Paddle Dashboard中创建一个简单的一次性付费产品（不含试用）进行测试。</p>
            `;
            
            errorSection.style.display = 'block';
        }

        // 页面加载时初始化
        window.onload = function() {
            log('🎯 AutoInput产品Paddle测试工具已加载', 'info');
            log('💡 使用确认的产品ID进行测试', 'info');
            log('⚠️ 注意：30天试用配置可能是JWT问题的原因', 'warning');
        };
    </script>
</body>
</html>

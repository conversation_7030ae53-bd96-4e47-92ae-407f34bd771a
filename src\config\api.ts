/**
 * API配置文件
 * 统一管理API URL，确保所有请求使用相同的基础URL
 */

// 判断是否为开发环境
const isDev = import.meta.env.DEV;

// 从环境变量中获取 API URL 和 API Key
export const API_URL = import.meta.env.VITE_API_URL || 'https://logindev.utilityapp.site/auth';
export const API_KEY = import.meta.env.VITE_API_KEY || '';
export const USERAPI_URL = import.meta.env.VITE_USERAPI_URL || 'https://autoapi.utilityapp.site';

// API配置已加载

// 构建完整的API路径
export const buildApiUrl = (endpoint: string): string => {
  // 确保 endpoint 不以 / 开头以避免双斜杠
  const formattedEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;

  // 针对不同类型的 API 使用相同的基础 URL
  return `${API_URL}/${formattedEndpoint}`;
};

/**
 * 创建带有基本请求头的请求选项
 * @param options - 自定义请求选项
 * @returns 合并了基本请求头的完整请求选项
 */
export const createApiRequestOptions = (options: RequestInit = {}): RequestInit => {
  // 创建默认的头部
  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json'
  };

  // 如果有环境变量设置的 API_KEY，添加到请求头（仅向后兼容）
  if (API_KEY) {
    defaultHeaders['x-api-key'] = API_KEY;
  }

  // 合并用户提供的头部
  const headers = {
    ...defaultHeaders,
    ...(options.headers as Record<string, string> || {})
  };

  // 请求选项已创建

  // 设置默认的CORS选项
  const corsOptions = {
    mode: 'cors' as RequestMode,
    credentials: 'omit' as RequestCredentials,
  };

  // 返回合并后的选项
  return {
    ...corsOptions,
    ...options,
    headers,
  };
};
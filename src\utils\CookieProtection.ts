/**
 * Cookie保护工具
 * 防止浏览器插件访问敏感的Cookie数据
 */

import { log } from './logger';

export class CookieProtection {
  private static instance: CookieProtection;
  private originalCookieDescriptor: PropertyDescriptor | undefined;
  private protectedCookies: Set<string> = new Set();
  private isProtectionEnabled = false;
  private allowedDomains: Set<string> = new Set();
  private isExtensionContext = false;

  private constructor() {
    this.initializeProtection();
  }

  public static getInstance(): CookieProtection {
    if (!CookieProtection.instance) {
      CookieProtection.instance = new CookieProtection();
    }
    return CookieProtection.instance;
  }

  /**
   * 初始化Cookie保护
   */
  private initializeProtection(): void {
    try {
      // 保存原始的cookie描述符
      this.originalCookieDescriptor = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie');

      // 设置默认保护的Cookie名称（仅针对插件访问）
      this.setProtectedCookies([
        'session_token',
        'auth_token',
        'access_token',
        'refresh_token',
        'user_session',
        'login_token',
        'csrf_token',
        'xsrf_token'
      ]);

      // 设置允许访问Cookie的合法域名
      this.setAllowedDomains([
        'paddle.com',
        'buy.paddle.com',
        'checkout.paddle.com',
        'sandbox-checkout-service.paddle.com',
        'cdn.paddle.com',
        'js.paddle.com'
      ]);

      log.debug('[CookieProtection] Cookie保护已初始化');
    } catch (error) {
      log.error('[CookieProtection] 初始化失败:', error);
    }
  }

  /**
   * 启用Cookie保护
   */
  public enableProtection(): void {
    if (this.isProtectionEnabled) {
      return;
    }

    try {
      const self = this;
      
      // 重写document.cookie的getter和setter
      Object.defineProperty(document, 'cookie', {
        get: function() {
          // 获取原始cookies
          const originalCookies = self.originalCookieDescriptor?.get?.call(this) || '';

          // 检查访问来源
          if (self.shouldAllowCookieAccess()) {
            // 允许合法的第三方服务（如Paddle）访问所有cookies
            return originalCookies;
          }

          // 对于插件访问，过滤掉受保护的cookies
          const filteredCookies = self.filterProtectedCookies(originalCookies);

          // 记录访问尝试（仅开发环境）
          if (import.meta.env.DEV) {
            log.debug('[CookieProtection] Cookie访问被过滤（插件访问）');
          }

          return filteredCookies;
        },

        set: function(value: string) {
          // 检查访问来源
          if (self.shouldAllowCookieAccess()) {
            // 允许合法的第三方服务设置cookies
            self.originalCookieDescriptor?.set?.call(this, value);
            return;
          }

          // 检查是否尝试设置受保护的cookie（插件访问）
          const cookieName = self.extractCookieName(value);

          if (self.protectedCookies.has(cookieName)) {
            // 阻止插件设置受保护的cookie
            if (import.meta.env.DEV) {
              log.warn(`[CookieProtection] 阻止插件设置受保护的Cookie: ${cookieName}`);
            }
            return;
          }

          // 允许插件设置非保护的cookie
          self.originalCookieDescriptor?.set?.call(this, value);
        },

        configurable: true,
        enumerable: true
      });

      this.isProtectionEnabled = true;
      log.debug('[CookieProtection] Cookie保护已启用');
    } catch (error) {
      log.error('[CookieProtection] 启用保护失败:', error);
    }
  }

  /**
   * 禁用Cookie保护
   */
  public disableProtection(): void {
    if (!this.isProtectionEnabled || !this.originalCookieDescriptor) {
      return;
    }

    try {
      // 恢复原始的cookie描述符
      Object.defineProperty(document, 'cookie', this.originalCookieDescriptor);
      this.isProtectionEnabled = false;
      log.debug('[CookieProtection] Cookie保护已禁用');
    } catch (error) {
      log.error('[CookieProtection] 禁用保护失败:', error);
    }
  }

  /**
   * 设置受保护的Cookie名称列表
   */
  public setProtectedCookies(cookieNames: string[]): void {
    this.protectedCookies.clear();
    cookieNames.forEach(name => {
      this.protectedCookies.add(name.toLowerCase());
    });
    log.debug('[CookieProtection] 已设置受保护的Cookie:', Array.from(this.protectedCookies));
  }

  /**
   * 添加受保护的Cookie名称
   */
  public addProtectedCookie(cookieName: string): void {
    this.protectedCookies.add(cookieName.toLowerCase());
    log.debug(`[CookieProtection] 已添加受保护的Cookie: ${cookieName}`);
  }

  /**
   * 移除受保护的Cookie名称
   */
  public removeProtectedCookie(cookieName: string): void {
    this.protectedCookies.delete(cookieName.toLowerCase());
    log.debug(`[CookieProtection] 已移除受保护的Cookie: ${cookieName}`);
  }

  /**
   * 过滤掉受保护的cookies
   */
  private filterProtectedCookies(cookieString: string): string {
    if (!cookieString) {
      return '';
    }

    const cookies = cookieString.split(';');
    const filteredCookies = cookies.filter(cookie => {
      const cookieName = this.extractCookieName(cookie.trim());
      return !this.protectedCookies.has(cookieName);
    });

    return filteredCookies.join(';');
  }

  /**
   * 检查是否应该允许Cookie访问
   * 临时策略：默认允许所有访问，确保第三方服务正常工作
   */
  private shouldAllowCookieAccess(): boolean {
    // 临时解决方案：默认允许所有Cookie访问
    // 这样可以确保Paddle等第三方服务正常工作
    // 后续可以根据需要调整为更精确的检测
    return true;

    /* 原有的检测逻辑保留备用
    try {
      // 获取调用栈信息
      const stack = new Error().stack || '';

      // 检查是否来自允许的域名（如Paddle）
      for (const domain of this.allowedDomains) {
        if (stack.includes(domain)) {
          return true;
        }
      }

      // 检查是否来自iframe（通常是第三方服务）
      if (window !== window.top) {
        try {
          const parentOrigin = document.referrer;
          for (const domain of this.allowedDomains) {
            if (parentOrigin.includes(domain)) {
              return true;
            }
          }
        } catch (error) {
          // 跨域访问限制，可能是合法的第三方服务
        }
      }

      // 检查当前页面URL是否包含允许的域名
      const currentUrl = window.location.href;
      for (const domain of this.allowedDomains) {
        if (currentUrl.includes(domain)) {
          return true;
        }
      }

      // 检查是否有特定的标识表明这是合法的第三方服务调用
      if (this.isLegitimateThirdPartyAccess()) {
        return true;
      }

      return false;
    } catch (error) {
      // 出错时采用保守策略，允许访问以避免破坏功能
      return true;
    }
    */
  }

  /**
   * 检查是否是合法的第三方服务访问
   */
  private isLegitimateThirdPartyAccess(): boolean {
    try {
      // 检查是否有Paddle相关的全局变量
      if (typeof (window as any).Paddle !== 'undefined') {
        return true;
      }

      // 检查是否有其他支付服务的标识
      if (typeof (window as any).PayPal !== 'undefined' ||
          typeof (window as any).Stripe !== 'undefined') {
        return true;
      }

      // 检查DOM中是否有第三方服务的脚本
      const scripts = document.querySelectorAll('script[src]');
      for (const script of scripts) {
        const src = (script as HTMLScriptElement).src;
        for (const domain of this.allowedDomains) {
          if (src.includes(domain)) {
            return true;
          }
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 设置允许访问Cookie的域名
   */
  public setAllowedDomains(domains: string[]): void {
    this.allowedDomains.clear();
    domains.forEach(domain => {
      this.allowedDomains.add(domain.toLowerCase());
    });
    log.debug('[CookieProtection] 已设置允许的域名:', Array.from(this.allowedDomains));
  }

  /**
   * 添加允许的域名
   */
  public addAllowedDomain(domain: string): void {
    this.allowedDomains.add(domain.toLowerCase());
    log.debug(`[CookieProtection] 已添加允许的域名: ${domain}`);
  }

  /**
   * 从cookie字符串中提取cookie名称
   */
  private extractCookieName(cookieString: string): string {
    const equalIndex = cookieString.indexOf('=');
    if (equalIndex === -1) {
      return cookieString.trim().toLowerCase();
    }
    return cookieString.substring(0, equalIndex).trim().toLowerCase();
  }

  /**
   * 获取受保护的Cookie列表
   */
  public getProtectedCookies(): string[] {
    return Array.from(this.protectedCookies);
  }

  /**
   * 检查保护状态
   */
  public isProtectionActive(): boolean {
    return this.isProtectionEnabled;
  }

  /**
   * 获取保护状态摘要
   */
  public getProtectionSummary(): {
    enabled: boolean;
    protectedCookies: string[];
    totalProtected: number;
    allowedDomains: string[];
    totalAllowedDomains: number;
  } {
    return {
      enabled: this.isProtectionEnabled,
      protectedCookies: this.getProtectedCookies(),
      totalProtected: this.protectedCookies.size,
      allowedDomains: Array.from(this.allowedDomains),
      totalAllowedDomains: this.allowedDomains.size
    };
  }

  /**
   * 测试Cookie保护
   */
  public testProtection(): void {
    console.group('🧪 Cookie保护测试');

    // 测试访问来源检测
    log.debug('测试访问来源检测...');
    const shouldAllow = this.shouldAllowCookieAccess();
    log.debug('当前访问是否被允许:', shouldAllow ? '✅ 允许' : '❌ 受限');

    // 测试设置受保护的cookie
    log.debug('测试设置受保护的Cookie...');
    const originalCookies = document.cookie;
    document.cookie = 'auth_token=test_value';

    // 测试读取cookies
    log.debug('测试读取Cookies...');
    const cookies = document.cookie;
    log.debug('可访问的Cookies:', cookies);

    // 检查是否包含受保护的cookie
    const hasProtectedCookie = cookies.includes('auth_token');
    log.debug('是否包含受保护的Cookie:', hasProtectedCookie ? '❌ 保护失败或访问被允许' : '✅ 保护成功');

    // 显示保护摘要
    log.debug('保护摘要:', this.getProtectionSummary());

    console.groupEnd();
  }
}

// 导出单例实例
export const cookieProtection = CookieProtection.getInstance();

// 在开发环境中暴露到全局对象
if (import.meta.env.DEV) {
  (window as any).cookieProtection = cookieProtection;
}

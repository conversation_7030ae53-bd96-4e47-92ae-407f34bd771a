<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle无CSP限制测试</title>
    <!-- 完全没有CSP限制的测试页面 -->
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #efe; border-color: #cfc; }
        .info { background-color: #eef; border-color: #ccf; }
        button { padding: 15px 25px; margin: 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .log { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; }
        .config-info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Paddle无CSP限制测试</h1>
        
        <div class="section success">
            <h2>✅ 测试环境</h2>
            <p><strong>此页面完全没有CSP限制，用于验证Paddle配置是否正确。</strong></p>
        </div>

        <div class="section info">
            <h2>📋 当前配置</h2>
            <div class="config-info">
                <p><strong>环境:</strong> Sandbox</p>
                <p><strong>Token:</strong> <code>test_27fb172b17e19acd4fbad97de75</code></p>
                <p><strong>产品ID:</strong> <code>pri_01k1aenmy30gyzrq9wayftn5z4</code></p>
                <p><strong>域名白名单:</strong> ✅ 已在Paddle Dashboard中配置</p>
            </div>
        </div>

        <div class="section">
            <h2>🧪 支付测试</h2>
            <button onclick="testPayment()" id="payBtn">🚀 测试支付功能</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 测试日志</h2>
            <div id="test-log" class="log"></div>
        </div>

        <div class="section success" id="success-section" style="display: none;">
            <h2>🎉 测试成功！</h2>
            <p>如果支付页面能正常打开，说明Paddle配置正确，问题在于主应用的CSP配置。</p>
        </div>
    </div>

    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    <script>
        const config = {
            token: 'test_27fb172b17e19acd4fbad97de75',
            productId: 'pri_01k1aenmy30gyzrq9wayftn5z4'
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '5px';
            logEntry.style.padding = '5px';
            logEntry.style.borderRadius = '3px';
            
            if (type === 'success') {
                logEntry.style.backgroundColor = '#d4edda';
                logEntry.style.color = '#155724';
            } else if (type === 'error') {
                logEntry.style.backgroundColor = '#f8d7da';
                logEntry.style.color = '#721c24';
            } else {
                logEntry.style.backgroundColor = '#d1ecf1';
                logEntry.style.color = '#0c5460';
            }
            
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('success-section').style.display = 'none';
        }

        function showSuccess() {
            document.getElementById('success-section').style.display = 'block';
        }

        async function testPayment() {
            log('🚀 开始测试支付功能...', 'info');
            
            try {
                // 初始化Paddle
                log('🔧 初始化Paddle...', 'info');
                
                window.Paddle.Initialize({
                    token: config.token,
                    eventCallback: (data) => {
                        log(`📊 Paddle事件: ${JSON.stringify(data)}`, 'info');
                        
                        if (data.type === 'checkout.error') {
                            log(`❌ Checkout错误: ${data.detail}`, 'error');
                        } else if (data.type === 'checkout.loaded') {
                            log('✅ 支付页面加载成功！', 'success');
                            showSuccess();
                        } else if (data.name === 'checkout.closed') {
                            const item = data.data?.items?.[0];
                            if (item?.price_id && item?.product?.id) {
                                log('🎉 产品数据获取成功！', 'success');
                                log(`产品ID: ${item.product.id}, 价格ID: ${item.price_id}`, 'success');
                            } else {
                                log('⚠️ 产品数据为空，但支付页面能打开', 'info');
                            }
                        }
                    }
                });
                
                log('✅ Paddle初始化成功', 'success');
                
                // 打开支付页面
                log('💳 打开支付页面...', 'info');
                
                const checkoutConfig = {
                    items: [{
                        priceId: config.productId,
                        quantity: 1
                    }],
                    customer: {
                        email: "<EMAIL>"
                    },
                    customData: {
                        userId: "test123",
                        plan: "test"
                    },
                    settings: {
                        displayMode: "overlay",
                        theme: "light",
                        locale: "en"
                    }
                };
                
                log(`配置: ${JSON.stringify(checkoutConfig, null, 2)}`, 'info');
                
                window.Paddle.Checkout.open(checkoutConfig);
                log('✅ 支付请求已发送', 'success');
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 监听CSP错误
        window.addEventListener('securitypolicyviolation', function(e) {
            log(`🚨 CSP违规: ${e.violatedDirective} - ${e.blockedURI}`, 'error');
        });

        // 页面加载时显示信息
        window.onload = function() {
            log('🚀 无CSP限制测试页面已加载', 'info');
            log('💡 此页面没有CSP限制，用于验证Paddle配置', 'info');
            log('🎯 如果这里能正常工作，说明问题在于主应用的CSP配置', 'info');
        };
    </script>
</body>
</html>

# 浏览器插件访问策略说明

## 概述

系统已更新为**开放式访问策略**：
- ✅ **允许所有浏览器插件**访问网站的缓存数据（localStorage）
- 🛡️ **保护Cookie数据**，防止插件访问敏感的Cookie信息
- 📊 **监控插件活动**，记录所有插件与网站的交互

## 访问策略详解

### 1. 缓存数据访问（localStorage）

**策略**：完全开放
- 所有浏览器插件都可以访问localStorage数据
- 包括用户认证信息、订阅状态、应用设置等
- 不再进行插件ID验证或限制

**原因**：
- 提高插件兼容性
- 简化开发和维护
- 支持更多第三方插件

### 2. Cookie数据保护

**策略**：严格保护
- 插件无法读取敏感Cookie
- 插件无法设置受保护的Cookie
- 自动过滤敏感Cookie信息

**受保护的Cookie类型**：
```javascript
const protectedCookies = [
  'session_token',
  'auth_token', 
  'access_token',
  'refresh_token',
  'user_session',
  'login_token',
  'csrf_token',
  'xsrf_token'
];
```

## 技术实现

### 1. 移除插件ID验证

```typescript
// 旧版本：需要验证插件ID
if (!this.validateExtensionId(extensionId)) {
  return Promise.resolve(null);
}

// 新版本：允许所有插件
if (!this.hasExtensionAvailable()) {
  return Promise.resolve(null);
}
```

### 2. Cookie保护机制

```typescript
// 重写document.cookie的getter和setter
Object.defineProperty(document, 'cookie', {
  get: function() {
    const originalCookies = getOriginalCookies();
    return filterProtectedCookies(originalCookies);
  },
  set: function(value: string) {
    const cookieName = extractCookieName(value);
    if (isProtectedCookie(cookieName)) {
      return; // 阻止设置
    }
    setOriginalCookie(value);
  }
});
```

## 使用方法

### 1. 开发环境测试

启动开发服务器：
```bash
npm run dev
```

在浏览器控制台中测试：
```javascript
// 查看插件状态
showExtensionStatus();

// 测试Cookie保护
testCookieProtection();

// 查看Cookie保护状态
showCookieProtection();

// 查看插件活动
showExtensionActivities();
```

### 2. 验证访问权限

#### 测试localStorage访问（应该成功）：
```javascript
// 插件应该能够访问这些数据
localStorage.setItem('test_data', 'plugin_can_access');
console.log(localStorage.getItem('access_token')); // 应该能读取
```

#### 测试Cookie访问（应该被阻止）：
```javascript
// 插件应该无法访问受保护的Cookie
document.cookie = 'auth_token=test_value'; // 应该被阻止
console.log(document.cookie); // 不应该包含受保护的Cookie
```

## 监控和调试

### 1. 插件活动监控

系统会自动记录：
- 插件检测事件
- 存储访问请求
- Cookie访问尝试
- 保护机制触发

### 2. 控制台命令

```javascript
// 插件相关
showExtensionActivities()    // 查看插件活动
showExtensionStatus()        // 查看插件状态
testExtensionCommunication() // 测试插件通信

// Cookie保护相关
showCookieProtection()       // 查看保护状态
testCookieProtection()       // 测试保护机制
enableCookieProtection()     // 启用保护
disableCookieProtection()    // 禁用保护

// 数据访问相关
showLocalStorage()           // 查看本地存储
exportActivities()           // 导出活动记录
```

## 安全考虑

### 1. 数据分类

**可访问数据**（localStorage）：
- 用户偏好设置
- 应用配置信息
- 非敏感的用户数据
- 订阅状态信息

**受保护数据**（Cookie）：
- 认证令牌
- 会话标识
- CSRF令牌
- 安全相关Cookie

### 2. 风险评估

**低风险**：
- localStorage数据泄露影响相对较小
- 数据可以通过重新登录恢复
- 不包含密码等敏感信息

**高风险**：
- Cookie数据包含认证信息
- 泄露可能导致账户被劫持
- 需要严格保护

## 配置选项

### 1. 环境变量

```bash
# 允许所有插件访问
VITE_ALLOW_ALL_EXTENSIONS=true

# 启用Cookie保护
VITE_ENABLE_COOKIE_PROTECTION=true
```

### 2. 自定义保护Cookie

```javascript
// 添加自定义受保护的Cookie
cookieProtection.addProtectedCookie('my_sensitive_cookie');

// 移除保护
cookieProtection.removeProtectedCookie('some_cookie');

// 查看受保护列表
cookieProtection.getProtectedCookies();
```

## 故障排除

### 问题1：插件无法访问localStorage
**检查**：
1. 插件是否正确注入
2. StorageBridge是否初始化
3. 浏览器控制台是否有错误

### 问题2：Cookie保护不生效
**检查**：
1. Cookie保护是否启用
2. 浏览器是否支持Object.defineProperty
3. 是否有其他脚本覆盖了document.cookie

### 问题3：监控数据不显示
**检查**：
1. 是否在开发环境
2. 监控器是否正确初始化
3. 控制台命令是否可用

## 总结

新的访问策略在保持安全性的同时提供了更好的兼容性：
- **开放localStorage访问**：支持更多插件功能
- **保护Cookie数据**：维护账户安全
- **完整监控机制**：便于调试和问题排查

这种平衡的方案既满足了功能需求，又保证了关键数据的安全性。

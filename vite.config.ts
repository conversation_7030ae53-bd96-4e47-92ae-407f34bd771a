import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    tsconfigPaths()
  ],
  // 针对Cloudflare Pages优化的设置
  build: {
    // 使用esbuild而不是terser进行压缩
    minify: 'esbuild',
    // 优化大型依赖包
    chunkSizeWarningLimit: 1000,
    outDir: 'dist',
    sourcemap: true,
  },
  // 确保路径解析正确
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  // 定义环境变量默认值（仅在没有.env文件时使用）
  define: {
    // 注意：不再硬编码API URL，必须从环境变量中获取
    // 注意：API_KEY应该从环境变量中获取，这里不硬编码
  },
  // 开发服务器配置
  server: {
    cors: true,
    // 添加开发服务器代理配置，解决CORS问题
    proxy: {
      '/api': {
        target: process.env.VITE_USERAPI_URL || 'https://autoapi.utilityapp.site',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      // Paddle代理 - 解决CSP问题
      '/paddle-proxy': {
        target: 'https://buy.paddle.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/paddle-proxy/, ''),
        configure: (proxy, options) => {
          proxy.on('proxyRes', (proxyRes, req, res) => {
            // 移除CSP和X-Frame-Options头
            delete proxyRes.headers['content-security-policy'];
            delete proxyRes.headers['x-frame-options'];
            delete proxyRes.headers['frame-ancestors'];
          });
        }
      }
    },
    port: 5173,
    // 开发环境完全移除CSP限制，让Paddle Dashboard的配置生效
    headers: {
      'Content-Security-Policy': "frame-ancestors 'self' http://localhost:5173 https://buy.paddle.com;"
    }
  }
});
